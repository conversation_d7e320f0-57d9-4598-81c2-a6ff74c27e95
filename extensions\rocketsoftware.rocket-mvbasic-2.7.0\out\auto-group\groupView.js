"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GroupView = void 0;
const vscode = require("vscode");
const groupTree_1 = require("./groupTree");
const extConfig = require("../config/extConfig");
class GroupView {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.groupTree = undefined;
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!this.groupTree) {
            this.groupTree = this.createTree();
            if (!this.groupTree.build()) {
                return [];
            }
        }
        return this.groupTree.getChildren(element);
    }
    refresh() {
        this.groupTree = this.createTree();
        if (!this.groupTree.build()) {
            this.groupTree = undefined;
        }
        this._onDidChangeTreeData.fire(null);
    }
    createTree() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length == 0) {
            return new groupTree_1.GroupTree("", []);
        }
        const wsfs = new Array();
        workspaceFolders.forEach(folder => wsfs.push(folder.uri.fsPath));
        return new groupTree_1.GroupTree(extConfig.groupView, wsfs);
    }
}
exports.GroupView = GroupView;
//# sourceMappingURL=groupView.js.map