"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBasicEditor = getBasicEditor;
exports.getWorkspacePath = getWorkspacePath;
exports.closeTab = closeTab;
const vscode = require("vscode");
const env_1 = require("./env");
const path = require("path");
const extConfig = require("../config/extConfig");
//This function will be used for vscode.window.activeTextEditor == undifined or get other editor(such as output contole)
function getBasicEditor() {
    return vscode.window.visibleTextEditors
        .find(a => a.document.languageId === env_1.LANGUAGE_ID);
}
function getWorkspacePath() {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length != 0) {
        const workspace = workspaceFolders[0].uri.fsPath;
        if (extConfig.rmvExists(workspaceFolders[0])) {
            return path.join(workspace, extConfig.offlineCache);
        }
        else {
            return path.join(workspace, extConfig.onlineCache);
        }
    }
    return undefined;
}
function closeTab(tabLabel) {
    const tabGroups = vscode.window.tabGroups;
    for (const tabGroup of tabGroups.all) {
        for (const tab of tabGroup.tabs) {
            if (tab.label === tabLabel) {
                tabGroups.close(tab);
            }
        }
    }
}
//# sourceMappingURL=editor.js.map