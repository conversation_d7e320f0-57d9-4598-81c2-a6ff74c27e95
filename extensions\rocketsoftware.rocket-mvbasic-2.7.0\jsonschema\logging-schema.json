{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "properties": {"logging": {"type": "object", "properties": {"level": {"type": "string", "description": "Log print level, output greater than this level", "oneOf": [{"const": "off", "description": "Close log output."}, {"const": "fatal", "description": "Only output level fatal log."}, {"const": "error", "description": "Output level fatal&error log."}, {"const": "warn", "description": "Output level fatal&error&warn log."}, {"const": "info", "description": "Output level fatal&error&warn&info log."}, {"const": "debug", "description": "Output level fatal&error&warn&info&debug log."}, {"const": "trace", "description": "Output level fatal&error&warn&info&debug&trace log."}]}, "directory": {"type": "string", "description": "Log file output path, without file name, for example: \"C:/basic/extension/log.\""}}}}}