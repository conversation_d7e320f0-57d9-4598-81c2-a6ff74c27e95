"use strict";Object.defineProperty(exports,"__esModule",{value:!0});exports.activate=G;exports.deactivate=H;var s=require("vscode"),h=require("@vscode/node-speech"),g=require("path"),x=require("crypto"),P=Buffer.from("3208a808a73d00a3a271f5c4118e74cf","hex"),O=Buffer.from("e299dc0147c19a63930e13bc","hex"),I=Buffer.from("7d3184322138a889c51b21cf4a355182baedca3ce7947416457ef8f68262528c2049cb49697094181b4baf76a7409db58f09b38f52f35eccd66a67619309cf291e55666de380734b3404a84b7afa32580daea53e97cd6fd91488e4982bcdde589b83470fd08df7f39ad07b679e95126696d0ae78cb8b55bf214a4cd90e1053a74d449978547d293cdc6f468aaedfec439112b9f12de6b98b8c8e356aa9d3dfaaca95ac8df3dde18a6c35262bb0f26fa4b8c06fef07b3fb2a60ab4b578331cd305b9f9f87741cf52a6f0d58648269d6dbb5c6fe47552d5c38fdec9125ab33b2d17e056416e63addce1c1e13e131632325e71560a77ae1fba16d966e96cf69219d049b35881cda9a57f5a4ae3b713aad20150a20601a1f75ec9df51c97c59b57a436d56badbc8cc2afdbecd4c389fd701cf939f9aa86c163e92e55eead143bed6f17a3a23a204c86ec0133ee88fa9d3caf47053180d3b13f8c2aec0f8c52f413a6997cf0b5800c2f9e387ee29675ff878e9b403e8de441ca4509746c0b29891ac17aa99f4408a02b0d7a3026aeeb5252aaf2b175cbb4ab88e4379efd504ad15192f0c9b12c5001f0936ac17634a8387869c8371ae8724387f3d49d547ea168bc9b586e4e04b2951353172ec33d613a33b912229ecff0da980ee7f4bc57aa3eca013565c443b7d8a2f6714a2fc240c74c1ca9f8b3c1530778a978a898c32b71abc075778046a8e977e4249ae4e31b46058fe25de0a1054405429e7f08239b4f205ae71864239b9c58585926f7b243eb698d42393991714e4dd258b8672c98265c4bff6c80ef3d8902ec247c05ba9e95bd65daab30295bc33b8aa706075558349ebc1ccab22b64cb42f14789a8a1ccc5557cf9dadf8d4d39ad0ca683f4e5e4df3b4aee63d2e8fd295e441a1ef65128aeca83531f10e52ef6281e99d623568351fb59bc0097d72b104f40df7951838537c3b7681c70a6b93b7b4f350c2d3037889cf5c95ad3ae9c11d2ed22c0084267345273e3","hex"),_="You may only use the C/C++ Extension for Visual Studio Code and C# Extension for Visual Studio Code with Visual Studio Code, Visual Studio or Xamarin Studio software to help you develop and test your applications. The software is licensed, not sold. This agreement only gives you some rights to use the software. Microsoft reserves all other rights. You may not work around any technical limitations in the software; reverse engineer, decompile or disassemble the software remove, minimize, block or modify any notices of Microsoft or its suppliers in the software share, publish, rent, or lease the software, or provide the software as a stand-alone hosted as solution for others to use.";function M(a,e,l,u){let d=(0,x.createHash)("sha256");d.update(a);let o=d.digest(),i=(0,x.createDecipheriv)("aes-256-gcm",o,l);return i.setAuthTag(e),Buffer.concat([i.update(u),i.final()]).toString()}var m="2",k=M(_,P,O,I),N=process.env.VSCODE_SPEECH_LOGS_PATH,v=new Map,S={version:m,modelName:"Microsoft Speech Recognizer en-US FP Model V9",modelPath:(0,g.join)(__dirname,"..","assets","stt"),locale:"en-US",phrases:["slash","Slash","At workspace","workspace","At code","code","fix","search","explain"]};v.set(S.locale,S);function A({modelName:a,modelPath:e,phrases:l},u){return h.createTranscriber({modelName:a,modelPath:e,modelKey:k,logsPath:N,phrases:l},(d,o)=>u(d,o))}var $=class{transcriber;constructor(e){this.transcriber=this.create(e)}create(e){let l=new s.EventEmitter,u=new s.EventEmitter,d=A(e,(o,i)=>{o?u.fire(o):l.fire(i)});return{onResult:l.event,onError:u.event,start:()=>d.start(),stop:()=>d.stop(),dispose:()=>{d.dispose(),l.dispose(),u.dispose()}}}start(e,l){let u=this.transcriber.onResult(o=>l(void 0,o)),d=this.transcriber.onError(o=>l(o,{status:h.TranscriptionStatusCode.ERROR,data:o.message}));e.onabort=()=>{this.transcriber.stop(),u.dispose(),d.dispose()},this.transcriber.start()}};function R(){for(let a of s.extensions.all)if(a.packageJSON.contributes?.vscodeSpeechModels)for(let e of a.packageJSON.contributes.vscodeSpeechModels)v.set(e.locale,{version:e.version,modelName:e.modelName,modelPath:(0,g.join)(a.extensionPath,e.modelPath),locale:e.locale})}var T=new Map,E={version:m,modelName:"Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)",modelPath:(0,g.join)(__dirname,"..","assets","tts"),locale:"en-US"};T.set(E.locale,E);function K({modelName:a,modelPath:e},l){return h.createSynthesizer({modelName:a,modelPath:e,modelKey:k,logsPath:N},(u,d)=>l(u,d))}var y=class{model;synthesizer=void 0;constructor(e){this.model=e}synthesize(e,l,u){this.synthesizer&&this.synthesizer.dispose();let d=this.synthesizer=this.create(this.model),o=d.onResult(t=>u(void 0,t)),i=d.onError(t=>u(t,{status:h.SynthesizerStatusCode.ERROR,data:t.message}));l.onabort=()=>{d.stop(),o.dispose(),i.dispose()},d.synthesize(e)}create(e){let l=new s.EventEmitter,u=new s.EventEmitter,d=K(e,(o,i)=>{o?u.fire(o):l.fire(i)});return{onResult:l.event,onError:u.event,synthesize:o=>d.synthesize(o),stop:()=>d.stop(),dispose:()=>{d.dispose(),l.dispose(),u.dispose()}}}};function w(){for(let a of s.extensions.all)if(a.packageJSON.contributes?.vscodeSynthesizerModels)for(let e of a.packageJSON.contributes.vscodeSynthesizerModels)T.set(e.locale,{version:e.version,modelName:e.modelName,modelPath:(0,g.join)(a.extensionPath,e.modelPath),locale:e.locale})}var q=(0,g.join)(__dirname,"..","assets","keyword","heycode.table");function L(a){return h.recognize({modelPath:q,signal:a})}function z(a,e){s.commands.executeCommand("workbench.extensions.installExtension",U(a),{justification:e?`Support for '${D[a]}' in speech recognition requires this extension.`:`Support for '${D[a]}' in speech synthesis requires this extension.`,enable:!0})}var D={"da-DK":"Danish (Denmark)","de-DE":"German (Germany)","en-AU":"English (Australia)","en-CA":"English (Canada)","en-GB":"English (United Kingdom)","en-IE":"English (Ireland)","en-IN":"English (India)","en-NZ":"English (New Zealand)",[S.locale]:"English (United States)","es-ES":"Spanish (Spain)","es-MX":"Spanish (Mexico)","fr-CA":"French (Canada)","fr-FR":"French (France)","hi-IN":"Hindi (India)","it-IT":"Italian (Italy)","ja-JP":"Japanese (Japan)","ko-KR":"Korean (South Korea)","nl-NL":"Dutch (Netherlands)","pt-PT":"Portuguese (Portugal)","pt-BR":"Portuguese (Brazil)","ru-RU":"Russian (Russia)","sv-SE":"Swedish (Sweden)","tr-TR":"Turkish (Turkey)","zh-CN":"Chinese (Simplified, China)","zh-HK":"Chinese (Traditional, Hong Kong)","zh-TW":"Chinese (Traditional, Taiwan)"};function U(a){return`ms-vscode.vscode-speech-language-pack-${a.toLowerCase()}`}function V(){return!!["Visual Studio Code","Visual Studio Code - Insiders","Visual Studio Code - Exploration","Code - OSS","Code - OSS Dev"].includes(s.env.appName)}function G(a){let e=s.window.createOutputChannel("VS Code Speech",{log:!0});if(a.subscriptions.push(e),!V()){e.error(`[vscode-speech] This extension is not supported in ${s.env.appName}.`);return}a.subscriptions.push(s.commands.registerCommand("vscode-speech.openSettings",()=>{s.commands.executeCommand("workbench.action.openSettings","voice")})),R(),w(),a.subscriptions.push(s.extensions.onDidChange(()=>{R(),w()}));let l=0,u=new Map,d=new Map;a.subscriptions.push(s.speech.registerSpeechProvider(a.extension.id,{provideSpeechToTextSession:(o,i)=>{let t=new s.EventEmitter;if(o.isCancellationRequested)return{onDidChange:t.event};if(i?.language&&!v.has(i.language))return z(i.language,!0),setTimeout(()=>t.fire({status:s.SpeechToTextStatus.Stopped})),{onDidChange:t.event};let r=l++,n=v.get(i?.language??S.locale);(!n||n.version!==m)&&(n=S),e.trace(`[vscode-speech-${r}] starting speech-to-text session (language: ${n.locale}, model: ${n.modelName}, path: ${n.modelPath})`);let b=new AbortController;o.onCancellationRequested(()=>{e.trace(`[vscode-speech-${r}] aborting speech-to-text session`),b.abort(),t.dispose()});let p=u.get(n.locale);return p||(p=new $(n),u.set(n.locale,p)),p.start(b.signal,(f,c)=>{switch(f&&(e.error(`[vscode-speech-${r}] speech-to-text error: ${f.message}`),t.fire({status:s.SpeechToTextStatus.Error,text:f.message})),c.status){case h.TranscriptionStatusCode.STARTED:e.trace(`[vscode-speech-${r}] started speech-to-text session${c.data?`: ${c.data}`:""}`),t.fire({status:s.SpeechToTextStatus.Started});break;case h.TranscriptionStatusCode.RECOGNIZING:e.trace(`[vscode-speech-${r}] recognizing: ${c.data}`),t.fire({status:s.SpeechToTextStatus.Recognizing,text:c.data});break;case h.TranscriptionStatusCode.RECOGNIZED:e.trace(`[vscode-speech-${r}] recognized: ${c.data}`),t.fire({status:s.SpeechToTextStatus.Recognized,text:c.data});break;case h.TranscriptionStatusCode.STOPPED:e.trace(`[vscode-speech-${r}] stopped speech-to-text session${c.data?`: ${c.data}`:""}`),t.fire({status:s.SpeechToTextStatus.Stopped});break;case h.TranscriptionStatusCode.ERROR:e.error(`[vscode-speech-${r}] speech-to-text error: ${c.data}`),t.fire({status:s.SpeechToTextStatus.Error,text:c.data});break;case h.TranscriptionStatusCode.SPEECH_START_DETECTED:e.trace(`[vscode-speech-${r}] speech start detected${c.data?`: ${c.data}`:""}`);break;case h.TranscriptionStatusCode.SPEECH_END_DETECTED:e.trace(`[vscode-speech-${r}] speech end detected${c.data?`: ${c.data}`:""}`);break;case h.TranscriptionStatusCode.NOT_RECOGNIZED:e.trace(`[vscode-speech-${r}] not recognized${c.data?`: ${c.data}`:""}`);break;case h.TranscriptionStatusCode.INITIAL_SILENCE_TIMEOUT:e.trace(`[vscode-speech-${r}] initial silence timeout${c.data?`: ${c.data}`:""}`);break;case h.TranscriptionStatusCode.END_SILENCE_TIMEOUT:e.trace(`[vscode-speech-${r}] end silence timeout${c.data?`: ${c.data}`:""}`);break;case h.TranscriptionStatusCode.DISPOSED:e.trace(`[vscode-speech-${r}] disposed speech-to-text session${c.data?`: ${c.data}`:""}`);break}}),{onDidChange:t.event}},provideTextToSpeechSession(o,i){let t=new s.EventEmitter;if(o.isCancellationRequested)return{onDidChange:t.event,synthesize:()=>{}};if(i?.language&&!T.has(i.language))return z(i.language,!1),setTimeout(()=>t.fire({status:s.TextToSpeechStatus.Stopped})),{onDidChange:t.event,synthesize:()=>{}};let r=l++,n=T.get(i?.language??E.locale);(!n||n.version!==m)&&(n=E),e.trace(`[vscode-speech-${r}] starting text-to-speech session (language: ${n.locale}, model: ${n.modelName}, path: ${n.modelPath})`);let b=new AbortController;o.onCancellationRequested(()=>{e.trace(`[vscode-speech-${r}] aborting text-to-speech session`),b.abort(),t.dispose()});let p=d.get(n.locale);return p||(p=new y(n),d.set(n.locale,p)),{onDidChange:t.event,synthesize(f){o.isCancellationRequested||(e.trace(`[vscode-speech-${r}] synthesize: ${f}`),p.synthesize(f,b.signal,(c,C)=>{switch(c&&(e.error(`[vscode-speech-${r}] text-to-speech error: ${c.message}`),t.fire({status:s.TextToSpeechStatus.Error,text:c.message})),C.status){case h.SynthesizerStatusCode.STARTED:e.trace(`[vscode-speech-${r}] started text-to-speech session`),t.fire({status:s.TextToSpeechStatus.Started});break;case h.SynthesizerStatusCode.STOPPED:e.trace(`[vscode-speech-${r}] stopped text-to-speech session`),t.fire({status:s.TextToSpeechStatus.Stopped});break;case h.SynthesizerStatusCode.ERROR:e.error(`[vscode-speech-${r}] text-to-speech error: ${C.data}`),t.fire({status:s.TextToSpeechStatus.Error,text:C.data});break;case h.SynthesizerStatusCode.DISPOSED:e.trace(`[vscode-speech-${r}] disposed text-to-speech session`);break}}))}}},provideKeywordRecognitionSession:o=>{let i=new s.EventEmitter;if(o.isCancellationRequested)return{onDidChange:i.event};let t=l++;e.trace(`[vscode-speech-${t}] starting keyword-recognition session`);let r=new AbortController;return o.onCancellationRequested(()=>{e.trace(`[vscode-speech-${t}] aborting keyword-recognition session`),r.abort(),i.dispose()}),L(r.signal).then(n=>{switch(n.status){case h.KeywordRecognitionStatusCode.RECOGNIZED:e.trace(`[vscode-speech-${t}] keyword recognized: ${n.data}`),i.fire({status:s.KeywordRecognitionStatus.Recognized,text:n.data});break;case h.KeywordRecognitionStatusCode.STOPPED:e.trace(`[vscode-speech-${t}] stopped keyword-recognition session${n.data?`: ${n.data}`:""}`),i.fire({status:s.KeywordRecognitionStatus.Stopped});break;case h.KeywordRecognitionStatusCode.ERROR:e.error(`[vscode-speech-${t}] keyword-recognition error: ${n.data}`);break}},n=>{e.error(`[vscode-speech-${t}] keyword-recognition error: ${n.message}`)}),{onDidChange:i.event}}}))}function H(){}
//# sourceMappingURL=extension.js.map
