"use strict";
/**
 *  provider - build / compile related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicBuildTaskProvider = void 0;
exports.getCatalogParameters = getCatalogParameters;
const path = require("path");
const vscode = require("vscode");
const fs = require("fs");
const minimatch = require("minimatch");
const glob = require("glob");
const extConfig = require("../config/extConfig");
const buildTerminal_1 = require("./buildTerminal");
const restart = require("../daClient/restart");
const extension_1 = require("../extension");
const builder_1 = require("./builder");
const msg_1 = require("../msg");
function getDataSource() {
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs === undefined || wsfs.length == 0) {
        return "UNIVERSE";
    }
    const config = extConfig.read(wsfs[0].uri.fsPath, extConfig.db, extConfig.offlineMode);
    return config.db.dataSource;
}
function getCatalogParameters() {
    var _a;
    var catalogType = "";
    var initialCharacter = "";
    var catalog_arguments = "";
    var ud_compile_flavor = "";
    var compile_arguments = "";
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs === undefined || wsfs.length == 0) {
        return {
            catalog: catalogType,
            initialCharacter: initialCharacter,
            catalog_arguments: catalog_arguments,
            ud_compile_flavor: ud_compile_flavor,
            compile_arguments: compile_arguments
        };
    }
    if (vscode.window.activeTextEditor) {
        const activeDocPath = vscode.window.activeTextEditor.document.uri;
        var workspaceFolder = ((_a = vscode.workspace.getWorkspaceFolder(activeDocPath)) === null || _a === void 0 ? void 0 : _a.uri.fsPath) || "";
        const config = extConfig.read(workspaceFolder, extConfig.basic_config, extConfig.offlineMode);
        catalogType = config.catalog || "";
        initialCharacter = config.initialCharacter || "";
        catalog_arguments = config.catalog_arguments || "";
        ud_compile_flavor = config.ud_compile_flavor;
        compile_arguments = config.compile_arguments || "";
    }
    return {
        catalog: catalogType,
        initialCharacter: initialCharacter,
        catalog_arguments: catalog_arguments,
        ud_compile_flavor: ud_compile_flavor,
        compile_arguments: compile_arguments
    };
}
// Add this flag here to ignore catalog process, in order to resolve the issue
// when debug BASIC, focused file will be cataloged.
// TODO: this is just a temp solution, we should not add such global flag.
let bIgnoreCatalog = false;
class BasicBuildTaskProvider {
    addQuickBuildFiles(files, isSameAccount) {
        this.quickBuilding = true;
        this.isSameAccount = isSameAccount;
        bIgnoreCatalog = true;
        this.quickBuildFiles = [];
        for (let file of files) {
            if (file.length != 0) {
                this.quickBuildFiles.push(file);
            }
        }
    }
    addQuickBuildFile(file, isSameAccount) {
        if (file.length != 0) {
            this.quickBuilding = true;
            this.quickBuildFiles.push(file);
            this.isSameAccount = isSameAccount;
        }
    }
    provideTasks() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.getTasks();
        });
    }
    constructor() {
        this.isSameAccount = undefined;
        this.quickBuildFiles = [];
        this.quickBuilding = false;
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new BasicBuildTaskProvider();
        }
        return this.instance;
    }
    clearFilesList() {
        this.quickBuildFiles = [];
        this.quickBuilding = false;
        bIgnoreCatalog = false;
    }
    resolveTask(_task) {
        let _definition = _task.definition;
        let _targets = [];
        // If debugging restart process is running, then we need build the debugging files.
        if (restart.isRunning()) {
            restart.updateTimestamp();
            this.quickBuilding = true;
            this.quickBuildFiles = restart.getDebugFiles();
            this.isSameAccount = true;
            bIgnoreCatalog = true;
        }
        else {
            _targets = _definition.targets;
            if (_targets === undefined) {
                return this.getTask([], false, _definition);
            }
        }
        this.workspaceFolder = _task.scope;
        // If the build files list is not empty, it means the request is
        // from right-click build. Set the build files rather than use the
        // files in build task configuration file.
        if (this.quickBuilding) {
            return this.getTask(this.quickBuildFiles, false, _definition);
        }
        if (extension_1.online != undefined) {
            vscode.window.showInformationMessage(msg_1.Msg.DEBUG_COMPILE_CONFIG);
            return extension_1.online.builder.getTask(_definition);
        }
        vscode.window.showInformationMessage(msg_1.Msg.DEBUG_COMPILE_CONFIG);
        return this.getTask(this.extract(_targets), false, _definition);
    }
    getTask(targets, quickBuild, 
    // By default, don't catalog the program
    definition, type) {
        const def = definition || (type === builder_1.Catalog.Do ? this.getCatalogDefinition(builder_1.Catalog.Do) : this.getU2Definition());
        if (quickBuild) {
            this.quickBuilding = true;
        }
        if (this.quickBuilding) {
            this.quickBuildFiles = targets;
            return this.createQuickBuildTask(def);
        }
        else {
            return this.createNormalBuildTask(targets, def);
        }
    }
    /**
     * This method is used to build UD BASIC programs with specified flavor, only for right-click compile.
     * @param files Files to be compiled.
     * @param flavor Pick or UniBasic
     * @returns Build task
     */
    getTaskForUdQuickCompile(files, flavor) {
        const ds = "UNIDATA";
        if (!flavor) {
            flavor = "UniBasic";
        }
        let def = {
            type: BasicBuildTaskProvider.buildType,
            targets: [],
            compile: {
                dataSource: ds,
                language: flavor
            }
        };
        this.quickBuilding = true;
        this.quickBuildFiles = files;
        return this.createQuickBuildTask(def);
    }
    createQuickBuildTask(definition) {
        let scope = this.getWorkspace(this.quickBuildFiles);
        const task = new vscode.Task(definition, scope, BasicBuildTaskProvider.buildLabel, BasicBuildTaskProvider.buildType, new vscode.CustomExecution(() => __awaiter(this, void 0, void 0, function* () {
            return new buildTerminal_1.BasicBuildTaskTerminal(this.quickBuildFiles, definition, bIgnoreCatalog, this.isSameAccount);
        })));
        task.presentationOptions.panel = vscode.TaskPanelKind.Dedicated;
        task.presentationOptions.showReuseMessage = false;
        task.presentationOptions.clear = true;
        task.group = vscode.TaskGroup.Build;
        return task;
    }
    createNormalBuildTask(targets, definition) {
        let scope = this.getWorkspace(targets);
        const task = new vscode.Task(definition, scope, BasicBuildTaskProvider.buildLabel, BasicBuildTaskProvider.buildType, new vscode.CustomExecution(() => __awaiter(this, void 0, void 0, function* () {
            return new buildTerminal_1.BasicBuildTaskTerminal(targets, definition, bIgnoreCatalog);
        })));
        task.presentationOptions.panel = vscode.TaskPanelKind.Dedicated;
        task.presentationOptions.showReuseMessage = false;
        task.presentationOptions.clear = true;
        task.group = vscode.TaskGroup.Build;
        return task;
    }
    createDefaultBuildTask(targets, definition, scope) {
        const task = new vscode.Task(definition, scope, BasicBuildTaskProvider.buildLabel, BasicBuildTaskProvider.buildType, new vscode.CustomExecution(() => __awaiter(this, void 0, void 0, function* () {
            if (restart.isRunning()) {
                restart.updateTimestamp();
                return new buildTerminal_1.BasicBuildTaskTerminal(restart.getDebugFiles(), definition, bIgnoreCatalog);
            }
            else {
                return new buildTerminal_1.BasicBuildTaskTerminal(targets, definition, bIgnoreCatalog);
            }
        })));
        task.presentationOptions.panel = vscode.TaskPanelKind.Dedicated;
        task.presentationOptions.showReuseMessage = false;
        task.presentationOptions.clear = true;
        //Because vscode1.69.0 updated, "Run build task" logic was changed
        //So set build task group
        //Or else, vscode will prompt "No build task ...", then no response when click this prompt
        //In addition, we need to modify extension documentation:
        //  Before run build task, we need to configure [default build] task
        task.group = vscode.TaskGroup.Build;
        return task;
    }
    // Create default task definition
    getDefinition() {
        const ds = getDataSource();
        return {
            type: BasicBuildTaskProvider.buildType,
            targets: [],
            compile: {
                dataSource: ds
            }
        };
    }
    // Right click compile
    getU2Definition() {
        const ds = getDataSource();
        const params = getCatalogParameters();
        let definition;
        if (ds === "UNIVERSE") {
            definition = {
                type: BasicBuildTaskProvider.buildType,
                targets: [],
                compile: {
                    dataSource: ds,
                    arguments: params.compile_arguments
                }
            };
        }
        else {
            definition = {
                type: BasicBuildTaskProvider.buildType,
                targets: [],
                compile: {
                    dataSource: "UNIDATA",
                    language: params.ud_compile_flavor,
                    arguments: params.compile_arguments
                }
            };
        }
        return definition;
    }
    getCatalogDefinition(type) {
        const ds = getDataSource();
        const params = getCatalogParameters();
        const flavor = params.ud_compile_flavor || 'Unibasic';
        if (ds === "UNIVERSE") {
            return {
                type: BasicBuildTaskProvider.buildType,
                targets: [],
                compile: {
                    dataSource: ds,
                    catalog: params.catalog || '',
                    initialCharacter: params.initialCharacter || '',
                    arguments: params.catalog_arguments || ''
                }
            };
        }
        else {
            return {
                type: BasicBuildTaskProvider.buildType,
                targets: [],
                compile: {
                    dataSource: ds,
                    language: flavor,
                    catalog: params.catalog || '',
                    arguments: params.catalog_arguments || ''
                }
            };
        }
    }
    // Extract full path files from 'targets'.
    extract(targetFiles) {
        const tempArray = targetFiles.map(item => {
            return this.globSync(item, this.workspaceFolder);
        }).reduce((a, b) => { return a.concat(b); });
        return tempArray.filter((item, index) => {
            return tempArray.indexOf(item, 0) === index;
        });
    }
    getTasks() {
        const tasks = [];
        if (this.quickBuilding) {
            tasks.push(this.getTask(this.quickBuildFiles, this.quickBuilding));
        }
        if (extension_1.online != undefined) {
            tasks.push(extension_1.online.builder.getDefaultTask());
        }
        else {
            const wsfs = vscode.workspace.workspaceFolders;
            if (wsfs) {
                for (const wsf of wsfs) {
                    if (extConfig.rmvExists(wsf)) {
                        const def = this.getDefinition();
                        const task = this.createDefaultBuildTask([], def, wsf);
                        tasks.push(task);
                    }
                }
            }
        }
        return tasks;
    }
    globSync(relPath, wsf) {
        let workspacePath = "";
        if (wsf) {
            workspacePath = wsf.uri.fsPath;
        }
        const patten = path.join(workspacePath, relPath);
        let paths = glob.sync(patten);
        if (paths.length == 0) {
            this.fsReadDir(workspacePath, paths);
            paths = minimatch.match(paths, patten);
        }
        return paths;
    }
    fsReadDir(dir, fileList) {
        fs.readdirSync(dir).forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            if (stat.isDirectory()) {
                this.fsReadDir(filePath, fileList);
            }
            if (stat.isFile()) {
                fileList.push(filePath);
            }
        });
    }
    getWorkspace(files) {
        const workspaces = vscode.workspace.workspaceFolders;
        if (!workspaces || workspaces.length == 0 || files.length == 0) {
            return vscode.TaskScope.Workspace;
        }
        let ret = undefined;
        let maxLen = 0;
        const filePath = files[0];
        const tempDocPath = filePath.toLowerCase().replace(/\\/g, "/");
        for (const ws of workspaces) {
            // It's very strange that not all "\\" could be replaced, so I use
            // 'path' rather than 'fsPath' to get the wsPath, and need remove the
            // first character '/'.
            const wsPath = ws.uri.fsPath.toLowerCase().replace(/\\/g, "/");
            if (tempDocPath.includes(wsPath) && tempDocPath.length > maxLen) {
                ret = ws;
                maxLen = ret.uri.fsPath.length;
            }
        }
        return ret ? ret : vscode.TaskScope.Workspace;
    }
}
exports.BasicBuildTaskProvider = BasicBuildTaskProvider;
BasicBuildTaskProvider.buildType = 'BASIC';
BasicBuildTaskProvider.buildLabel = 'Build';
BasicBuildTaskProvider.instance = undefined;
//# sourceMappingURL=provider.js.map