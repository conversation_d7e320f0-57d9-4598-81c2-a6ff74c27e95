{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Authorization code is required.": "需要授權碼。", "Error validating custom environment setting: {0}": "驗證自訂環境設定時發生錯誤: {0}", "Having trouble logging in? Would you like to try a different way? ({0})": "登入時遇到問題嗎? 要嘗試其他方式嗎? ({0})", "Microsoft Account configuration has been changed.": "Microsoft 帳戶設定已變更。", "Microsoft Authentication": "Microsoft 驗證", "Microsoft Sovereign Cloud Authentication": "Microsoft Sovereign Cloud Authentication", "No": "否", "Open settings": "開啟設定", "Paste authorization code here...": "在這裡貼上授權碼...", "Provide the authorization code to complete the sign in flow.": "提供授權碼以完成登入流程。", "Reload": "重新載入", "Signing in to Microsoft...": "正在登入 Microsoft...", "Signing in to your account...": "正有登入您的帳戶...", "The environment `{0}` is not a valid environment.": "環境 `{0}` 不是有效的環境。", "Yes": "是", "You have been signed out because reading stored authentication information failed.": "由於無法讀取儲存的驗證資訊，所以已將您登出。", "You have not yet finished authorizing this extension to use your Microsoft Account. Would you like to try a different way? ({0})": "您尚未完成授權此延伸模組使用您的 Microsoft 帳戶。要嘗試其他方式嗎? ({0})", "You must also specify a custom environment in order to use the custom environment auth provider.": "您也必須指定自訂環境，才能使用自訂環境驗證提供者。"}, "package": {"description": "Microsoft 驗證提供者", "displayName": "Microsoft 帳戶", "microsoft-authentication.implementation.description": "用來使用Microsoft 帳戶登入的驗證實作。\r\n\r\n*注意： 'classic' 實作已過時，並將隨此設定一起在未來的版本中移除。如果只有「傳統」實作適用於您，請 [open an issue](command：workbench.action.openIssueReporter)，並說明您嘗試登入的內容。*", "microsoft-authentication.implementation.enumDescriptions.classic": "(已取代) 使用傳統驗證流程以使用 Microsoft 帳戶登入。", "microsoft-authentication.implementation.enumDescriptions.msal": "使用 Microsoft 驗證程式庫 (MSAL) 登入 Microsoft 帳戶。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryEndpointUrl.description": "自訂主權雲端的 Active Directory 端點。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryResourceId.description": "自訂主權雲端的 Active Directory 資源識別碼。", "microsoft-sovereign-cloud.customEnvironment.description": "要與 Microsoft 主權雲端驗證提供者搭配使用的主權雲端的自訂設定。若要使用此功能，需要此項目與將 `#microsoft-sovereign-cloud.environment#` 設定為 `custom`。", "microsoft-sovereign-cloud.customEnvironment.managementEndpointUrl.description": "自訂主權雲端的管理端點。", "microsoft-sovereign-cloud.customEnvironment.name.description": "自訂主權雲端的名稱。", "microsoft-sovereign-cloud.customEnvironment.portalUrl.description": "自訂主權雲端的入口網站 URL。", "microsoft-sovereign-cloud.customEnvironment.resourceManagerEndpointUrl.description": "自訂主權雲端的資源管理員端點。", "microsoft-sovereign-cloud.environment.description": "要用於驗證的主權雲端。如果您選取 `custom`，則也必須設定 `#microsoft-sovereign-cloud.customEnvironment#` 設定。", "microsoft-sovereign-cloud.environment.enumDescriptions.AzureChinaCloud": "Azure 中國", "microsoft-sovereign-cloud.environment.enumDescriptions.AzureUSGovernment": "Azure 美國政府", "microsoft-sovereign-cloud.environment.enumDescriptions.custom": "自訂 Microsoft 主權雲端", "signIn": "登入", "signOut": "登出"}}}