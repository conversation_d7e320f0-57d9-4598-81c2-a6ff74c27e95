{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Auto detecting Jake for folder {0} failed with error: {1}', this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown": "自動偵測資料夾 {0} 的 jake 失敗，錯誤為: {1}'， this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown", "Go to output": "前往輸出", "Problem finding jake tasks. See the output for more information.": "尋找 jake 工作時發生問題。如需詳細資訊，請參閱輸出。"}, "package": {"config.jake.autoDetect": "控制 Jake 工作偵測的啟用。Jake 工作偵測可能會導致在任何開啟的工作區中執行檔案。", "description": "將 Jake 功能新增至 VS Code 的延伸模組。", "displayName": "VS Code 的 Jake 支援", "jake.taskDefinition.file.description": "提供工作的 Jack 檔案。可以省略。", "jake.taskDefinition.type.description": "要自訂的 Jake 工作。"}}}