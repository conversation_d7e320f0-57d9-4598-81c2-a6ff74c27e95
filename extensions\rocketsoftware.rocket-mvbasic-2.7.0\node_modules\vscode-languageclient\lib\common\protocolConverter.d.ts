import * as code from 'vscode';
import * as ls from 'vscode-languageserver-protocol';
import ProtocolCompletionItem from './protocolCompletionItem';
export interface Converter {
    asUri(value: string): code.Uri;
    asDocumentSelector(value: ls.DocumentSelector): code.DocumentSelector;
    asPosition(value: undefined | null): undefined;
    asPosition(value: ls.Position): code.Position;
    asPosition(value: ls.Position | undefined | null): code.Position | undefined;
    asRange(value: undefined | null): undefined;
    asRange(value: ls.Range): code.Range;
    asRange(value: ls.Range | undefined | null): code.Range | undefined;
    asRanges(items: ReadonlyArray<ls.Range>, token?: code.CancellationToken): Promise<code.Range[]>;
    asDiagnostic(diagnostic: ls.Diagnostic): code.Diagnostic;
    asDiagnostics(diagnostics: ls.Diagnostic[], token?: code.CancellationToken): Promise<code.Diagnostic[]>;
    asDiagnosticSeverity(value: number | undefined | null): code.DiagnosticSeverity;
    asDiagnosticTag(tag: ls.DiagnosticTag): code.DiagnosticTag | undefined;
    asHover(hover: undefined | null): undefined;
    asHover(hover: ls.Hover): code.Hover;
    asHover(hover: ls.Hover | undefined | null): code.Hover | undefined;
    asCompletionResult(value: undefined | null, allCommitCharacters?: string[], token?: code.CancellationToken): Promise<undefined>;
    asCompletionResult(value: ls.CompletionList, allCommitCharacters?: string[], token?: code.CancellationToken): Promise<code.CompletionList>;
    asCompletionResult(value: ls.CompletionItem[], allCommitCharacters?: string[], token?: code.CancellationToken): Promise<code.CompletionItem[]>;
    asCompletionResult(value: ls.CompletionItem[] | ls.CompletionList | undefined | null, allCommitCharacters?: string[], token?: code.CancellationToken): Promise<code.CompletionItem[] | code.CompletionList | undefined>;
    asCompletionItem(item: ls.CompletionItem, defaultCommitCharacters?: string[]): ProtocolCompletionItem;
    asTextEdit(edit: undefined | null): undefined;
    asTextEdit(edit: ls.TextEdit): code.TextEdit;
    asTextEdit(edit: ls.TextEdit | undefined | null): code.TextEdit | undefined;
    asTextEdits(items: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asTextEdits(items: ls.TextEdit[], token?: code.CancellationToken): Promise<code.TextEdit[]>;
    asTextEdits(items: ls.TextEdit[] | undefined | null, token?: code.CancellationToken): Promise<code.TextEdit[] | undefined>;
    asSignatureHelp(item: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asSignatureHelp(item: ls.SignatureHelp, token?: code.CancellationToken): Promise<code.SignatureHelp>;
    asSignatureHelp(item: ls.SignatureHelp | undefined | null, token?: code.CancellationToken): Promise<code.SignatureHelp | undefined>;
    asSignatureInformation(item: ls.SignatureInformation, token?: code.CancellationToken): Promise<code.SignatureInformation>;
    asSignatureInformations(items: ls.SignatureInformation[], token?: code.CancellationToken): Promise<code.SignatureInformation[]>;
    asParameterInformation(item: ls.ParameterInformation): code.ParameterInformation;
    asParameterInformations(item: ls.ParameterInformation[], token?: code.CancellationToken): Promise<code.ParameterInformation[]>;
    asLocation(item: ls.Location): code.Location;
    asLocation(item: undefined | null): undefined;
    asLocation(item: ls.Location | undefined | null): code.Location | undefined;
    asDeclarationResult(item: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asDeclarationResult(item: ls.Declaration, token?: code.CancellationToken): Promise<code.Location | code.Location[]>;
    asDeclarationResult(item: ls.DeclarationLink[], token?: code.CancellationToken): Promise<code.LocationLink[]>;
    asDeclarationResult(item: ls.Declaration | ls.DeclarationLink[] | undefined | null, token?: code.CancellationToken): Promise<code.Declaration | undefined>;
    asDefinitionResult(item: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asDefinitionResult(item: ls.Definition, token?: code.CancellationToken): Promise<code.Definition>;
    asDefinitionResult(item: ls.DefinitionLink[], token?: code.CancellationToken): Promise<code.DefinitionLink[]>;
    asDefinitionResult(item: ls.Definition | ls.DefinitionLink[] | undefined | null, token?: code.CancellationToken): Promise<code.Definition | code.DefinitionLink[] | undefined>;
    asReferences(values: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asReferences(values: ls.Location[], token?: code.CancellationToken): Promise<code.Location[]>;
    asReferences(values: ls.Location[] | undefined | null, token?: code.CancellationToken): Promise<code.Location[] | undefined>;
    asDocumentHighlightKind(item: number): code.DocumentHighlightKind;
    asDocumentHighlight(item: ls.DocumentHighlight): code.DocumentHighlight;
    asDocumentHighlights(values: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asDocumentHighlights(values: ls.DocumentHighlight[], token?: code.CancellationToken): Promise<code.DocumentHighlight[]>;
    asDocumentHighlights(values: ls.DocumentHighlight[] | undefined | null, token?: code.CancellationToken): Promise<code.DocumentHighlight[] | undefined>;
    asSymbolKind(item: ls.SymbolKind): code.SymbolKind;
    asSymbolTag(item: ls.SymbolTag): code.SymbolTag | undefined;
    asSymbolTags(items: undefined | null): undefined;
    asSymbolTags(items: ReadonlyArray<ls.SymbolTag>): code.SymbolTag[];
    asSymbolTags(items: ReadonlyArray<ls.SymbolTag> | undefined | null): code.SymbolTag[] | undefined;
    asSymbolInformation(item: ls.SymbolInformation | ls.WorkspaceSymbol): code.SymbolInformation;
    asSymbolInformations(values: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asSymbolInformations(values: ls.SymbolInformation[] | ls.WorkspaceSymbol[], token?: code.CancellationToken): Promise<code.SymbolInformation[]>;
    asSymbolInformations(values: ls.SymbolInformation[] | ls.WorkspaceSymbol[] | undefined | null, token?: code.CancellationToken): Promise<code.SymbolInformation[] | undefined>;
    asDocumentSymbol(value: ls.DocumentSymbol, token?: code.CancellationToken): code.DocumentSymbol;
    asDocumentSymbols(value: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asDocumentSymbols(value: ls.DocumentSymbol[], token?: code.CancellationToken): Promise<code.DocumentSymbol[]>;
    asDocumentSymbols(value: ls.DocumentSymbol[] | undefined | null, token?: code.CancellationToken): Promise<code.DocumentSymbol[] | undefined>;
    asCommand(item: ls.Command): code.Command;
    asCommands(items: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asCommands(items: ls.Command[], token?: code.CancellationToken): Promise<code.Command[]>;
    asCommands(items: ls.Command[] | undefined | null, token?: code.CancellationToken): Promise<code.Command[] | undefined>;
    asCodeAction(item: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asCodeAction(item: ls.CodeAction, token?: code.CancellationToken): Promise<code.CodeAction>;
    asCodeAction(item: ls.CodeAction | undefined | null, token?: code.CancellationToken): Promise<code.CodeAction | undefined>;
    asCodeActionKind(item: null | undefined): undefined;
    asCodeActionKind(item: ls.CodeActionKind): code.CodeActionKind;
    asCodeActionKind(item: ls.CodeActionKind | null | undefined): code.CodeActionKind | undefined;
    asCodeActionKinds(item: null | undefined): undefined;
    asCodeActionKinds(items: ls.CodeActionKind[]): code.CodeActionKind[];
    asCodeActionKinds(item: ls.CodeActionKind[] | null | undefined): code.CodeActionKind[] | undefined;
    asCodeActionResult(items: (ls.Command | ls.CodeAction)[], token?: code.CancellationToken): Promise<(code.Command | code.CodeAction)[]>;
    asCodeLens(item: ls.CodeLens): code.CodeLens;
    asCodeLens(item: undefined | null): undefined;
    asCodeLens(item: ls.CodeLens | undefined | null): code.CodeLens | undefined;
    asCodeLenses(items: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asCodeLenses(items: ls.CodeLens[], token?: code.CancellationToken): Promise<code.CodeLens[]>;
    asCodeLenses(items: ls.CodeLens[] | undefined | null, token?: code.CancellationToken): Promise<code.CodeLens[] | undefined>;
    asWorkspaceEdit(item: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asWorkspaceEdit(item: ls.WorkspaceEdit, token?: code.CancellationToken): Promise<code.WorkspaceEdit>;
    asWorkspaceEdit(item: ls.WorkspaceEdit | undefined | null, token?: code.CancellationToken): Promise<code.WorkspaceEdit | undefined>;
    asDocumentLink(item: ls.DocumentLink): code.DocumentLink;
    asDocumentLinks(items: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asDocumentLinks(items: ls.DocumentLink[], token?: code.CancellationToken): Promise<code.DocumentLink[]>;
    asDocumentLinks(items: ls.DocumentLink[] | undefined | null, token?: code.CancellationToken): Promise<code.DocumentLink[] | undefined>;
    asColor(color: ls.Color): code.Color;
    asColorInformation(ci: ls.ColorInformation): code.ColorInformation;
    asColorInformations(colorPresentations: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asColorInformations(colorPresentations: ls.ColorInformation[], token?: code.CancellationToken): Promise<code.ColorInformation[]>;
    asColorInformations(colorInformation: ls.ColorInformation[] | undefined | null, token?: code.CancellationToken): Promise<code.ColorInformation[]>;
    asColorPresentation(cp: ls.ColorPresentation): code.ColorPresentation;
    asColorPresentations(colorPresentations: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asColorPresentations(colorPresentations: ls.ColorPresentation[], token?: code.CancellationToken): Promise<code.ColorPresentation[]>;
    asColorPresentations(colorPresentations: ls.ColorPresentation[] | undefined | null, token?: code.CancellationToken): Promise<code.ColorPresentation[] | undefined>;
    asFoldingRangeKind(kind: string | undefined): code.FoldingRangeKind | undefined;
    asFoldingRange(r: ls.FoldingRange): code.FoldingRange;
    asFoldingRanges(foldingRanges: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asFoldingRanges(foldingRanges: ls.FoldingRange[], token?: code.CancellationToken): Promise<code.FoldingRange[]>;
    asFoldingRanges(foldingRanges: ls.FoldingRange[] | undefined | null, token?: code.CancellationToken): Promise<code.FoldingRange[] | undefined>;
    asSelectionRange(selectionRange: ls.SelectionRange): code.SelectionRange;
    asSelectionRanges(selectionRanges: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asSelectionRanges(selectionRanges: ls.SelectionRange[], token?: code.CancellationToken): Promise<code.SelectionRange[]>;
    asSelectionRanges(selectionRanges: ls.SelectionRange[] | undefined | null, token?: code.CancellationToken): Promise<code.SelectionRange[] | undefined>;
    asInlineValue(value: ls.InlineValue): code.InlineValue;
    asInlineValues(values: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asInlineValues(values: ls.InlineValue[], token?: code.CancellationToken): Promise<code.InlineValue[]>;
    asInlineValues(values: ls.InlineValue[] | undefined | null, token?: code.CancellationToken): Promise<code.InlineValue[] | undefined>;
    asInlayHint(value: ls.InlayHint, token?: code.CancellationToken): Promise<code.InlayHint>;
    asInlayHints(values: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asInlayHints(values: ls.InlayHint[], token?: code.CancellationToken): Promise<code.InlayHint[]>;
    asInlayHints(values: ls.InlayHint[] | undefined | null, token?: code.CancellationToken): Promise<code.InlayHint[] | undefined>;
    asSemanticTokensLegend(value: ls.SemanticTokensLegend): code.SemanticTokensLegend;
    asSemanticTokens(value: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asSemanticTokens(value: ls.SemanticTokens, token?: code.CancellationToken): Promise<code.SemanticTokens>;
    asSemanticTokens(value: ls.SemanticTokens | undefined | null, token?: code.CancellationToken): Promise<code.SemanticTokens | undefined>;
    asSemanticTokensEdit(value: ls.SemanticTokensEdit): code.SemanticTokensEdit;
    asSemanticTokensEdits(value: undefined | null, token?: code.CancellationToken): Promise<undefined>;
    asSemanticTokensEdits(value: ls.SemanticTokensDelta, token?: code.CancellationToken): Promise<code.SemanticTokensEdits>;
    asSemanticTokensEdits(value: ls.SemanticTokensDelta | undefined | null, token?: code.CancellationToken): Promise<code.SemanticTokensEdits | undefined>;
    asCallHierarchyItem(item: null): undefined;
    asCallHierarchyItem(item: ls.CallHierarchyItem): code.CallHierarchyItem;
    asCallHierarchyItem(item: ls.CallHierarchyItem | null): code.CallHierarchyItem | undefined;
    asCallHierarchyItems(items: null, token?: code.CancellationToken): Promise<undefined>;
    asCallHierarchyItems(items: ls.CallHierarchyItem[], token?: code.CancellationToken): Promise<code.CallHierarchyItem[]>;
    asCallHierarchyItems(items: ls.CallHierarchyItem[] | null, token?: code.CancellationToken): Promise<code.CallHierarchyItem[] | undefined>;
    asCallHierarchyIncomingCall(item: ls.CallHierarchyIncomingCall, token?: code.CancellationToken): Promise<code.CallHierarchyIncomingCall>;
    asCallHierarchyIncomingCalls(items: null, token?: code.CancellationToken): Promise<undefined>;
    asCallHierarchyIncomingCalls(items: ReadonlyArray<ls.CallHierarchyIncomingCall>, token?: code.CancellationToken): Promise<code.CallHierarchyIncomingCall[]>;
    asCallHierarchyIncomingCalls(items: ReadonlyArray<ls.CallHierarchyIncomingCall> | null, token?: code.CancellationToken): Promise<code.CallHierarchyIncomingCall[] | undefined>;
    asCallHierarchyOutgoingCall(item: ls.CallHierarchyOutgoingCall, token?: code.CancellationToken): Promise<code.CallHierarchyOutgoingCall>;
    asCallHierarchyOutgoingCalls(items: null, token?: code.CancellationToken): Promise<undefined>;
    asCallHierarchyOutgoingCalls(items: ReadonlyArray<ls.CallHierarchyOutgoingCall>, token?: code.CancellationToken): Promise<code.CallHierarchyOutgoingCall[]>;
    asCallHierarchyOutgoingCalls(items: ReadonlyArray<ls.CallHierarchyOutgoingCall> | null, token?: code.CancellationToken): Promise<code.CallHierarchyOutgoingCall[] | undefined>;
    asLinkedEditingRanges(value: null | undefined, token?: code.CancellationToken): Promise<undefined>;
    asLinkedEditingRanges(value: ls.LinkedEditingRanges, token?: code.CancellationToken): Promise<code.LinkedEditingRanges>;
    asLinkedEditingRanges(value: ls.LinkedEditingRanges | null | undefined, token?: code.CancellationToken): Promise<code.LinkedEditingRanges | undefined>;
    asTypeHierarchyItem(item: null): undefined;
    asTypeHierarchyItem(item: ls.TypeHierarchyItem): code.TypeHierarchyItem;
    asTypeHierarchyItem(item: ls.TypeHierarchyItem | null): code.TypeHierarchyItem | undefined;
    asTypeHierarchyItems(items: null, token?: code.CancellationToken): Promise<undefined>;
    asTypeHierarchyItems(items: ls.TypeHierarchyItem[], token?: code.CancellationToken): Promise<code.TypeHierarchyItem[]>;
    asTypeHierarchyItems(items: ls.TypeHierarchyItem[] | null, token?: code.CancellationToken): Promise<code.TypeHierarchyItem[] | undefined>;
    asGlobPattern(pattern: ls.GlobPattern): code.GlobPattern | undefined;
}
export interface URIConverter {
    (value: string): code.Uri;
}
export declare function createConverter(uriConverter: URIConverter | undefined, trustMarkdown: boolean, supportHtml: boolean): Converter;
