"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.show = show;
exports.set = set;
exports.get = get;
exports.isUV = isUV;
exports.isUD = isUD;
const vscode = require("vscode");
const extConfig = require("../config/extConfig");
function show() {
    const dsi = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    const text = get();
    dsi.text = text;
    dsi.command = undefined;
    dsi.show();
}
let defaultDS = "UniVerse";
function set(ds) {
    defaultDS = ds;
}
function get() {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length == 0) {
        return defaultDS;
    }
    const workspace = workspaceFolders[0];
    if (extConfig.rmvExists(workspace)) {
        return getFromOffline(workspace);
    }
    return getFromOnline(workspace);
}
function getFromOnline(workspace) {
    const config = extConfig.read(workspace.uri.fsPath, extConfig.servers, extConfig.onlineMode);
    const ds = config.datasource;
    if (ds === undefined) {
        return defaultDS;
    }
    return ds;
}
function getFromOffline(workspace) {
    const config = extConfig.read(workspace.uri.fsPath, extConfig.db, extConfig.offlineMode);
    if (!config) {
        return defaultDS;
    }
    if (!config.db) {
        return defaultDS;
    }
    if (!config.db.dataSource) {
        return "UniVerse";
    }
    if (config.db.dataSource.toLowerCase() === "universe") {
        return "UniVerse";
    }
    return "UniData";
}
function isUV() {
    if (get().toLowerCase() === "universe") {
        return true;
    }
    return false;
}
function isUD() {
    if (get().toLowerCase() === "unidata") {
        return true;
    }
    return false;
}
//# sourceMappingURL=dataSource.js.map