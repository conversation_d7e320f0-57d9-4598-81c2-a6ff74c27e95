"use strict";
/**
 *  basicDebug - debug related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicDebug = exports.COMMAND_GET_PROGRAM_NAME = exports.DEBUG_TYPE = void 0;
/* eslint-disable no-mixed-spaces-and-tabs */
const vscode = require("vscode");
const basicDebugProvider_1 = require("./basicDebugProvider");
const basicDebugAdapterFactory_1 = require("./basicDebugAdapterFactory");
const basicDebugCommand_1 = require("./basicDebugCommand");
const debugTerminal_1 = require("./debugTerminal");
const restart = require("./restart");
exports.DEBUG_TYPE = 'mvbasic';
exports.COMMAND_GET_PROGRAM_NAME = 'vscode-rocket.mv.basic.command.getProgramName';
class BasicDebug {
    constructor(context) {
        this.context = context;
        this.buildFailure = false;
        this.debugTerminal = new debugTerminal_1.BasicDebugTerminal();
    }
    active() {
        this.context.subscriptions.push(
        //Not provide external interface, so it is invalid, to be opened when needed, perfect and effective
        vscode.commands.registerCommand(BasicDebug.COMMAND_RUN_EDITOR, (resource) => {
            return new basicDebugCommand_1.BasicDebugCommand(resource).regist('Run File', true);
        }), 
        //Not provide external interface, so it is invalid, to be opened when needed, perfect and effective
        vscode.commands.registerCommand(BasicDebug.COMMAND_DEBUG_EDITOR, (resource) => {
            return new basicDebugCommand_1.BasicDebugCommand(resource).regist('Debug File');
        }), 
        //Active this command by launch.json -> program (${command:vscode-rocket.mv.basic.command.getProgramName})
        vscode.commands.registerCommand(exports.COMMAND_GET_PROGRAM_NAME, (config) => {
            return new basicDebugCommand_1.GetProgramNameCommand(config).regist();
        }), vscode.tasks.onDidEndTaskProcess((event) => {
            if (event.exitCode !== undefined && event.exitCode > 0) {
                this.buildFailure = true;
            }
        }), vscode.debug.onDidStartDebugSession(session => {
            if (session && this.buildFailure) {
                vscode.debug.stopDebugging(session);
            }
            else {
                setTimeout(() => {
                    let terminal = vscode.window.terminals.find(t => t.name === 'Basic debugging.');
                    if (terminal) {
                        terminal.show();
                    }
                }, 300);
            }
        }), vscode.debug.onDidChangeActiveDebugSession(session => {
            if (session && this.buildFailure) {
                vscode.debug.stopDebugging(session);
            }
            if (session === undefined && this.buildFailure === true) {
                this.buildFailure = false;
            }
        }), vscode.debug.onDidReceiveDebugSessionCustomEvent(customEvent => {
            if (customEvent.event === "launchDebugTerminal") {
                this.debugTerminal.activate((customEvent.body));
            }
        }), vscode.debug.onDidTerminateDebugSession(Session => {
            //when debug session end , close debug terminal;
            this.debugTerminal.close();
            // Update timestamp, so that compiler can identify restart process is running
            restart.updateTimestamp();
        }), vscode.debug.registerDebugConfigurationProvider(exports.DEBUG_TYPE, new basicDebugProvider_1.BasicConfigurationProvider()), vscode.debug.registerDebugConfigurationProvider(exports.DEBUG_TYPE, new basicDebugProvider_1.BasicDynamicConfigurationProvider(), vscode.DebugConfigurationProviderTriggerKind.Dynamic), vscode.debug.registerDebugAdapterDescriptorFactory(exports.DEBUG_TYPE, new basicDebugAdapterFactory_1.BasicDebugAdapterFactory()), 
        // override VS Code's default implementation of the debug hover
        vscode.languages.registerEvaluatableExpressionProvider(exports.DEBUG_TYPE, new basicDebugProvider_1.BasicEvaluatableExpressionProvider()), 
        // override VS Code's default implementation of the "inline values" feature"
        vscode.languages.registerInlineValuesProvider(exports.DEBUG_TYPE, new basicDebugProvider_1.BasicInlineValuesProvider()));
    }
}
exports.BasicDebug = BasicDebug;
BasicDebug.COMMAND_RUN_EDITOR = 'vscode-rocket.mv.basic.command.runEditorContents';
BasicDebug.COMMAND_DEBUG_EDITOR = 'vscode-rocket.mv.basic.command.debugEditorContents';
//# sourceMappingURL=basicDebug.js.map