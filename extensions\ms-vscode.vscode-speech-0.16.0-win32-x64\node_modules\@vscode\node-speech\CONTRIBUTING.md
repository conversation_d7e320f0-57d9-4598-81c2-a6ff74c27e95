# Contributing to node-speech

## Building the project

### Prerequisites

- [.NET SDK](https://dotnet.microsoft.com/en-us/download) (The default recommended LTS version should work)
- [pwsh](https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell?view=powershell-7.4)
- Node.js and npm

### Steps

1. Clone the repository: `git clone https://github.com/microsoft/node-speech.git`
2. Run the install script: `npm i`.
