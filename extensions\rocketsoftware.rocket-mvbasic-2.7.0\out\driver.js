"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.send = send;
exports.onReceive = onReceive;
const node_1 = require("vscode-languageclient/node");
const lsp_1 = require("./lsp");
const mvClient = require("./mvClient/client");
const vscode = require("vscode");
const extension_1 = require("./extension");
function send(cmd) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!lsp_1.languageClient) {
            return Promise.resolve();
        }
        const vscode_lsp = require('vscode-languageserver-protocol');
        return Promise.resolve(lsp_1.languageClient.sendRequest(vscode_lsp.ExecuteCommandRequest.type, cmd));
    });
}
function onReceive(packet) {
    if (packet === "disconnect") {
        var debugSession = vscode.debug.activeDebugSession;
        if (debugSession) {
            vscode.debug.stopDebugging(debugSession);
        }
        mvClient.silentDisconnect();
    }
    const packetStr = packet;
    if (packetStr.indexOf("definition") == 0) {
        var uri = vscode.Uri.parse(packetStr.replace("definition:", ""));
        extension_1.online.addFile(uri);
        const command = node_1.Command.create("definitionDone", "definitionDone");
        send(command);
    }
}
//# sourceMappingURL=driver.js.map