"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */
Object.defineProperty(exports, "__esModule", { value: true });
const assert = require("assert");
const debugSession_1 = require("../debugSession");
class TestDebugSession extends debugSession_1.DebugSession {
    constructor() {
        super();
    }
    convertClientPath2Debugger(clientPath) {
        return this.convertClientPathToDebugger(clientPath);
    }
    convertDebuggerPath2Client(debuggerPath) {
        return this.convertDebuggerPathToClient(debuggerPath);
    }
}
suite('URI', () => {
    let da;
    setup(() => {
        da = new TestDebugSession();
    });
    teardown(() => {
        da.stop();
    });
    suite('path conversion', () => {
        test('convertClientPathToDebugger', () => {
            da.setDebuggerPathFormat('url');
            if (process.platform === 'win32') {
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\test.js'), 'file:///c:/abc/test.js');
                // drive letters are normalized to lower case
                assert.equal(da.convertClientPath2Debugger('C:\\abc\\test.js'), 'file:///c:/abc/test.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo bar.js'), 'file:///c:/abc/foo%20bar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo%bar.js'), 'file:///c:/abc/foo%25bar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\föö bär.js'), 'file:///c:/abc/f%C3%B6%C3%B6%20b%C3%A4r.js');
                // 'path' percent-encode set
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo{bar.js'), 'file:///c:/abc/foo%7Bbar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo}bar.js'), 'file:///c:/abc/foo%7Dbar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo#bar.js'), 'file:///c:/abc/foo%23bar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo?bar.js'), 'file:///c:/abc/foo%3Fbar.js');
                // not percent-encoded
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo:bar.js'), 'file:///c:/abc/foo:bar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo+bar.js'), 'file:///c:/abc/foo+bar.js'); // see https://github.com/microsoft/vscode-debugadapter-node/issues/182
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\foo_bar.js'), 'file:///c:/abc/foo_bar.js');
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\<EMAIL>'), 'file:///c:/abc/<EMAIL>');
                // see https://github.com/microsoft/vscode-debugadapter-node/issues/#159
                assert.equal(da.convertClientPath2Debugger('c:\\abc\\test.js'), 'file:///c:/abc/test.js');
            }
            else {
                assert.equal(da.convertClientPath2Debugger('/abc/test.js'), 'file:///abc/test.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo bar.js'), 'file:///abc/foo%20bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo%bar.js'), 'file:///abc/foo%25bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/föö bär.js'), 'file:///abc/f%C3%B6%C3%B6%20b%C3%A4r.js');
                // 'path' percent-encode set
                assert.equal(da.convertClientPath2Debugger('/abc/foo{bar.js'), 'file:///abc/foo%7Bbar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo}bar.js'), 'file:///abc/foo%7Dbar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo#bar.js'), 'file:///abc/foo%23bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo?bar.js'), 'file:///abc/foo%3Fbar.js');
                // not percent-encoded
                assert.equal(da.convertClientPath2Debugger('/abc/foo:bar.js'), 'file:///abc/foo:bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo+bar.js'), 'file:///abc/foo+bar.js'); // see https://github.com/microsoft/vscode-debugadapter-node/issues/182
                assert.equal(da.convertClientPath2Debugger('/abc/foo_bar.js'), 'file:///abc/foo_bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/<EMAIL>'), 'file:///abc/<EMAIL>');
            }
        });
        test('convertDebuggerPathToClient', () => {
            da.setDebuggerPathFormat('url');
            if (process.platform === 'win32') {
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/test.js'), 'c:\\abc\\test.js');
                // drive letter casing are preserved
                assert.equal(da.convertDebuggerPath2Client('file:///C:/abc/test.js'), 'c:\\abc\\test.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%20bar.js'), 'c:\\abc\\foo bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%25bar.js'), 'c:\\abc\\foo%bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/f%C3%B6%C3%B6%20b%C3%A4r.js'), 'c:\\abc\\föö bär.js');
                // 'path' percent-encode set
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%7Bbar.js'), 'c:\\abc\\foo{bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%7Dbar.js'), 'c:\\abc\\foo}bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%23bar.js'), 'c:\\abc\\foo#bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%3Fbar.js'), 'c:\\abc\\foo?bar.js');
                // not percent-encoded
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo:bar.js'), 'c:\\abc\\foo:bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo+bar.js'), 'c:\\abc\\foo+bar.js'); //see https://github.com/microsoft/vscode-debugadapter-node/issues/182
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo_bar.js'), 'c:\\abc\\foo_bar.js');
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/<EMAIL>'), 'c:\\abc\\<EMAIL>');
                // see https://github.com/microsoft/vscode-debugadapter-node/issues/#159
                assert.equal(da.convertDebuggerPath2Client('file:///c:/abc/foo%20bar/test.js'), 'c:\\abc\\foo bar\\test.js');
            }
            else {
                assert.equal(da.convertClientPath2Debugger('/abc/test.js'), 'file:///abc/test.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo bar.js'), 'file:///abc/foo%20bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo%bar.js'), 'file:///abc/foo%25bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/föö bär.js'), 'file:///abc/f%C3%B6%C3%B6%20b%C3%A4r.js');
                // 'path' percent-encode set
                assert.equal(da.convertClientPath2Debugger('/abc/foo{bar.js'), 'file:///abc/foo%7Bbar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo}bar.js'), 'file:///abc/foo%7Dbar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo#bar.js'), 'file:///abc/foo%23bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo?bar.js'), 'file:///abc/foo%3Fbar.js');
                // not percent-encoded
                assert.equal(da.convertClientPath2Debugger('/abc/foo:bar.js'), 'file:///abc/foo:bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/foo+bar.js'), 'file:///abc/foo+bar.js'); // see https://github.com/microsoft/vscode-debugadapter-node/issues/182
                assert.equal(da.convertClientPath2Debugger('/abc/foo_bar.js'), 'file:///abc/foo_bar.js');
                assert.equal(da.convertClientPath2Debugger('/abc/<EMAIL>'), 'file:///abc/<EMAIL>');
            }
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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