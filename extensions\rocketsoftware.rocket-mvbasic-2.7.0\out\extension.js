"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mvvs = exports.online = exports.extensionPath = exports.javaExe = void 0;
exports.activate = activate;
exports.deactivate = deactivate;
// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
const vscode = require("vscode");
const mvClient = require("./mvClient/client");
const mvvsfs_1 = require("./vfs/mvvsfs");
const extConfig = require("./config/extConfig");
const vscode_1 = require("vscode");
const provider_1 = require("./compile/provider");
const builder_1 = require("./compile/builder");
const workspaceConfig = require("./workspaceConfig");
const fs = require("fs");
const path = require("path");
const dataSource = require("./mvClient/dataSource");
const basicDebug_1 = require("./daClient/basicDebug");
const env_1 = require("./common/env");
const lspClient = require("./lsp");
const ui = require("./ui");
const ol = require("./config/onlineConfig");
const online_1 = require("./online/online");
const message = require("./message");
const mvvs_1 = require("./mvvs");
let basicBuildTaskProvider;
let activated = false;
// this method is called when your extension is activated
// your extension is activated the very first time the command is executed
function activate(context) {
    return __awaiter(this, void 0, void 0, function* () {
        if (activated) {
            return;
        }
        activated = true;
        exports.javaExe = (0, env_1.getJavaCmd)();
        if (exports.javaExe === undefined) {
            return undefined;
        }
        exports.extensionPath = context.extensionPath;
        (0, env_1.setLspJarPath)(context.extensionPath);
        const wsfs = vscode.workspace.workspaceFolders;
        const state = ol.getWorkingState();
        vscode.commands.executeCommand("setContext", "workingState", state);
        if (state == 1 && vscode.window.activeTextEditor == undefined) {
            return;
        }
        exports.mvvs = new mvvs_1.MvVs();
        switch (state) {
            case 1: {
                // There is no opened workspace / folder, 
                // start language server and extension work in offline mode.
                yield initWithoutWorkspaceFolders(context);
                break;
            }
            case 2: {
                // There is .rmv folder in workspace, 
                // init the extension with offline mode and use configuration in .rmv folder.
                if (wsfs) {
                    yield initWithWorkspaceFolders(context, wsfs);
                }
                break;
            }
            case 3: {
                // There is .rmvonline folder in workspace, 
                // init the extension with online mode and use configuration in .rmvonline folder.
                yield startOnlineMode(context);
                break;
            }
            case 4: {
                // No rmv and rmvonline folder, 
                // activate extension but do not start language server.
                registerCommand_Init(context);
                break;
            }
            default: {
                break;
            }
        }
        ui.setContext(context);
        registerCommands(context);
        yield message.init(context);
    });
}
// this method is called when your extension is deactivated
function deactivate() {
    lspClient.deactivate();
    if (basicBuildTaskProvider) {
        basicBuildTaskProvider.dispose();
    }
}
/**
 * @param workspaceFolderUri
 *        undefined: open single workspace
 *        vscode.Uri: open workspace folder
 */
function initWorkspaceSetting(workspaceUri) {
    workspaceConfig.defaultCloseCodeLens(workspaceUri);
}
function setupEnv(context) {
    lspClient.activate(extConfig.offlineCache);
    vscode.commands.executeCommand("setContext", "extensionEnabled", true);
    //registerCommands(context);
}
function registerCommands(context) {
    const activateExt = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.activate', () => {
        if (vscode.workspace.workspaceFolders) {
            initWithWorkspaceFolders(context, vscode.workspace.workspaceFolders);
        }
        else {
            lspClient.activate(extConfig.offlineCache);
        }
    });
    context.subscriptions.push(activateExt);
    // Register virtual file system.
    // If file has schema 'mvvs', the path will be map to project folder.
    // For example, 'mvvs:/cache/sample.B', will be map to '/your/project/folder/.rmv/cache/sample.B'.
    // So that the real file path could be hidden.
    const vfs = new mvvsfs_1.MVVSFileSystem();
    context.subscriptions.push(vscode.workspace.registerFileSystemProvider("mvaccount", vfs, { isCaseSensitive: true }));
    // multi-value server related commands
    const connectServer = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.connect', () => {
        mvClient.connect();
    });
    context.subscriptions.push(connectServer);
    const disconnectServer = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.disconnect', () => {
        mvClient.disconnect();
    });
    context.subscriptions.push(disconnectServer);
    const editConfig = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.editconfig', () => {
        extConfig.openConfigFile(extConfig.offlineMode);
    });
    context.subscriptions.push(editConfig);
    const connStatus = vscode.commands.registerCommand("_vscode-rocket.mv.basic.command.u2connect", () => {
        if (mvClient.isConnected()) {
            mvClient.disconnect();
        }
        else {
            mvClient.connect();
        }
    });
    context.subscriptions.push(connStatus);
    // add codelens command to show reference
    vscode.commands.registerCommand('codelens.reference', (position) => __awaiter(this, void 0, void 0, function* () {
        var _a, _b;
        const results = yield vscode.commands.executeCommand('vscode.executeReferenceProvider', (_a = vscode.window.activeTextEditor) === null || _a === void 0 ? void 0 : _a.document.uri, position);
        vscode.commands.executeCommand("editor.action.goToLocations", (_b = vscode.window.activeTextEditor) === null || _b === void 0 ? void 0 : _b.document.uri, new vscode_1.Position(0, 0), results, "gotoAndPeek", "no results");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.do', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Compile.Do);
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.catalog.do', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Catalog.Do);
    }));
    // Register commands for UniData compilation
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.do.ud.pick', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Compile.Do, "Pick");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.do.ud.unibasic', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Compile.Do, "UniBasic");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.do.ud.revelation', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Compile.Do, "revelation");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.do.ud.douglas', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().run(builder_1.Compile.Do, "douglas");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.select.ud.pick', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().compileSelect(selected, "Pick");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.select.ud.unibasic', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().compileSelect(selected, "UniBasic");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.select.ud.revelation', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().compileSelect(selected, "revelation");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.select.ud.douglas', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().compileSelect(selected, "douglas");
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.compile.select', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        exports.mvvs.getBuilder().compileSelect(selected);
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.codelens.show', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        workspaceConfig.updateCodeLens(true);
    }));
    vscode.commands.registerCommand('vscode-rocket.mv.basic.command.codelens.hide', (_, selected) => __awaiter(this, void 0, void 0, function* () {
        workspaceConfig.updateCodeLens(false);
    }));
    basicBuildTaskProvider = vscode.tasks.registerTaskProvider(provider_1.BasicBuildTaskProvider.buildType, provider_1.BasicBuildTaskProvider.getInstance());
    // monitor save events
    context.subscriptions.push(vscode.workspace.onDidSaveTextDocument((td) => {
        const fsPath = td.uri.fsPath;
        saveAndRefreshOnlineConfig(fsPath);
        // Try to exclude non-BASIC files.
        if (!fsPath || fsPath.endsWith(".json") || fsPath.endsWith(".md")
            || fsPath.endsWith(".txt") || fsPath.endsWith(".py")) {
            return;
        }
        exports.mvvs.getDirty().add(fsPath);
        // For online editing, need write back to server
        const wsfs = vscode.workspace.workspaceFolders;
        if (wsfs === undefined || wsfs.length > 1) {
            return;
        }
        const workspace = wsfs[0];
        if (!extConfig.rmvExists(workspace)) {
            exports.online.writeDirRecord(fsPath, td.getText());
        }
    }));
    // monitor delete events
    context.subscriptions.push(vscode.workspace.onDidDeleteFiles((td) => {
        for (const file of td.files) {
            const fsPath = file.fsPath;
            exports.mvvs.getDirty().remove(fsPath);
        }
    }));
    // monitor rename events
    context.subscriptions.push(vscode.workspace.onDidRenameFiles((td) => {
        for (const file of td.files) {
            const oldPath = file.oldUri.fsPath;
            exports.mvvs.getDirty().remove(oldPath);
            const newPath = file.newUri.fsPath;
            exports.mvvs.getDirty().add(newPath);
        }
    }));
    new basicDebug_1.BasicDebug(context).active();
}
function saveAndRefreshOnlineConfig(fsPath) {
    // Only when:
    // saving online configuration file, 
    // and online mode is enabled, 
    // and U2 server is not connected, 
    // then we can refresh the tree view in online editing view
    if (!fsPath.endsWith('servers.mvbasic.json') || !exports.online) {
        return;
    }
    if (mvClient.isConnected()) {
        message.show(80001);
        return;
    }
    vscode.commands.executeCommand("vscode-rocket.mv.basic.command.online.server.refresh");
}
function createDefaultConfig(dataSource, folderPath) {
    if (extConfig.create(folderPath, extConfig.db, extConfig.offlineMode)) {
        if (dataSource === "UNIDATA") {
            // Open configuration file, and replace "UNIVERSE" to "UNIDATA"
            const content = extConfig.readConfigFile(folderPath, extConfig.db);
            if (content == undefined) {
                return;
            }
            const dataSourceKV = "\"dataSource\": \"UNIVERSE\"";
            const udKV = "\"dataSource\": \"UNIDATA\"";
            const newContent = content.replace(dataSourceKV, udKV);
            const configFilePath = path.join(folderPath, "./.rmv/config", extConfig.db);
            fs.writeFileSync(configFilePath, newContent);
        }
    }
    //Initialize catalog config with catalog.mvbasic.json.
    extConfig.create(folderPath, extConfig.basic_config, extConfig.offlineMode);
}
function selectDataSource() {
    return __awaiter(this, void 0, void 0, function* () {
        let dataSource = "UNIVERSE";
        const selected = yield vscode.window.showQuickPick(["UniVerse", "UniData"], {
            ignoreFocusOut: true,
            placeHolder: "Please select data source."
        });
        if (selected) {
            if (selected === "UniData") {
                dataSource = "UNIDATA";
            }
        }
        return dataSource;
    });
}
function createNewConfig(path) {
    createDefaultConfig(getRootDataSource(), path);
}
function showWarning(folder) {
    const rootDataSource = getRootDataSource();
    const dataSource = getDataSource(folder.uri.fsPath);
    const msg = "Configured data source in folder " + folder.name + " is '"
        + dataSource + "', but it's '" + rootDataSource + "' in root folder.";
    vscode.window.showWarningMessage(msg);
}
function isConfigExists(path) {
    return extConfig.exists(path, extConfig.db, extConfig.offlineMode);
}
function getDataSource(path) {
    const config = extConfig.read(path, extConfig.db, extConfig.offlineMode);
    return config.db.dataSource;
}
function getRootDataSource() {
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs === undefined) {
        return "";
    }
    return getDataSource(wsfs[0].uri.fsPath);
}
function addNewFolder(folders) {
    for (const folder of folders) {
        const folderPath = folder.uri.fsPath;
        if (isConfigExists(folderPath)) {
            if (getRootDataSource() !== getDataSource(folderPath)) {
                showWarning(folder);
            }
            continue;
        }
        createNewConfig(folderPath);
    }
}
function initWorkspaceFolders() {
    vscode.workspace.onDidChangeWorkspaceFolders(event => {
        if (event.added.length != 0) {
            addNewFolder(event.added);
            isAvailable(event);
        }
    });
}
// Initialize workspace folders and setting
function initWithWorkspaceFolders(context, workspaceFolders) {
    return __awaiter(this, void 0, void 0, function* () {
        const rootFolder = workspaceFolders[0];
        // Select data source first if no configure folder exists
        if (!extConfig.rmvExists(rootFolder)) {
            const dataSource = yield selectDataSource();
            createDefaultConfig(dataSource, rootFolder.uri.fsPath);
        }
        for (let i = 0; i < workspaceFolders.length; i++) {
            if (i >= 1 && !extConfig.rmvExists(workspaceFolders[i])) {
                createNewConfig(workspaceFolders[i].uri.fsPath);
            }
            initWorkspaceSetting(workspaceFolders[i].uri);
        }
        initWorkspaceFolders();
        setupEnv(context);
        workspaceConfig.defaultFilesAssociations(extConfig.offlineCache);
        workspaceConfig.defaultFilesExclude(extConfig.offlineCache);
    });
}
// Initialize the environment when no workspace
function initWithoutWorkspaceFolders(context) {
    return __awaiter(this, void 0, void 0, function* () {
        const ds = yield selectDataSource();
        dataSource.set(ds);
        setupEnv(context);
    });
}
function initWithOnlineMode(context) {
    return __awaiter(this, void 0, void 0, function* () {
        const wsfs = vscode.workspace.workspaceFolders;
        if (wsfs === undefined || wsfs.length == 0) {
            return;
        }
        lspClient.activate(extConfig.onlineCache);
    });
}
//If add a folder which is a nested dir, we need to notice user this action is not right
function isAvailable(changeEvent) {
    let errorMsg = "Nested directories are not supported. Workspace already has the $replaceUri$, if you want to add this folder, please remove the $replaceFolder$ from workspace.";
    changeEvent.added.forEach(addFolder => {
        let workspaceFolders = vscode.workspace.workspaceFolders;
        let isAdd = true;
        if (workspaceFolders != undefined) {
            for (let i = 0; i < workspaceFolders.length; i++) {
                let folder = workspaceFolders[i];
                if (addFolder.uri.path != folder.uri.path) {
                    //Adding "/" at the end to void "a/ab" be supported as "a/a"'s child path
                    if (addFolder.uri.path.startsWith(folder.uri.path + "/")) {
                        errorMsg = errorMsg.replace("$replaceUri$", "parent folder(" + folder.uri.fsPath + ")").replace("$replaceFolder$", folder.name);
                        vscode.window.showErrorMessage(errorMsg);
                        vscode.workspace.updateWorkspaceFolders(addFolder.index, 1);
                        isAdd = false;
                        break;
                    }
                    else if (folder.uri.path.startsWith(addFolder.uri.path + "/")) {
                        errorMsg = errorMsg.replace("$replaceUri$", "child folder(" + folder.uri.fsPath + ")").replace("$replaceFolder$", folder.name);
                        vscode.window.showErrorMessage(errorMsg);
                        vscode.workspace.updateWorkspaceFolders(addFolder.index, 1);
                        isAdd = false;
                        break;
                    }
                }
            }
        }
        if (isAdd) {
            initWorkspaceSetting(addFolder.uri);
        }
    });
}
function startOnlineMode(context) {
    return __awaiter(this, void 0, void 0, function* () {
        yield initWithOnlineMode(context);
        exports.online = new online_1.Online(context);
        exports.online.registerCommons();
        exports.online.start().then(onlineStartSuc => {
            onlineStartSuc.registerFileSystem();
            return onlineStartSuc;
        }).then(onlineStartSuc => {
            onlineStartSuc.registerTreeProvider();
            ol.readConfig();
        }).then(() => {
            workspaceConfig.defaultFilesAssociations(extConfig.onlineCache);
            workspaceConfig.defaultFilesExclude(extConfig.onlineCache);
            workspaceConfig.defaultCloseCodeLens();
            exports.mvvs.onlineLaunched();
        });
    });
}
function registerCommand_Init(context) {
    context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.init', () => __awaiter(this, void 0, void 0, function* () {
        const wsfs = vscode.workspace.workspaceFolders;
        if (wsfs === undefined) {
            return;
        }
        const workspace = wsfs[0];
        extConfig.create(workspace.uri.fsPath, extConfig.servers, extConfig.onlineMode);
        extConfig.create(workspace.uri.fsPath, extConfig.basic_config, extConfig.onlineMode);
        yield startOnlineMode(context);
    })));
}
//# sourceMappingURL=extension.js.map