import { Disposable, TextDocument, ProviderResult, Range as VRange, InlineValueContext as VInlineValueContext, InlineValue as VInlineValue, InlineValuesProvider, EventEmitter } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, InlineValueOptions, InlineValueRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export declare type ProvideInlineValuesSignature = (this: void, document: TextDocument, viewPort: VRange, context: VInlineValueContext, token: CancellationToken) => ProviderResult<VInlineValue[]>;
export declare type InlineValueMiddleware = {
    provideInlineValues?: (this: void, document: TextDocument, viewPort: VRange, context: VInlineValueContext, token: CancellationToken, next: ProvideInlineValuesSignature) => ProviderResult<VInlineValue[]>;
};
export declare type InlineValueProviderShape = {
    provider: InlineValuesProvider;
    onDidChangeInlineValues: EventEmitter<void>;
};
export declare class InlineValueFeature extends TextDocumentLanguageFeature<boolean | InlineValueOptions, InlineValueRegistrationOptions, InlineValueProviderShape, InlineValueMiddleware> {
    constructor(client: FeatureClient<InlineValueMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: InlineValueRegistrationOptions): [Disposable, InlineValueProviderShape];
    private registerProvider;
}
