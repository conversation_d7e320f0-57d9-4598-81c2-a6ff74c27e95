"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setTooltip = setTooltip;
exports.disconnect = disconnect;
exports.connect = connect;
exports.show = show;
const vscode = require("vscode");
const extConfig = require("../config/extConfig");
let icon = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left);
;
let disconnIcon = "$(debug-disconnect)";
let connIcon = "$(testing-passed-icon)";
let disconnTooltip = "U2 server connected. Click to disconnect.";
let connTooltip = "U2 server disconnected. Click to connect.";
let connTooltipOnline = "Connected to ";
icon.text = disconnIcon;
icon.color = "yellow";
icon.tooltip = connTooltip;
function refresh() {
    icon.hide();
    icon.show();
}
function setTooltip(cacheType) {
    if (extConfig.onlineCache === cacheType) {
        disconnTooltip = "U2 server connected.";
        connTooltip = "U2 server disconnected.";
    }
    else {
        disconnTooltip = "U2 server connected. Click to disconnect.";
        connTooltip = "U2 server disconnected. Click to connect.";
        icon.command = "_vscode-rocket.mv.basic.command.u2connect";
    }
    icon.tooltip = connTooltip;
}
function disconnect() {
    icon.text = disconnIcon;
    icon.color = "yellow";
    icon.tooltip = connTooltip;
    refresh();
}
function connect(accountName) {
    icon.text = connIcon + " " + accountName;
    icon.tooltip = disconnTooltip;
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs) {
        if (!extConfig.rmvExists(wsfs[0])) {
            icon.text = connIcon;
            icon.tooltip = connTooltipOnline + "U2 server.";
            icon.command = undefined;
        }
    }
    refresh();
}
function show(show) {
    if (show) {
        icon.show();
    }
    else {
        icon.hide();
    }
}
//# sourceMappingURL=status.js.map