"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.languageClient = void 0;
exports.setup = setup;
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = require("vscode");
const net = require("net");
const extConfig = require("./config/extConfig");
const node_1 = require("vscode-languageclient/node");
const env = require("./common/env");
const dataSource = require("./mvClient/dataSource");
const ui = require("./ui");
const mvClient = require("./mvClient/client");
const msg_1 = require("./msg");
const driver_1 = require("./driver");
const logConfig_1 = require("./config/logConfig");
const env_1 = require("./common/env");
let logConfig;
let started = false;
function setup(cacheType) {
    if (exports.languageClient) {
        return;
    }
    let clientOptions = setupClientOptions();
    let serverOptions = setupServerOptions(cacheType);
    if (!serverOptions) {
        return;
    }
    exports.languageClient = new node_1.LanguageClient('rocketMvBasicLanguageServer', 'Rocket MV BASIC Language Server', serverOptions, clientOptions);
    mvClient.setStatusTooltip(cacheType);
    setChangeStateProcess(cacheType);
    setTelemetryProcess();
}
function setupClientOptions() {
    var _a;
    const systemWatchers = [];
    if (vscode.workspace.workspaceFolders != undefined) {
        for (let i = 0; i < ((_a = vscode.workspace.workspaceFolders) === null || _a === void 0 ? void 0 : _a.length); i++) {
            const watcher = vscode.workspace.createFileSystemWatcher('**/' + vscode.workspace.workspaceFolders[i].name + '/**');
            systemWatchers.push(watcher);
        }
    }
    const clientOptions = {
        // Register the server for plain text documents
        documentSelector: [
            { scheme: 'file', language: env.LANGUAGE_ID },
            { scheme: env_1.FS_SCHEMA, language: env.LANGUAGE_ID }
        ],
        synchronize: {
            fileEvents: systemWatchers
        }
    };
    return clientOptions;
}
function setupServerOptions(cacheType) {
    let serverOptions;
    const developmentMode = vscode.workspace.getConfiguration().get(env.DEVELOPMENT_MODEL) || false;
    logConfig = new logConfig_1.LogConfig(cacheType);
    if (!developmentMode) {
        // Check java 11 environment
        // If java 11 is setup, no need change the command, otherwise check the jdk 11 config
        // if empty, show error message, if not use the path to check java 11
        // if still not work, show error message
        // otherwise go on
        let javaCmd = env.getJavaCmd();
        if (javaCmd === undefined) {
            return undefined;
        }
        // TODO: Get current mode: online or offline
        // Check config, if has rmv, it's offline, or it will be online
        let mode = "offline";
        const wsfs = vscode.workspace.workspaceFolders;
        if (wsfs) {
            const workspace = wsfs[0];
            if (!extConfig.rmvExists(workspace)) {
                mode = "online";
            }
        }
        serverOptions = {
            command: javaCmd,
            args: [
                //"-Xdebug",
                //"-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8000,quiet=y",
                "-jar",
                logConfig.getLaunchParameter(),
                env.getLspJarPath(),
                mode,
                "system",
                dataSource.get(),
            ],
            options: {
            // stdio: "pipe"
            }
        };
    }
    else {
        let hostName = env.DEFAULT_HOST_NAME;
        let hostPort = env.DEFAULT_HOST_LSP_PORT;
        serverOptions = () => {
            const socket = net.connect({
                host: hostName,
                port: hostPort,
            });
            const result = {
                reader: socket,
                writer: socket,
            };
            return Promise.resolve(result);
        };
    }
    return serverOptions;
}
function activate(cacheType) {
    setup(cacheType);
    if (exports.languageClient && !started) {
        exports.languageClient.start().then(() => {
            const command = node_1.Command.create("setLogConfigParameter", "setLogConfigParameter", logConfig.getLaunchParameter());
            (0, driver_1.send)(command);
        });
    }
    vscode.window.onDidChangeActiveTextEditor(event => {
        if (event && event.document && event.document.fileName) {
            const command = node_1.Command.create("textChange", "textChange", event.document.fileName);
            (0, driver_1.send)(command);
        }
    });
}
function deactivate() {
    if (exports.languageClient) {
        exports.languageClient.stop();
    }
}
function setChangeStateProcess(cacheType) {
    exports.languageClient.onDidChangeState((e) => {
        if (e.oldState === node_1.State.Starting && e.newState === node_1.State.Stopped) {
            mvClient.showConnectIcon(false);
            vscode.window.showErrorMessage(msg_1.Msg.LS_START_FAILED);
        }
        else if (e.oldState === node_1.State.Starting && e.newState === node_1.State.Running) {
            vscode.window.showInformationMessage(msg_1.Msg.LS_START_SUCCESS);
            started = true;
            if (cacheType == extConfig.offlineCache) {
                ui.registerGroupView();
            }
            dataSource.show();
            mvClient.showConnectIcon(true);
            if (dataSource.isUD()) {
                vscode.commands.executeCommand("setContext", "datasource", "ud");
            }
            else {
                vscode.commands.executeCommand("setContext", "datasource", "uv");
            }
        }
        else if (e.oldState === node_1.State.Running && e.newState === node_1.State.Stopped) {
            mvClient.showConnectIcon(false);
            vscode.window.showWarningMessage(msg_1.Msg.LS_STOPPED);
        }
    });
}
function setTelemetryProcess() {
    exports.languageClient.onTelemetry(driver_1.onReceive);
}
//# sourceMappingURL=lsp.js.map