{"version": 3, "targets": {"net8.0": {"Azure.Core/1.44.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CognitiveServices.Speech/1.44.0": {"type": "package", "dependencies": {"Azure.Core": "1.44.1"}, "compile": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "build": {"build/Microsoft.CognitiveServices.Speech.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "ios-arm64"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-arm64"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-x64"}, "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.SR/1.44.0": {"type": "package", "dependencies": {"Microsoft.CognitiveServices.Speech": "1.44.0", "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime": "1.44.0", "Microsoft.CognitiveServices.Speech.Extension.Telemetry": "1.44.0"}, "build": {"build/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS/1.44.0": {"type": "package", "dependencies": {"Microsoft.CognitiveServices.Speech": "1.44.0", "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime": "1.44.0", "Microsoft.CognitiveServices.Speech.Extension.Telemetry": "1.44.0"}, "build": {"build/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime/1.44.0": {"type": "package", "dependencies": {"Microsoft.CognitiveServices.Speech": "1.44.0"}, "build": {"build/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.CognitiveServices.Speech.Extension.Telemetry/1.44.0": {"type": "package", "dependencies": {"Microsoft.CognitiveServices.Speech": "1.44.0"}, "build": {"build/Microsoft.CognitiveServices.Speech.Extension.Telemetry.props": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.ClientModel/1.1.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "6.0.9"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory.Data/6.0.0": {"type": "package", "dependencies": {"System.Text.Json": "6.0.0"}, "compile": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/6.0.10": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"Azure.Core/1.44.1": {"sha512": "YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "type": "package", "path": "azure.core/1.44.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.44.1.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CognitiveServices.Speech/1.44.0": {"sha512": "pUGHPjxsv72SYw2Fh0rQV8NehZcDqzlUMLZ1keOSWaX8Yqs6QcANIu+xopdRnI2V8rY0x+8GJ8Ern/i4+D6Hdw==", "type": "package", "path": "microsoft.cognitiveservices.speech/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.props", "build/REDIST.txt", "build/monoandroid/Microsoft.CognitiveServices.Speech.targets", "build/native/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/Microsoft.CognitiveServices.Speech.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/include/c_api/CMakeLists.txt", "build/native/include/c_api/azac_api_c_common.h", "build/native/include/c_api/azac_api_c_diagnostics.h", "build/native/include/c_api/azac_api_c_error.h", "build/native/include/c_api/azac_api_c_pal.h", "build/native/include/c_api/azac_debug.h", "build/native/include/c_api/azac_error.h", "build/native/include/c_api/speechapi_c.h", "build/native/include/c_api/speechapi_c_audio_config.h", "build/native/include/c_api/speechapi_c_audio_processing_options.h", "build/native/include/c_api/speechapi_c_audio_stream.h", "build/native/include/c_api/speechapi_c_audio_stream_format.h", "build/native/include/c_api/speechapi_c_auto_detect_source_lang_config.h", "build/native/include/c_api/speechapi_c_common.h", "build/native/include/c_api/speechapi_c_connection.h", "build/native/include/c_api/speechapi_c_conversation.h", "build/native/include/c_api/speechapi_c_conversation_transcription_result.h", "build/native/include/c_api/speechapi_c_conversation_translator.h", "build/native/include/c_api/speechapi_c_diagnostics.h", "build/native/include/c_api/speechapi_c_dialog_service_config.h", "build/native/include/c_api/speechapi_c_dialog_service_connector.h", "build/native/include/c_api/speechapi_c_embedded_speech_config.h", "build/native/include/c_api/speechapi_c_error.h", "build/native/include/c_api/speechapi_c_ext_audiocompression.h", "build/native/include/c_api/speechapi_c_factory.h", "build/native/include/c_api/speechapi_c_grammar.h", "build/native/include/c_api/speechapi_c_hybrid_speech_config.h", "build/native/include/c_api/speechapi_c_intent_recognizer.h", "build/native/include/c_api/speechapi_c_intent_result.h", "build/native/include/c_api/speechapi_c_intent_trigger.h", "build/native/include/c_api/speechapi_c_json.h", "build/native/include/c_api/speechapi_c_keyword_recognition_model.h", "build/native/include/c_api/speechapi_c_language_understanding_model.h", "build/native/include/c_api/speechapi_c_meeting.h", "build/native/include/c_api/speechapi_c_meeting_transcription_result.h", "build/native/include/c_api/speechapi_c_operations.h", "build/native/include/c_api/speechapi_c_participant.h", "build/native/include/c_api/speechapi_c_pattern_matching_model.h", "build/native/include/c_api/speechapi_c_pronunciation_assessment_config.h", "build/native/include/c_api/speechapi_c_property_bag.h", "build/native/include/c_api/speechapi_c_recognizer.h", "build/native/include/c_api/speechapi_c_result.h", "build/native/include/c_api/speechapi_c_session.h", "build/native/include/c_api/speechapi_c_source_lang_config.h", "build/native/include/c_api/speechapi_c_speaker_recognition.h", "build/native/include/c_api/speechapi_c_speech_config.h", "build/native/include/c_api/speechapi_c_speech_recognition_model.h", "build/native/include/c_api/speechapi_c_speech_translation_config.h", "build/native/include/c_api/speechapi_c_speech_translation_model.h", "build/native/include/c_api/speechapi_c_synthesis_request.h", "build/native/include/c_api/speechapi_c_synthesizer.h", "build/native/include/c_api/speechapi_c_translation_recognizer.h", "build/native/include/c_api/speechapi_c_translation_result.h", "build/native/include/c_api/speechapi_c_user.h", "build/native/include/c_api/spxdebug.h", "build/native/include/c_api/spxerror.h", "build/native/include/cxx_api/CMakeLists.txt", "build/native/include/cxx_api/azac_api_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx.h", "build/native/include/cxx_api/speechapi_cxx_audio_config.h", "build/native/include/cxx_api/speechapi_cxx_audio_data_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_processing_options.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream_format.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_result.h", "build/native/include/cxx_api/speechapi_cxx_class_language_model.h", "build/native/include/cxx_api/speechapi_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx_connection.h", "build/native/include/cxx_api/speechapi_cxx_connection_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_connection_message.h", "build/native/include/cxx_api/speechapi_cxx_connection_message_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator_events.h", "build/native/include/cxx_api/speechapi_cxx_conversational_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_config.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_embedded_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_enums.h", "build/native/include/cxx_api/speechapi_cxx_event_logger.h", "build/native/include/cxx_api/speechapi_cxx_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_eventsignal.h", "build/native/include/cxx_api/speechapi_cxx_eventsignalbase.h", "build/native/include/cxx_api/speechapi_cxx_file_logger.h", "build/native/include/cxx_api/speechapi_cxx_grammar.h", "build/native/include/cxx_api/speechapi_cxx_grammar_list.h", "build/native/include/cxx_api/speechapi_cxx_grammar_phrase.h", "build/native/include/cxx_api/speechapi_cxx_hybrid_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_intent_trigger.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_log_level.h", "build/native/include/cxx_api/speechapi_cxx_meeting.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_memory_logger.h", "build/native/include/cxx_api/speechapi_cxx_participant.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_entity.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_intent.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_model.h", "build/native/include/cxx_api/speechapi_cxx_phrase_list_grammar.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_config.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_result.h", "build/native/include/cxx_api/speechapi_cxx_properties.h", "build/native/include/cxx_api/speechapi_cxx_recognition_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_base_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_session.h", "build/native/include/cxx_api/speechapi_cxx_session_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_smart_handle.h", "build/native/include/cxx_api/speechapi_cxx_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_source_language_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_identification_model.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_verification_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_bookmark_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_request.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_viseme_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_word_boundary_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_model.h", "build/native/include/cxx_api/speechapi_cxx_string_helpers.h", "build/native/include/cxx_api/speechapi_cxx_synthesis_voices_result.h", "build/native/include/cxx_api/speechapi_cxx_translation_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_translation_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_translation_result.h", "build/native/include/cxx_api/speechapi_cxx_user.h", "build/native/include/cxx_api/speechapi_cxx_utils.h", "build/native/include/cxx_api/speechapi_cxx_voice_info.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_client.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_enrollment_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_phrase_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_result.h", "build/native/uap/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/net462/Microsoft.CognitiveServices.Speech.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.targets", "build/net8.0-ios/Microsoft.CognitiveServices.Speech.targets", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-android/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-android/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-ios/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-ios/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.xml", "microsoft.cognitiveservices.speech.1.44.0.nupkg.sha512", "microsoft.cognitiveservices.speech.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-arm/native/libpal_azure_c_shared.so", "runtimes/linux-arm/native/libpal_azure_c_shared_openssl3.so", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-arm64/native/libpal_azure_c_shared.so", "runtimes/linux-arm64/native/libpal_azure_c_shared_openssl3.so", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-x64/native/libpal_azure_c_shared.so", "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so", "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll"]}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.SR/1.44.0": {"sha512": "DQ0p9TeWDuU9quGKuXsdCaj60WzTyNkNvu7PgXBVeRb+9HnHqy/SzmbFs6ee4LtJjg1e7dyC5LCb8mPy9SJUsg==", "type": "package", "path": "microsoft.cognitiveservices.speech.extension.embedded.sr/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.props", "build/REDIST.txt", "build/monoandroid/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.targets", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/native/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.sr.lib", "build/net462/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.targets", "microsoft.cognitiveservices.speech.extension.embedded.sr.1.44.0.nupkg.sha512", "microsoft.cognitiveservices.speech.extension.embedded.sr.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.dylib", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dylib", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll"]}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS/1.44.0": {"sha512": "b65/mrYuVJekffjHfoxIJfjN96bwZ33YExpl1i14RfNATh1WJ2SytwJIuSdEYhm3TKDbWoiTxqQi3qOC6jrtsg==", "type": "package", "path": "microsoft.cognitiveservices.speech.extension.embedded.tts/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.props", "build/REDIST.txt", "build/monoandroid/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.targets", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/native/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.extension.embedded.tts.lib", "build/net462/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.targets", "microsoft.cognitiveservices.speech.extension.embedded.tts.1.44.0.nupkg.sha512", "microsoft.cognitiveservices.speech.extension.embedded.tts.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.dylib", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll"]}, "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime/1.44.0": {"sha512": "m07SomYtyWfQ9RBSgGFZrw8giBObNDn+DodCChQ0yNs7I/2fA33ATq8Z9XN48hlpcd+SVsBg7D9/yI19LQF3jw==", "type": "package", "path": "microsoft.cognitiveservices.speech.extension.onnx.runtime/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.props", "build/REDIST.txt", "build/monoandroid/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.targets", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/native/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.extension.onnxruntime.lib", "build/net462/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.targets", "microsoft.cognitiveservices.speech.extension.onnx.runtime.1.44.0.nupkg.sha512", "microsoft.cognitiveservices.speech.extension.onnx.runtime.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.dylib", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll"]}, "Microsoft.CognitiveServices.Speech.Extension.Telemetry/1.44.0": {"sha512": "Ty4fha4YXc1vd/4zp6bsuM0xlqNq6ho4pyZTxuRlgfpFyH+CcTw2bTIbQRPETuwwLqqtkWZc4rdZCI7ujh0e9w==", "type": "package", "path": "microsoft.cognitiveservices.speech.extension.telemetry/1.44.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.Extension.Telemetry.props", "build/REDIST.txt", "build/monoandroid/Microsoft.CognitiveServices.Speech.Extension.Telemetry.targets", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/native/Microsoft.CognitiveServices.Speech.Extension.Telemetry.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.extension.telemetry.lib", "build/net462/Microsoft.CognitiveServices.Speech.Extension.Telemetry.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.Extension.Telemetry.targets", "microsoft.cognitiveservices.speech.extension.telemetry.1.44.0.nupkg.sha512", "microsoft.cognitiveservices.speech.extension.telemetry.nuspec", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.so", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.telemetry.dylib", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.telemetry.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.telemetry.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.telemetry.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.telemetry.dll"]}, "System.ClientModel/1.1.0": {"sha512": "UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "type": "package", "path": "system.clientmodel/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.1.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/6.0.0": {"sha512": "ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "type": "package", "path": "system.memory.data/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/net6.0/System.Memory.Data.dll", "lib/net6.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.6.0.0.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/6.0.0": {"sha512": "Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "type": "package", "path": "system.text.encodings.web/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/netcoreapp3.1/System.Text.Encodings.Web.dll", "lib/netcoreapp3.1/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.6.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/6.0.10": {"sha512": "NSB0kDipxn2ychp88NXWfFRFlmi1bst/xynOutbnpEfRCT9JZkZ7KOmF/I/hNKo2dILiMGnqblm+j1sggdLB9g==", "type": "package", "path": "system.text.json/6.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netcoreapp3.1/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/netcoreapp3.1/System.Text.Json.dll", "lib/netcoreapp3.1/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.6.0.10.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.CognitiveServices.Speech >= 1.44.0", "Microsoft.CognitiveServices.Speech.Extension.Embedded.SR >= 1.44.0", "Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS >= 1.44.0", "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime >= 1.44.0"]}, "packageFolders": {"temp_packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj", "projectName": "node-speech", "projectPath": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.SR": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime": {"target": "Package", "version": "[1.44.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}