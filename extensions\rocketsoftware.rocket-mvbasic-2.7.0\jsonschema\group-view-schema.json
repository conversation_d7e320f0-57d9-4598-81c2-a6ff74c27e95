{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "properties": {"groupView": {"type": "object", "description": "Set rules for feature files auto-group.", "properties": {"ignore": {"type": "array", "description": "File names in the list would not be shown in the group view.", "items": {"type": "string"}, "minItems": 0, "uniqueItems": true}, "default": {"type": "object", "description": "Default rule to auto-group files.", "properties": {"delimiter": {"type": "string", "description": "Specifies the character(s) to use to separate the file names."}, "level": {"type": "string", "description": "specifies how to combine group names based on the separated parts of the file names."}}}, "groups": {"type": "array", "description": "Set source code directories and customize groups", "items": {"type": "object", "properties": {"sourceDir": {"type": "string", "description": "specify the source code folder name or a relative path. Multiple source code folders can be specified."}, "groupName": {"type": "string", "description": "specifies the customized group name."}, "include": {"type": "array", "description": "specifies which files will be included in this group.", "items": {"type": "string"}, "uniqueItems": true}, "exclude": {"type": "array", "description": "specifies which files should be excluded from this group. Note that when files are excluded from a group, they can alternatively be placed into other groups.", "items": {"type": "string"}, "uniqueItems": true}}, "dependencies": {"exclude": ["include"]}}}}}}}