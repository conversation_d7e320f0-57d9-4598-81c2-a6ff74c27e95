"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.offlineCache = exports.onlineCache = exports.offlineMode = exports.onlineMode = exports.basic_config = exports.servers = exports.groupView = exports.format = exports.db = void 0;
exports.read = read;
exports.write = write;
exports.openConfigFile = openConfigFile;
exports.openConfig = openConfig;
exports.rmvExists = rmvExists;
exports.create = create;
exports.exists = exists;
exports.getPath = getPath;
exports.readConfigFile = readConfigFile;
exports.listFolders = listFolders;
exports.getDb = getDb;
exports.addNewEntry4OnlineConfig = addNewEntry4OnlineConfig;
exports.deleteEntry4OnlineConfig = deleteEntry4OnlineConfig;
const path = require("path");
const fs = require("fs");
const vscode = require("vscode");
const msg_1 = require("../msg");
exports.db = "db.mvbasic.json";
exports.format = "format.mvbasic.json";
exports.groupView = "groupView.mvbasic.json";
exports.servers = "servers.mvbasic.json";
exports.basic_config = "basic.mvbasic.json";
exports.onlineMode = "online";
exports.offlineMode = "offline";
exports.onlineCache = ".rmvonline";
exports.offlineCache = ".rmv";
function read(workspace, config, mode) {
    if (!exists(workspace, config, mode)) {
        if (!create(workspace, config, mode)) {
            return undefined;
        }
    }
    try {
        const root = getPath(workspace, mode);
        if (!root) {
            return undefined;
        }
        let content = fs.readFileSync(path.join(root, config)).toString();
        if (!content) {
            return undefined;
        }
        const regex = /(\/\*[\S\s]+\*\/|[ \t]*\/\/.*$)/;
        const regExp = new RegExp(regex, "mg");
        content = content.replace(regExp, "");
        return JSON.parse(content);
    }
    catch (e) {
        return undefined;
    }
}
function write(workspace, config, content, mode) {
    if (!exists(workspace, config, mode)) {
        if (!create(workspace, config, mode)) {
            return;
        }
    }
    try {
        const root = getPath(workspace, mode);
        if (!root) {
            return;
        }
        const str = JSON.stringify(content, null, 4);
        fs.writeFileSync(path.join(root, config).toString(), str);
    }
    catch (e) {
        return;
    }
}
function openConfigFile(mode) {
    const workspaces = vscode.workspace.workspaceFolders;
    if (!workspaces || workspaces.length == 0) {
        vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
        return;
    }
    const configList = [
        "Database and BASIC Related Configuration",
        "Formatting Configuration",
        "Group View Configuration",
        "Catalog BASIC program Configuration"
    ];
    let configFileName = "";
    let workspaceFolderPath = "";
    vscode.window.showQuickPick(configList).then(s1 => {
        switch (s1) {
            case configList[0]:
                configFileName = exports.db;
                break;
            case configList[1]:
                configFileName = exports.format;
                break;
            case configList[2]:
                configFileName = exports.groupView;
                break;
            case configList[3]:
                configFileName = exports.basic_config;
                break;
            default: break;
        }
        const folders = listFolders();
        if (folders.length > 1) {
            // multiple workspace folders
            const items = createQuickPickItems(folders);
            vscode.window.showQuickPick(items).then(s2 => {
                if (s2 === undefined || s2.description === undefined) {
                    return;
                }
                workspaceFolderPath = s2.description;
                openConfig(workspaceFolderPath, configFileName, mode);
            });
        }
        else {
            // only 1 workspace folder
            workspaceFolderPath = workspaces[0].uri.fsPath;
            openConfig(workspaceFolderPath, configFileName, mode);
        }
    });
}
function openConfig(workspaceFolderPath, configFileName, mode) {
    if (!exists(workspaceFolderPath, configFileName, mode)) {
        create(workspaceFolderPath, configFileName, mode);
    }
    const configPath = getPath(workspaceFolderPath, mode);
    if (!configPath) {
        return;
    }
    const fullPath = path.join(configPath, configFileName);
    const uri = vscode.Uri.file(fullPath);
    vscode.commands.executeCommand('vscode.open', uri);
}
// Check if .rmv folder exists in current workspace.
function rmvExists(workspaceFolder) {
    const wsf = workspaceFolder.uri.fsPath;
    const rmv = path.join(wsf, ".rmv");
    if (fs.existsSync(rmv)) {
        return true;
    }
    return false;
}
function create(workspace, config, mode) {
    createConfigDir(workspace, mode);
    if (!exists(workspace, config, mode)) {
        const root = getPath(workspace, mode);
        if (!root) {
            return false;
        }
        if (!fs.existsSync(root)) {
            try {
                mkdirsSync(root);
            }
            catch (e) {
                return false;
            }
        }
        return createDefault(workspace, config, mode);
    }
    return true;
}
function mkdirsSync(dirname) {
    if (fs.existsSync(dirname)) {
        return true;
    }
    if (mkdirsSync(path.dirname(dirname))) {
        fs.mkdirSync(dirname);
        return true;
    }
    return false;
}
function exists(workspace, config, mode) {
    const root = getPath(workspace, mode);
    if (!root) {
        return false;
    }
    return fs.existsSync(path.join(root, config));
}
function createDefault(workspace, config, mode) {
    createConfigDir(workspace, mode);
    const root = getPath(workspace, mode);
    if (!root) {
        return false;
    }
    const fullPath = path.join(root, config);
    const fd = fs.openSync(fullPath, "w+");
    if (!fd) {
        return false;
    }
    let content = readDefaultContent(config);
    let count = 0;
    if (content != undefined) {
        count = fs.writeSync(fd, content);
    }
    else {
        return false;
    }
    fs.closeSync(fd);
    if (count > 0) {
        return true;
    }
    return false;
}
function getPath(workspace, mode) {
    if (!workspace) {
        return undefined;
    }
    if (mode === exports.onlineMode) {
        return path.join(workspace, exports.onlineCache + "/config");
    }
    else {
        return path.join(workspace, exports.offlineCache + "/config");
    }
}
function readDefaultContent(configFileName) {
    const outDir = __dirname;
    const filePath = path.resolve(outDir, "../../config/", configFileName);
    if (!fs.existsSync(filePath)) {
        return undefined;
    }
    return fs.readFileSync(filePath).toString();
}
function readConfigFile(workspace, configFileName) {
    const filePath = path.join(workspace, ".rmv/config", configFileName);
    if (!fs.existsSync(filePath)) {
        return undefined;
    }
    return fs.readFileSync(filePath).toString();
}
function createConfigDir(workspace, mode) {
    let rmvFolder = exports.offlineCache;
    if (mode === exports.onlineMode) {
        rmvFolder = exports.onlineCache;
    }
    const configDir = path.join(workspace, rmvFolder + "/config/");
    if (fs.existsSync(configDir)) {
        return;
    }
    mkdir(configDir);
}
function mkdir(dirname) {
    if (fs.existsSync(dirname)) {
        return true;
    }
    if (mkdir(path.dirname(dirname))) {
        fs.mkdirSync(dirname);
        return true;
    }
    return false;
}
function listFolders() {
    const ret = [];
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs === undefined) {
        return ret;
    }
    for (const folder of wsfs) {
        const folderName = folder.name;
        const folderPath = folder.uri.fsPath;
        ret.push([folderName, folderPath]);
    }
    return ret;
}
function getDb(path) {
    const config = read(path, exports.db, exports.offlineMode);
    if (!config) {
        return undefined;
    }
    return config.db;
}
function createQuickPickItems(folders) {
    const ret = [];
    for (const folder of folders) {
        const label = folder[0];
        const description = folder[1];
        ret.push({ label: label, description: description });
    }
    return ret;
}
/**
 * Add a new server entry to online editing configuration file.
 * @returns No return value.
 */
function addNewEntry4OnlineConfig() {
    let workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders === undefined) {
        return;
    }
    let nodes = read(workspaceFolders[0].uri.fsPath, exports.servers, exports.onlineMode);
    if (nodes === undefined) {
        return;
    }
    nodes.servers.push({
        "address": "",
        "name": "",
        "username": "",
        "password": "",
        "port": 31438
    });
    write(workspaceFolders[0].uri.fsPath, exports.servers, nodes, exports.onlineMode);
}
/**
 * Delete user selected item in servers tree.
 * @param entry Selected item in server tree.
 * @returns no return value.
 */
function deleteEntry4OnlineConfig(entry) {
    if (entry === undefined) {
        return;
    }
    const serverName = entry.label;
    if (serverName === undefined) {
        return;
    }
    let workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders === undefined) {
        return;
    }
    let nodes = read(workspaceFolders[0].uri.fsPath, exports.servers, exports.onlineMode);
    if (nodes === undefined) {
        return;
    }
    let serverNodes = nodes.servers;
    let i = 0;
    for (i = 0; i < serverNodes.length; i++) {
        if (serverNodes[i].name === serverName) {
            break;
        }
    }
    if (i >= serverNodes.length) {
        return;
    }
    nodes.servers.splice(i, 1);
    write(workspaceFolders[0].uri.fsPath, exports.servers, nodes, exports.onlineMode);
}
//# sourceMappingURL=extConfig.js.map