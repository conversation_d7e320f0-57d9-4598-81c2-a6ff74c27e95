<?xml version="1.0" encoding="utf-8"?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
  <Metadata>
    <Identity Language="en-US" Id="rocket-mvbasic" Version="2.7.0" Publisher="RocketSoftware"/>
    <DisplayName>Rocket MV BASIC</DisplayName>
    <Description xml:space="preserve">Rocket MV BASIC for Visual Studio Code</Description>
    <Tags>MultiValue,MV,Pick,BASIC,PickBASIC,MV BASIC,UniData,UniVerse,debuggers,json,rocket-mvbasic,Rocket MV BASIC,rocketmvbasic,__ext_b,__ext_B</Tags>
    <Categories>Programming Languages,Linters,Snippets,Formatters,Debuggers</Categories>
    <GalleryFlags>Public</GalleryFlags>
    <Badges></Badges>
    <Properties>
      <Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.75.1" />
      <Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
      <Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
      <Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
      <Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
      
      
      
      
      
      <Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
      
      
      
    </Properties>
    <License>extension/LICENSE.txt</License>
    <Icon>extension/logo-128x128.png</Icon>
  </Metadata>
  <Installation>
    <InstallationTarget Id="Microsoft.VisualStudio.Code"/>
  </Installation>
  <Dependencies/>
  <Assets>
    <Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
    <Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/README.md" Addressable="true" /><Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/CHANGELOG.md" Addressable="true" /><Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" /><Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/logo-128x128.png" Addressable="true" />
  </Assets>
</PackageManifest>
