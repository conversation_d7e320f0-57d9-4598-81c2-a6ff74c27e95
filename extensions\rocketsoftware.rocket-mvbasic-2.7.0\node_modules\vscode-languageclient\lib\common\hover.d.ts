import { TextDocument, Disposable, Position as VPosition, CancellationToken, ProviderR<PERSON>ult, HoverProvider, Hover as VHover } from 'vscode';
import { ClientCapabilities, DocumentSelector, HoverOptions, HoverRegistrationOptions, ServerCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideHoverSignature {
    (this: void, document: TextDocument, position: VPosition, token: CancellationToken): ProviderResult<VHover>;
}
export interface HoverMiddleware {
    provideHover?: (this: void, document: TextDocument, position: VPosition, token: CancellationToken, next: ProvideHoverSignature) => ProviderResult<VHover>;
}
export declare class HoverFeature extends TextDocumentLanguageFeature<boolean | HoverOptions, HoverRegistrationOptions, HoverProvider, HoverMiddleware> {
    constructor(client: FeatureClient<HoverMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: HoverRegistrationOptions): [Disposable, HoverProvider];
    private registerProvider;
}
