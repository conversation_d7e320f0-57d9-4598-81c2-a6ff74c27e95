{"name": "Rocket MV Basic", "scopeName": "source.mvbasic", "patterns": [{"include": "#comment_line"}, {"include": "#comment_inline"}, {"include": "#label_w"}, {"include": "#label_n"}, {"include": "#strings_quoted_double"}, {"include": "#strings_quoted_single"}, {"include": "#boolean"}, {"include": "#control"}, {"include": "#control_end"}, {"include": "#directive"}, {"include": "#declaration"}, {"include": "#mark"}, {"include": "#atvar"}, {"include": "#function_declaration"}, {"include": "#builtin_func_case_sensitive"}, {"include": "#builtin_func_no_case_sensitive"}, {"include": "#statement"}, {"include": "#assign"}, {"include": "#semicolon"}, {"include": "#operator_relational_word"}, {"include": "#operator_logical_word"}, {"include": "#operator_logical"}, {"include": "#assignment"}, {"include": "#builtin_string"}, {"include": "#builtin_conversion"}, {"include": "#builtin_math"}, {"include": "#builtin_relational"}, {"include": "#builtin_system"}, {"include": "#builtin_rpc"}, {"include": "#numeric"}, {"include": "#separator"}, {"include": "#operator_relational"}, {"include": "#operator_arithmetic"}, {"include": "#operator_colon"}, {"include": "#builtin_func"}], "repository": {"comment_line": {"match": "^([0-9]+)?\\s*(?i)(!|\\*|\\$\\*|rem )(.*)$", "captures": {"1": {"name": "entity.name.label.mvbasic entity.name.tag.mvbasic"}, "2": {"name": "comment.line.mvbasic"}, "3": {"name": "comment.line.mvbasic"}}}, "comment_inline": {"match": "((((?<=\\:))\\s*(?i)(!|\\*|\\$\\*|rem )(.*)$)|((\\;)\\s*(?i)(!|\\*|\\$\\*|rem )(.*)$))", "captures": {"1": {"name": "comment.line.mvbasic"}, "2": {"name": "comment.line.mvbasic"}, "3": {"name": "comment.line.mvbasic"}}}, "numeric": {"match": "(?<![\\.\\@\\$\\%\\w])-?(?:\\.[0-9]+|[0-9][\\.0-9]*)\\b", "name": "constant.numeric.mvbasic"}, "boolean": {"match": "(?i)(?:\\$?true|\\$t|\\$?false|\\$f)\\b", "name": "constant.language.mvbasic"}, "mark": {"match": "\\@(?i)(?:fm|am|vm|sm)(?![\\.\\@\\$\\%])\\b", "name": "entity.name.constant.marks.mvbasic constant.language.mvbasic"}, "atvar": {"match": "\\@(?i)[\\w\\%\\#\\.]+", "name": "variable.other.constant.atvar.mvbasic"}, "strings_quoted_double": {"name": "string.quoted.double.mvbasic", "begin": "\"", "end": "\""}, "strings_quoted_single": {"name": "string.quoted.single.mvbasic", "begin": "'", "end": "'"}, "control": {"name": "keyword.control.conditional.flow.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(?:continue|case|otherwise|else|exit|for|gosub|go(to)?|if|loop|next|repeat|return|then|while|until)\\b(?![\\.\\@\\$\\%])"}, "control_end": {"name": "keyword.control.flow.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(?:begin|end)\\b(?![\\.\\@\\$\\%])(?!\\s+(?i)(?:transaction))"}, "statement": {"patterns": [{"include": "#builtin_io"}, {"include": "#builtin_select"}, {"include": "#builtin_misc"}, {"include": "#builtin_nls"}, {"include": "#other_keyword"}]}, "other_keyword": {"name": "keyword.other.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(?:null|equate|equ|clearcommon|precision|deffun|prog|authorization|subroutine|function|transaction|isolation|level|set|(de)?activatekey|(dis|en)abledec|storage|swap|formlist|calling|to|abort(e|m)?|call|do|from|setting|in|lit|literally|chain|enter|execute|on\\s+error|on|(pc)?perform|stop(e|m)?|locked|step|before|waiting|with|at|all|(a)?sync|append|bpiocp(n)?|setindex|buffer\\.keys|by|callc|capturing|clearsql|executesql|fieldwrite(u)?|first\\_alt\\_key|last\\_alt\\_key|nullval\\_alt\\_key|garbagecollect|include|key|length|mdperform|noconvert|nodelay|off|osclose|osdelete|osopen|osread|oswrite|osbread|osbwrite|passcom|passlist|pause|readbck(u|l)?|readfwd(u|l)?|readonly|readnexttuple|readselect|readxbck|readxfwd|resizet|returning|rndseed|rtnlist|rqm|unfiltered|udtexecute|unit|using|validatekey|wake|writeonly|match|CONVERT|del|findstr|find|groupstore|ins|locate|matbuild|matparse|remove|revremove|setrem)\\b(?![\\.\\@\\$\\%])"}, "assign": {"name": "keyword.other.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(?:assign|assigned|clear|let|mat|unassigned)\\b(?![\\.\\@\\$\\%])"}, "directive": {"name": "keyword.control.directive.mvbasic", "match": "\\s*(?i)(?:\\$map|\\$chain|\\$copyright|\\$define|\\$eject|\\$page|\\$ifdef|\\$ifndef|\\$else|\\$endif|\\$insert|\\$map|\\$options|[$#]include|\\$undefine|\\$basictype)\\b(?<![\\.\\@\\$\\%])"}, "declaration": {"name": "keyword.declaration.mvbasic storage.type.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(common|dim|dimension|program|declare)(?<![\\.\\@\\$\\%])\\b"}, "function_declaration": {"begin": "^\\s*(?i)(deffun|function|subroutine|pro)(?=\\s)\\b", "end": "(\\)|\\s*$)", "beginCaptures": {"1": {"name": "keyword.declaration.fucntion.mvbasic storage.type.function.mvbasic"}}, "patterns": [{"include": "#function_name"}, {"include": "#function_params"}, {"include": "#comment_inline"}]}, "function_name": {"name": "entity.name.function.mvbasic", "match": "[\\w\\$\\.\\%\\@]+\\b"}, "function_params": {"begin": "(\\()", "end": "(\\))", "beginCaptures": {"1": {"name": "punctuation.section.parens.begin.mvbasic"}}, "endCaptures": {"1": {"name": "punctuation.section.parens.end.mvbasic"}}, "patterns": [{"name": "storage.type keyword.declaration.type.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(?:mat)(?![\\.\\@\\$\\%])\\b"}, {"name": "punctuation.separator.mvbasic", "match": ","}, {"name": "variable.parameter.mvbasic", "match": "[\\w\\$\\.\\%\\@]+"}]}, "semicolon": {"name": "punctuation.terminator.mvbasic", "match": ";"}, "assignment": {"name": "keyword.operator.mvbasic", "match": "[:+-]="}, "separator": {"name": "punctuation.separator.mvbasic", "match": ","}, "operator_colon": {"name": "keyword.operator.mvbasic", "match": ":"}, "operator_relational": {"name": "keyword.operator.mvbasic", "match": "[><#\\=]"}, "operator_relational_word": {"name": "keyword.operator.logical.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(eq|gt|lt|ge|le|ne)\\b(?![\\.\\@\\$\\%])"}, "operator_logical_word": {"name": "keyword.operator.logical.mvbasic keyword.control.flow.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(and|or)\\b(?![\\.\\@\\$\\%])"}, "operator_logical": {"name": "keyword.operator.logical.mvbasic keyword.control.flow.mvbasic", "match": "[!&]"}, "operator_arithmetic": {"name": "keyword.operator.mvbasic", "match": "(\\*{2}|[\\*\\+\\-^/])"}, "label_w": {"name": "entity.name.label.mvbasic entity.name.tag.mvbasic", "match": "^(\\s)*[\\w\\$\\.\\%\\@]+:(?!\\s*=)"}, "label_n": {"name": "entity.name.label.mvbasic entity.name.tag.mvbasic", "match": "^(\\s)*(\\d)+(\\s)*(:)?"}, "builtin_io": {"name": "keyword.other.io.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(authorization|bscan|clearfile|close|commit|delete|deleteu|filelock|fileunlock|matread|matreadl|matreadu|matwrite|matwriteu|open(check)?|openpath|procread|procwrite|read|readl|readu|readv|readvl|readvu|recordlocked|recordlockl|recordlocku|release|rollback|(?:begin|end)\\s+transaction|set\\stransaction\\sisolation\\slevel|trans|transaction\\s+(?:abort|commit|start)|write|writeu|writev|writevu|xlate|closeseq|create|flush|get|getx|nobuf|openseq|readblk|readseq|send|status|timeout|ttyctl|ttyget|ttyset|weofseq|writeblk|writeseq|writeseqf|break|cleardata|crt|data|display|echo|footing|heading(e|n)?|hush|input(dp|if)?|inputclear|clearinput|inputdisp|inputerr|inputnull|inputtrap|keyedit|keyexit|keyedit|keyin|keytrap|opendev|page|print|printer\\s+(?:close|on|off|reset)|printer(r)?|prompt|tabstop|terminfo|tparm|tprint|readt|rewind|weof|writet)\\b(?![\\.\\@\\$\\%])"}, "builtin_select": {"name": "keyword.other.select.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(clearselect|deletelist|getlist|readlist|readnext|(s)?select(n|v)?|readnext|selecte|selectindex|selectinfo|sselect|writelist)\\b(?![\\.\\@\\$\\%])"}, "builtin_string": {"name": "entity.name.function.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(str|alpha|cats|change|checksum|col1|col2|compare|count|counts|dcount|downcase|dquote|ereplace|exchange|extract|field|fields|fieldstore|fold|getrem|group|index|indexs|insert|left|len|lens|lower|matchfield|quote|raise|replace|reuse|right|soundex|space|spaces|splice|str|strs|substrings|trim|trimb|trimbs|trimf|trimfs|trims|upcase)(\\b)?(\\s)*(?=\\()"}, "builtin_nls": {"name": "keyword.other.nls.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(auxmap|inputdisp|uprint)\\b(?![\\.\\@\\$\\%])"}, "builtin_conversion": {"name": "support.function.builtin.conversion.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(ascii|char|chars|dtx|fix|fmt|fmts|iconv|iconvs|oconv|oconvs|seq|seqs|xtd)(\\b)?(\\s)*(?=\\()"}, "builtin_math": {"name": "support.function.builtin.math.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(abs|abss|acos|adds|asin|atan|bitand|bitnot|bitor|bitreset|bitset|bittest|bitxor|cos|cosh|div|divs|exp|int|fadd|fdiv|ffix|fflt|fmul|fsub|ln|maximum|minimum|mod|mods|muls|neg|negs|num|nums|pwr|randomize|real|rnd|sadd|scmp|sdiv|sin|sinh|smul|sqrt|ssub|subs|sum|summation|tan|tanh)(\\b)?(\\s)*(?=\\()"}, "builtin_relational": {"name": "support.function.builtin.relational.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(ands|eqs|ges|gts|ifs|isnull|isnulls|les|lts|nes|not|nots|ors)(\\b)?(\\s)*(?=\\()"}, "builtin_system": {"name": "keyword.other.builtin.system.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(debug|errmsg|lock|nap|sleep|unlock)\\b(?![\\.\\@\\$\\%])"}, "builtin_rpc": {"name": "support.function.builtin.rpc.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(rpc.call|rpc.connect|rpc.disconnect)(\\b)?(\\s)*(?=\\()"}, "builtin_misc": {"name": "keyword.other.misc.mvbasic", "match": "(?<![\\.\\@\\$\\%])\\b(?i)(clearprompts|get|seek)\\b(?![\\.\\@\\$\\%])"}, "variable": {"name": "variable.other", "match": "\\@?\\w[\\w\\.\\$\\%]*"}, "builtin_func_case_sensitive": {"name": "entity.name.function.mvbasic", "match": "(?<![\\.\\$\\%])\\b(?:acceptConnection|addAuthenticationRule|addCertificate|addRequestParameter|amInitialize|amReceiveMsg|amReceiveRequest|amSendMsg|amSendRequest|amSendResponse|amTerminate|analyzeCertificate|closeSocket|createCertificate|createCertRequest|createRequest|createSecureRequest|createSecurityContext|generateKey|getCipherSuite|getHTTPDefault|getIpv|getResponseHeader|getSocketErrorMessage|getSocketInformation|getSocketOptions|initSecureServerSocket|initServerSocket|loadSecurityContext|openSecureSocket|openSocket|OPENXMLDATA|PREPAREXML|protocolLogging|readSocket|READXMLDATA|RELEASEXML|saveSecurityContext|setAuthenticationDepth|setCipherSuite|setClientAuthentication|setHTTPDefault|setIpv|setPrivateKey|setRandomSeed|setRequestHeader|setSocketOptions|showSecurityContext|SOAPCreateRequest|SOAPCreateSecureRequest|SOAPGetDefault|SOAPGetFault|SOAPGetResponseHeader|SOAPRequestWrite|SOAPSetDefault|SOAPSetParameters|SOAPSetRequestBody|SOAPSetRequestContent|SOAPSetRequestHeader|SOAPSubmitRequest|SQLAllocConnect|SQLAllocEnv|SQLAllocStmt|SQLBindCol|SQLBindParameter|SQLCancel|SQLColAttributes|SQLColumns|SQLConnect|SQLDescribeCol|SQLDisconnect|SQLError|SQLExecDirect|SQLExecute|SQLFetch|SQLFreeConnect|SQLFreeEnv|SQLFreeStmt|SQLGetInfo|SQLGetTypeInfo|SQLNumParams|SQLNumResultCols|SQLParamOptions|SQLPrepare|SQLRowCount|SQLSetConnectOption|SQLSetParam|SQLBindParameter|SQLSpecialColumns|SQLStatistics|SQLTables|SQLTransact|submitRequest|UDOArrayAppendItem|UDOArrayDeleteItem|UDOArrayGetItem|UDOArrayGetNextItem|UDOArrayGetSize|UDOArrayInsertItem|UDOArraySetItem|UDOClone|UDOCreate|UDODeleteProperty|UDOFree|UDOGetLastError|UDOGetNextProperty|UDOGetOption|UDOGetProperty|UDOGetPropertyNames|UDOGetType|UDOIsTypeOf|UDORead|UDOSetOption|UDOSetProperty|UDOWrite|XDOMAddChild|XDOMAppend|XDOMClone|XDOMClose|XDOMCreateNode|XDOMCreateRoot|XDOMEvaluate|XDOMGetAttribute|XDOMGetChildNodes|XDOMGetElementById|XDOMGetElementsByName|XDOMGetElementsByTag|XDOMGetNodeName|XDOMGetNodeType|XDOMGetNodeValue|XDOMGetOwnerDocument|XDOMGetUserData|XDOMImportNode|XDOMInsert|XDOMItem|XDOMLength|XDOMLocate|XDOMLocateNode|XDOMOpen|XDOMRemove|XDOMReplace|XDOMSetNodeValue|XDOMSetUserData|XDOMTransform|XDOMValidate|XDOMValidateDom|XDOMWrite|XMAPAppendRec|XMAPClose|XMAPCreate|XMAPOpen|XMAPReadNext|XMAPToXMLDoc|XMLError|XMLExecute|XMLGetError|XMLGetOptions|XMLGetOptionValue|XMLSetOptions|XMLTODB|writeSocket)(\\b)?(\\s)*(?=\\()"}, "builtin_func_no_case_sensitive": {"name": "entity.name.function.mvbasic", "match": "(?<![\\.\\$\\%])\\b(?i)(?:abs|acos|ALPHA|ASCII|ASIN|ATAN|BITAND|BITNOT|BITOR|BITXOR|BYTELEN|CALCULATE|CATS|CHANGE|CHAR|CHARLEN|CHARS|CHECKSUM|CLOSEXMLDATA|COL1|COL2|CONVERT|COS|COUNT|COUNTS|DATE|DBTOXML|DCOUNT|DELETE|DIGEST|DIR|DISPLAYWIDTH|DOWNCASE|DQUOTE|QUOTE|DROUND|EBCDIC|ENCODE|ENCRYPT|EQS|EREPLACE|EXP|EXTRACT|FIELD|FIELDSTORE|FILEINFO|FMT|GES|GETENV|GETPTR|GETPU|GETQUEUE|GETREADU|GETUSERGROUP|GETUSERID|GETUSERNAME|GROUP|GTS|HASH|HMAC|ICONV|ICONVS|IN|INDEX|INDICES|INMAT|INSERT|INT|ISMB|ISNV|ISNVS|ITYPE|LEN|LENS|LES|LISTUSER|LN|LOWER|LTS|MATCH(ES)?|MATCHFIELD|MAXIMUM|MBLEN|MINIMUM|MOD|NEG|NES|NFAUSER|NUM|NUMS|OCONV(S)?|PWR|RAISE|RECORDLOCKED|REMOVE|REPLACE|REUSE|RND|SADD|SCMP|SDIV|SELECTINFO|SEQ|SEQS|SETENV|SIGNATURE|SIN|SMUL|SORT|SOUNDEX|SPACE|SPACES|SPLICE|SQUOTE|SSUB|STATUS|STR(S)?|SUBSTRINGS|SUM|SYSTEM|TAN|TIME|TIMEDATE|TRIM(B|F|S)?|UNASSIGNED|UPCASE|XLATE|rem|byte|bytelen|bytetype|byteval|fmtdp|fmtsdp|folddp|getlocale|lendp|lensdp|localeinfo|setlocale|unichar|unichars|uniseq|uniseqs|date|inmat|itype|sentence|status|system|time|timedate|userinfo|eof|fileinfo|ilprompt|auditlog|century.pivot|descrinfo|icheck|mqclose|mqconn|mqdisc|pycall|pycallfunction|pycallmethod|pygetattr|pyimport|pysetattr|round|setsocketmap|subr|trunc)(\\b)?(\\s)*(?=\\()"}}}