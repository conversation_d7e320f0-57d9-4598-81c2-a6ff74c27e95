"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamTerminal = void 0;
const vscode = require("vscode");
const net = require("net");
class StreamTerminal {
    constructor(port) {
        this.writeEmitter = new vscode.EventEmitter();
        this.closeEmitter = new vscode.EventEmitter();
        this.onDidWrite = this.writeEmitter.event;
        this.onDidClose = this.closeEmitter.event;
        this.conn = new net.Socket;
        this.port = port;
    }
    open(initialDimensions) {
        this.conn.on('data', (data) => {
            this.writeEmitter.fire(data.toString('utf8'));
        }).on('end', () => {
            this.closeEmitter.fire(0);
        }).on('connect', () => {
            this.writeEmitter.fire('Debug terminal connected.\r\n');
        }).connect(this.port, 'localhost');
    }
    close() {
        this.closeConnection(true);
    }
    closeConnection(stopDap) {
        if (stopDap) {
            vscode.debug.stopDebugging(vscode.debug.activeDebugSession);
        }
        this.conn.end();
        this.closeEmitter.fire(0);
    }
    handleInput(data) {
        this.conn.write(data === '\r' ? '\n' : data);
    }
}
exports.StreamTerminal = StreamTerminal;
//# sourceMappingURL=stream_terminal.js.map