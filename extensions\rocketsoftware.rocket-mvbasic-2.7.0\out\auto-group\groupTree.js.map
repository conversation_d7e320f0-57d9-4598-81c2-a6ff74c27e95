{"version": 3, "file": "groupTree.js", "sourceRoot": "", "sources": ["../../src/auto-group/groupTree.ts"], "names": [], "mappings": ";;;AAAA,6BAA6B;AAC7B,6BAA6B;AAC7B,yBAAyB;AACzB,iCAAiC;AAEjC,6CAA+E;AAC/E,iDAAiD;AAEjD,MAAM,YAAY;IAOd,YAAY,OAAe,EAAE,eAAuB;QAL5C,WAAM,GAAkB,EAAE,CAAC;QAC3B,YAAO,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACvC,cAAS,GAAqB,EAAE,CAAC;QAIrC,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC;QAClC,IAAI,CAAC,GAAG,GAAG,IAAI,8BAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAAE,OAAO,KAAK,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,KAAK,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAAE,OAAO,KAAK,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,aAAa;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,aAAa;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAEO,mBAAmB,CAAC,IAAe;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACpC,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACxC,CAAC;QAED,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC;IAED;;;;;OAKG;IACK,QAAQ,CAAC,IAAe,EAAE,SAAiB,EAAE,MAAoB;QACrE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;gBACtD,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChB,OAAQ;YACZ,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,uBAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC7C,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAED;;;OAGG;IACK,gBAAgB,CAAC,KAAoB;QACzC,IAAI,CAAC;YACD,MAAM,MAAM,GAAsB,EAAE,CAAC;YACrC,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACT,SAAS;gBACb,CAAC;gBAED,MAAM,IAAI,GAAc,IAAI,sBAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAW,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,aAAa,GAAsB,EAAE,CAAC;YAC1C,IAAI,WAAW,GAAqB,EAAE,CAAC;YAEvC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACzB,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1B,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;wBACpB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3B,CAAC;gBACL,CAAC;YACL,CAAC;YAED,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,MAAM,GAAqB,EAAE,CAAC;YAElC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACtC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEpC,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa;QACjB,IAAI,CAAC;YACD,MAAM,UAAU,GAAgC,IAAI,GAAG,CAAC;YACxD,MAAM,MAAM,GAA2B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAEzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAChC,8EAA8E;gBAC9E,MAAM,UAAU,GAAkB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC7E,IAAI,KAAK,GAAkB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;gBAC1D,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAEnC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAChD,IAAI,UAAU,EAAE,CAAC;oBACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACnD,OAAO,IAAI,CAAC;gBAChB,CAAC;gBAED,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,kDAAkD;YAClD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACzB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,SAAS;gBACb,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;oBAChB,SAAS;gBACb,CAAC;gBAED,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC7B,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC/D,IAAI,UAAU,EAAE,CAAC;oBACb,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEnD,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,KAAoB;QACvC,MAAM,MAAM,GAAkB,EAAE,CAAC;QACjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,KAAoB;QACtC,MAAM,MAAM,GAAkB,EAAE,CAAC;QACjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,KAAU,EAAE,MAAsB;QACxD,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,OAAO,SAAS,CAAC;YACrB,CAAC;YAED,MAAM,QAAQ,GAAkB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAkB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;gBACxB,IAAI,YAAY,GAAkB,EAAE,CAAC;gBACrC,IAAI,YAAY,GAAkB,EAAE,CAAC;gBAErC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBACvD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC3C,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACrD,CAAC;gBAED,qDAAqD;gBACrD,MAAM,aAAa,GAAkB,EAAE,CAAC;gBACxC,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;oBACrC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC7C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;4BACvC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACpC,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,QAAQ,EAAE,CAAC;oBACX,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;wBAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;wBACvD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC3C,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;oBACrD,CAAC;gBACL,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;gBACzD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC9B,MAAM,WAAW,GAAG,IAAI,uBAAU,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAE/D,yCAAyC;gBACzC,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;oBAC3B,MAAM,KAAK,GAAuB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACzD,IAAI,CAAC,KAAK,EAAE,CAAC;wBACT,SAAS;oBACb,CAAC;oBACD,MAAM,SAAS,GAAc,IAAI,sBAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAC3D,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;gBAED,OAAO,WAAW,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,uBAAU,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,SAAS,CAAC,KAAU,EAAE,OAAoC;QAC9D,IAAI,CAAC;YACD,MAAM,GAAG,GAAuB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACP,OAAO,SAAS,CAAC;YACrB,CAAC;YAED,MAAM,OAAO,GAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACxD,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,IAAI,2BAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBAEzB,MAAM,KAAK,GAAkB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBAChE,MAAM,MAAM,GAAkB,EAAE,CAAC;gBACjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7B,SAAS;oBACb,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,OAAO,CAAC,QAAuB,EAAE,QAAuB;QAC5D,MAAM,MAAM,GAAkB,EAAE,CAAC;QACjC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,CAAC;YACD,MAAM,WAAW,GAA8B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACjE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACjB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBACxE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,QAAgB;QAC/B,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC;QACD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAEO,QAAQ,CAAC,QAAgB;QAC7B,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;gBACV,OAAO,SAAS,CAAC;YACrB,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEO,WAAW,CAAC,IAAY;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAEO,OAAO,CAAC,CAAY,EAAE,CAAY;QACtC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QACtB,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QACtB,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;CACJ;AAED,MAAa,SAAS;IAKlB,YAAY,cAAsB,EAAE,WAA0B;QAHtD,aAAQ,GAAwB,EAAE,CAAC;QACnC,UAAK,GAAqB,EAAE,CAAC;QAGjC,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAQ;QACZ,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,IAAI,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACtD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAED,KAAK;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACjB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC;aAAM,CAAC;YACJ,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,MAAM,SAAS,GAAG,IAAI,2BAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBAC7D,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,IAA2B;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,2BAAc,EAAE,CAAC;gBACpE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACtB,CAAC;QAED,sBAAsB;QACtB,6CAA6C;QAC7C,yDAAyD;QACzD,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB;eAC9B,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;eAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACvB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAE1B,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;oBAClC,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;oBAC/B,SAAS,GAAG,IAAI,CAAC;oBACjB,MAAM;gBACV,CAAC;YACL,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,QAAQ,CAAC,IAAe;QAC5B,MAAM,KAAK,GAAqB,EAAE,CAAC;QACnC,IAAI,IAAI,GAA0B,IAAI,CAAC;QACvC,OAAO,IAAI,EAAE,CAAC;YACV,MAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM;YACV,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,IAAe;QACrC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ;mBAC9B,IAAI,YAAY,2BAAc,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AAjHD,8BAiHC"}