import { CancellationToken } from 'vscode';
export declare type ITask<T> = () => T;
export declare class Delayer<T> {
    defaultDelay: number;
    private timeout;
    private completionPromise;
    private onSuccess;
    private task;
    constructor(defaultDelay: number);
    trigger(task: ITask<T>, delay?: number): Promise<T>;
    forceDelivery(): T | undefined;
    isTriggered(): boolean;
    cancel(): void;
    private cancelTimeout;
}
export declare class Semaphore<T = void> {
    private _capacity;
    private _active;
    private _waiting;
    constructor(capacity?: number);
    lock(thunk: () => T | PromiseLike<T>): Promise<T>;
    get active(): number;
    private runNext;
    private doRunNext;
}
export declare function setTestMode(): void;
export declare function clearTestMode(): void;
export declare type YieldOptions = {
    /**
     * The time in ms after which the function should yield.
     * The minimum yield time is 15ms
     */
    yieldAfter?: number;
    /**
     * An optional callback that is invoke when the code yields.
     */
    yieldCallback?: () => void;
};
export declare function map<P, C>(items: ReadonlyArray<P>, func: (item: P) => C, token?: CancellationToken, options?: YieldOptions): Promise<C[]>;
export declare function mapAsync<P, C>(items: ReadonlyArray<P>, func: (item: P, token?: CancellationToken) => Promise<C>, token?: CancellationToken, options?: YieldOptions): Promise<C[]>;
export declare function forEach<P>(items: ReadonlyArray<P>, func: (item: P) => void, token?: CancellationToken, options?: YieldOptions): Promise<void>;
