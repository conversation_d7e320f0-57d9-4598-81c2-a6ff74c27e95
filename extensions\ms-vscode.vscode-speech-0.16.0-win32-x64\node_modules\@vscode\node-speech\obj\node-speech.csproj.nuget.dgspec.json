{"format": 1, "restore": {"D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj": {}}, "projects": {"D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj", "projectName": "node-speech", "projectPath": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj", "packagesPath": "temp_packages", "outputPath": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.SR": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS": {"target": "Package", "version": "[1.44.0, )"}, "Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime": {"target": "Package", "version": "[1.44.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}