[{"id": 10001, "level": 4, "component": 1, "content": "Failed to start Rocket MV BASIC extension."}, {"id": 10002, "level": 1, "component": 1, "content": "Rocket MV BASIC extension started."}, {"id": 10003, "level": 2, "component": 1, "content": "Rocket MV BASIC extension has not started. Please activate it first."}, {"id": 10004, "level": 1, "component": 1, "content": "Rocket MV BASIC extension has stopped."}, {"id": 20001, "level": 1, "component": 2, "content": "No folder or workspace opened.\nPlease open a folder or a workspace first."}, {"id": 20002, "level": 1, "component": 2, "content": "Rocket MV BASIC extension already activated."}, {"id": 30001, "level": 1, "component": 3, "content": "Connection to the U2 server was successful."}, {"id": 30002, "level": 2, "component": 3, "content": "Connection to the U2 server failed."}, {"id": 30003, "level": 2, "component": 3, "content": "Connection to the U2 server failed.\nPlease fill out the configuration file first."}, {"id": 30004, "level": 1, "component": 3, "content": "Disconnected from the MV server."}, {"id": 30005, "level": 2, "component": 3, "content": "Disconnection from the MV server failed."}, {"id": 30006, "level": 1, "component": 3, "content": "Rocket MV BASIC extension will be reloaded to change the data source. Continue?"}, {"id": 30007, "level": 2, "component": 3, "content": "Please open an account workspace before using this command."}, {"id": 30008, "level": 1, "component": 3, "content": "Please input the U2 server address / host name."}, {"id": 30009, "level": 1, "component": 3, "content": "Please input the U2 server account name."}, {"id": 30010, "level": 1, "component": 3, "content": "Please input username."}, {"id": 30011, "level": 1, "component": 3, "content": "Please input password."}, {"id": 30012, "level": 1, "component": 3, "content": "Please input port number."}, {"id": 30013, "level": 3, "component": 3, "content": "Error: please open a U2 account level folder first."}, {"id": 40001, "level": 2, "component": 4, "content": "Configuration file doesn't exist. Please activate Rocket MV BASIC first then try again."}, {"id": 50001, "level": 2, "component": 5, "content": "Java 11 or Open JDK 11 is required. Please setup the Java 11, or Open JDK 11 environment first, or specify the JDK 11 directory in settings."}, {"id": 50002, "level": 2, "component": 5, "content": "Java 11 or Open JDK 11 runtime environment is irregular. Please check your Java environment and settings."}, {"id": 50003, "level": 2, "component": 5, "content": "Java 11 or Open JDK 11 is required. Current version is not supported. Please update the version and try again."}, {"id": 60001, "level": 3, "component": 6, "content": "Without workspace, debugger is not supported."}, {"id": 60002, "level": 3, "component": 6, "content": "Please connect to a U2 server first."}, {"id": 60003, "level": 2, "component": 6, "content": "A BASIC program is under debugging, if disconnect from U2 server, the debug session will be lost. Continue?"}, {"id": 60004, "level": 3, "component": 6, "content": "Please connect the db in the correct workspace folder."}, {"id": 60005, "level": 3, "component": 6, "content": "No active BASIC program file is open. Please open or configure a BASIC program file then try again."}, {"id": 60006, "level": 3, "component": 6, "content": "${editor.document.uri.fsPath} is not valid Basic file, please choose a basic file."}, {"id": 60007, "level": 3, "component": 6, "content": "Please open a file for debug."}, {"id": 60008, "level": 3, "component": 6, "content": "Failed to debug current file. This may be due to incorrect file type or incorrect language type. Please check the current file or change the language type to Rocket MV BASIC."}, {"id": 60009, "level": 2, "component": 6, "content": "The program is not under the active workspace folder."}, {"id": 60010, "level": 1, "component": 6, "content": "Compilation will read the flavor from basic.mvbasic.json file."}, {"id": 70001, "level": 2, "component": 7, "content": "Account mismatch. Account <${predict.account}> for amended file doesn't match the connected mv account <${connect.account}>."}, {"id": 70002, "level": 2, "component": 7, "content": "Please confirm whether the connected account <${account}> belongs to the workspace folder of the file to be compiled."}, {"id": 80001, "level": 1, "component": 8, "content": "Please refresh the servers list manually as there is an active connection."}, {"id": 80002, "level": 3, "component": 8, "content": "Duplicate server names are not supported."}, {"id": 80003, "level": 3, "component": 8, "content": "A file name cannot contain any of the following characters: <, >, :, \", /, |, ?, *, ."}, {"id": 80004, "level": 1, "component": 8, "content": "Please refresh the servers list manually to apply filters as there is an active connection."}]