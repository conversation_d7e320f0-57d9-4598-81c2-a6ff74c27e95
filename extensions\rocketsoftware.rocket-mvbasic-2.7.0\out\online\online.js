"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Online = void 0;
const vscode = require("vscode");
const fs = require('fs');
const path = require('path');
const mvClient = require("../mvClient/client");
const u2tree_1 = require("./explorer/u2tree");
const fsprovider_1 = require("./explorer/fsprovider");
const env_1 = require("../common/env");
const extConfig = require("../config/extConfig");
const ol = require("../config/onlineConfig");
const datasource = require("../mvClient/dataSource");
const onlineBuilder_1 = require("./compile/onlineBuilder");
const editor_1 = require("../common/editor");
const message = require("../message");
const extension_1 = require("../extension");
let storedBreakpoints = [];
class Online {
    constructor(_context) {
        this._context = _context;
        this.isResettingBreakpoints = false;
        this._treeData = new Map();
        this._fsProvider = new fsprovider_1.MVFileSystemProvider(this._treeData);
        this._treeProvider = new u2tree_1.U2ItemProvider(this._fsProvider, this._treeData);
        this._connectServer = undefined;
        this.builder = new onlineBuilder_1.OnlineBuilder(this._fsProvider);
    }
    start() {
        return __awaiter(this, void 0, void 0, function* () {
            this.setOnline(false);
            return this;
        });
    }
    getConnectedServerLabel() {
        if (this._connectServer) {
            return this._connectServer.label;
        }
        return undefined;
    }
    registerCommons() {
        this.registerCommand_RefreshServer();
        this.registerCommand_Connect();
        this.registerCommand_Disconnect();
        this.registerCommand_AddNewServer();
        this.registerCommand_EditServer();
        this.registerCommand_DeleteServer();
        this.registerCommand_NewFile();
        this.registerCommand_ConfigurePattern();
        this.registerCommand_DeleteFile();
        this.registerCommand_Rename();
        this.registerCommand_Logon();
        this.registerCommand_OpenConfig();
        this.registerCommand_OpenBasicConfig();
        this.registerOperation_SortByName();
        this.registerCommand_Search();
        this.registerCreateFiles();
        this.registerOperation_OpenFile_fileLock();
        this.registerOperation_CloseFile_releaseFileLock();
        vscode.commands.executeCommand('setContext', 'onlineEditingEnabled', true);
    }
    registerFileSystem() {
        this._context.subscriptions.push(vscode.workspace.registerFileSystemProvider(env_1.FS_SCHEMA, this._fsProvider, { isCaseSensitive: true }));
    }
    registerTreeProvider() {
        this._context.subscriptions.push(vscode.window.registerTreeDataProvider('online-editing', this._treeProvider));
        this.treeView = vscode.window.createTreeView('online-editing', { treeDataProvider: this._treeProvider });
        this.searchResultsProvider = new fsprovider_1.SearchResultsProvider();
        vscode.window.createTreeView('searchResults', { treeDataProvider: this.searchResultsProvider });
        this.registerCommand_OpenFile();
    }
    registerCreateFiles() {
        this._context.subscriptions.push(vscode.workspace.onDidCreateFiles(td => {
            if (td != undefined) {
                vscode.window.showWarningMessage("Files created are stored on local machine.\nPlease create files from online mode tree view if you want to create them on U2 server.");
            }
        }));
    }
    registerCommand_OpenFile() {
        this._context.subscriptions.push(vscode.commands.registerCommand('extension.openU2File', (uri) => __awaiter(this, void 0, void 0, function* () {
            // Logon to account in case the file is under other account
            const fileUri = uri;
            if (fileUri.scheme !== env_1.FS_SCHEMA) {
                return;
            }
            const fsPath = fileUri.fsPath;
            const parts = fsPath.split("\\");
            if (parts.length <= 3) {
                return;
            }
            const accountName = parts[parts.length - 3];
            mvClient.logon(accountName).then(ret => {
                var _a;
                if (ret === "") {
                    (_a = mvClient.getAccount()) === null || _a === void 0 ? void 0 : _a.setName(accountName);
                    // Load file content
                    this._fsProvider.openFile(uri).then((ret) => {
                        if (ret == 0) {
                            vscode.commands.executeCommand('vscode.open', uri);
                        }
                        else {
                            // Encounter error when open a file
                            let options = { modal: true };
                            switch (ret) {
                                case 29: {
                                    vscode.window.showWarningMessage("Could not apply lock to read BASIC program \'" + parts[parts.length - 1] + "\' in account \'" + accountName + "\'.\nThis file is probably being edited by another user.", options);
                                    break;
                                }
                                default: {
                                    vscode.window.showErrorMessage("Unknown error: could not read BASIC program \'" + parts[parts.length - 1] + "\' in account \'" + accountName + "\'.\nPlease close the file and try again.", options);
                                    break;
                                }
                            }
                        }
                    });
                }
            });
        })));
    }
    registerCommand_RefreshServer() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.refresh', () => __awaiter(this, void 0, void 0, function* () {
            if (ol.hasDuplicateNameServer()) {
                message.show(80002);
            }
            this._treeProvider.refresh();
        })));
    }
    registerCommand_Connect() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.connect', (treeItem) => __awaiter(this, void 0, void 0, function* () {
            if (treeItem === undefined) {
                return;
            }
            const serverName = treeItem.label;
            if (serverName === undefined || serverName.trim().length == 0) {
                return;
            }
            // Find detail information from configuration file
            const nodes = ol.getServers();
            let server = undefined;
            for (const v of nodes) {
                if (v.name === serverName) {
                    server = v;
                }
            }
            if (server === undefined) {
                return;
            }
            // verify configuration content
            let username = server.username;
            if (username === undefined || username.length === 0) {
                username = yield vscode.window.showInputBox({
                    placeHolder: "Please enter user name"
                });
                if (username === undefined) {
                    return;
                }
            }
            let password = server.password;
            if (server.password === undefined || server.password.length === 0) {
                password = yield vscode.window.showInputBox({
                    placeHolder: "Please enter password",
                    password: true
                });
                if (password === undefined) {
                    return;
                }
            }
            let port = server.port;
            if (port === undefined) {
                port = 31438;
            }
            let accountName = "demo";
            if (datasource.isUV()) {
                accountName = "XDEMO";
            }
            mvClient.connect(server.address, username, password, accountName, port.toString(), treeItem.uri.path);
            this._connectServer = treeItem;
        })));
    }
    registerCommand_Disconnect() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.disconnect', () => __awaiter(this, void 0, void 0, function* () {
            mvClient.disconnect();
            this._connectServer = undefined;
            vscode.commands.executeCommand('vscode-rocket.mv.basic.command.online.server.refresh');
        })));
    }
    /**
     * Register command: create a new server entry.
     */
    registerCommand_AddNewServer() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.add', () => __awaiter(this, void 0, void 0, function* () {
            // Open configuration file first
            // then create a new entry
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            extConfig.openConfig(wfs[0].uri.fsPath, extConfig.servers, extConfig.onlineMode);
            extConfig.addNewEntry4OnlineConfig();
        })));
    }
    /**
     * Register command: delete specified server.
     */
    registerCommand_DeleteServer() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.delete', (treeItem) => __awaiter(this, void 0, void 0, function* () {
            extConfig.deleteEntry4OnlineConfig(treeItem);
            this._treeProvider.refresh();
        })));
    }
    /**
     * Register command: edit specified server.
     * Run this command will open configuration file and reveal related configuration.
     */
    registerCommand_EditServer() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.edit', (treeItem) => __awaiter(this, void 0, void 0, function* () {
            if (treeItem === undefined) {
                return;
            }
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            // Open file
            extConfig.openConfig(wfs[0].uri.fsPath, extConfig.servers, extConfig.onlineMode);
            const editor = vscode.window.activeTextEditor;
            if (editor === undefined) {
                return;
            }
            const document = editor.document;
            // Find entry line number
            const servername = treeItem.label;
            const text = document.getText();
            const lineNumber = text.split('\n').findIndex(line => line.includes(servername));
            vscode.workspace.openTextDocument(document.uri).then(document1 => {
                vscode.window.showTextDocument(document1).then(editor => {
                    const line = editor.document.lineAt(lineNumber);
                    const range = line.range;
                    editor.selection = new vscode.Selection(range.start, range.end);
                    editor.revealRange(editor.selection, vscode.TextEditorRevealType.Default);
                });
            });
            return;
        })));
    }
    registerCommand_NewFile() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.add.file', (item) => __awaiter(this, void 0, void 0, function* () {
            let fileName = yield vscode.window.showInputBox({ placeHolder: 'Please enter file name' });
            if (fileName !== undefined) {
                const regex = /^[^\s<>:"/\\|?*]*$/;
                if (regex.test(fileName)) {
                    const uri = vscode.Uri.joinPath(item.uri, fileName.trim());
                    yield this._fsProvider.writeFile2(uri, Buffer.from(""), { create: true, overwrite: true });
                    this._treeProvider.refresh();
                    vscode.commands.executeCommand('vscode.open', uri);
                }
                else {
                    message.show(80003);
                }
            }
        })));
    }
    registerCommand_ConfigurePattern() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.configure.pattern', (item) => __awaiter(this, void 0, void 0, function* () {
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            var serverLabel = item instanceof u2tree_1.U2Server ? item.label : extension_1.online.getConnectedServerLabel();
            var node = item.uri.path;
            if (serverLabel === undefined) {
                return;
            }
            var isChanged = this.addNewFilterPattern(node, wfs[0].uri.fsPath, serverLabel);
            const configJson = extConfig.read(wfs[0].uri.fsPath, extConfig.servers, extConfig.onlineMode);
            const server = configJson.servers.find((srv) => srv.name === serverLabel);
            if (server.filterPatterns.length > 0) {
                const configPath = extConfig.getPath(wfs[0].uri.fsPath, extConfig.onlineMode);
                const fullPath = path.join(configPath, extConfig.servers);
                const uri = vscode.Uri.file(fullPath);
                const document = yield vscode.workspace.openTextDocument(uri);
                yield vscode.window.showTextDocument(document);
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    const position = this.findPositionForCursor(node, server.filterPatterns, document);
                    if (position) {
                        editor.selection = new vscode.Selection(position.start, position.end);
                        editor.revealRange(new vscode.Selection(position.start, position.end));
                    }
                }
            }
            if (isChanged) {
                if (mvClient.isConnected()) {
                    message.show(80004);
                }
            }
        })));
    }
    addNewFilterPattern(folderPath, configFilePath, serverName) {
        const newPattern = { path: folderPath, value: "*" };
        const configJson = extConfig.read(configFilePath, extConfig.servers, extConfig.onlineMode);
        const server = configJson.servers.find((srv) => srv.name === serverName);
        var isChanged = false;
        if (server) {
            if (!server.filterPatterns) {
                server.filterPatterns = [];
            }
            const existingPatternIndex = server.filterPatterns.findIndex((pattern) => pattern.path === folderPath);
            if (existingPatternIndex === -1) {
                server.filterPatterns.push(newPattern);
                isChanged = true;
            }
        }
        if (isChanged) {
            extConfig.write(configFilePath, extConfig.servers, configJson, extConfig.onlineMode);
        }
        return isChanged;
    }
    findPositionForCursor(folderPath, filterPatterns, document) {
        for (const pattern of filterPatterns) {
            if (pattern.path === folderPath) {
                for (let i = 0; i < document.lineCount; i++) {
                    const lineText = document.lineAt(i).text;
                    if (lineText.includes(`"${pattern.path}"`)) {
                        const valueLine = i + 1;
                        const valueText = document.lineAt(valueLine).text;
                        const valueStartIndex = valueText.indexOf(':');
                        const startChar = valueText.indexOf('"', valueStartIndex) + 1;
                        const endChar = valueText.indexOf('"', startChar);
                        const startPos = new vscode.Position(valueLine, startChar);
                        const endPos = new vscode.Position(valueLine, endChar);
                        return { start: startPos, end: endPos };
                    }
                }
            }
        }
        return null;
    }
    registerCommand_Rename() {
        // Not implemented
    }
    registerCommand_DeleteFile() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.delete.file', (item) => __awaiter(this, void 0, void 0, function* () {
            const CONFIRM_DELETE = "Do you want to delete file \"${fileName}\" ?";
            const confirmMsg = CONFIRM_DELETE.replace("${fileName}", item.label);
            let options = { modal: true };
            let itemYes = "Yes";
            vscode.window.showWarningMessage(confirmMsg, options, itemYes).then((opt) => {
                if (opt === itemYes) {
                    this._fsProvider.delete(item.uri).then(() => {
                        this._treeProvider.refresh();
                        (0, editor_1.closeTab)(item.label);
                    });
                }
            });
        })));
    }
    updateTree() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this._connectServer === undefined) {
                return;
            }
            vscode.commands.executeCommand('vscode-rocket.mv.basic.command.online.server.refresh');
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            this._fsProvider.clear();
            this._treeProvider.refresh();
            this._connectServer = undefined;
        });
    }
    registerCommand_Logon() {
        this._context.subscriptions.push(vscode.commands.registerCommand('extension.logon', (item) => __awaiter(this, void 0, void 0, function* () {
            if (item) {
                const uri = item;
                const fsPath = uri.fsPath;
                const parts = fsPath.split("\\");
                if (parts.length == 0) {
                    return;
                }
                const accountName = parts[parts.length - 1];
                if (accountName) {
                    mvClient.logon(accountName).then(ret => {
                        var _a;
                        if (ret === "") {
                            (_a = mvClient.getAccount()) === null || _a === void 0 ? void 0 : _a.setName(accountName);
                            vscode.commands.executeCommand('vscode-rocket.mv.basic.command.online.server.refresh');
                        }
                    });
                }
            }
        })));
    }
    registerCommand_Init() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.server.init', () => __awaiter(this, void 0, void 0, function* () {
            const wsfs = vscode.workspace.workspaceFolders;
            if (wsfs === undefined) {
                return;
            }
            const workspace = wsfs[0];
            extConfig.create(workspace.uri.fsPath, extConfig.servers, extConfig.onlineMode);
            extConfig.create(workspace.uri.fsPath, extConfig.basic_config, extConfig.onlineMode);
            this._treeProvider.refresh();
        })));
    }
    registerCommand_OpenConfig() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.config.open', () => __awaiter(this, void 0, void 0, function* () {
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            // Open file
            extConfig.openConfig(wfs[0].uri.fsPath, extConfig.servers, extConfig.onlineMode);
        })));
    }
    registerCommand_OpenBasicConfig() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.basic.config.open', () => __awaiter(this, void 0, void 0, function* () {
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            // Open file
            extConfig.openConfig(wfs[0].uri.fsPath, extConfig.basic_config, extConfig.onlineMode);
        })));
    }
    registerCommand_Search() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.search', (item) => __awaiter(this, void 0, void 0, function* () {
            const wfs = vscode.workspace.workspaceFolders;
            if (wfs === undefined) {
                return;
            }
            var searchTerm = yield vscode.window.showInputBox({
                prompt: 'Enter search pattern',
                placeHolder: 'e.g., ?FILE* ',
            });
            const uri = item.uri;
            if (searchTerm && uri) {
                if (!searchTerm.includes("*") && !searchTerm.includes("?")) {
                    searchTerm += "*";
                }
                const results = this._fsProvider.findFiles(uri, searchTerm);
                if (results.length > 0 && this.searchResultsProvider) {
                    this.searchResultsProvider.setSearchResults(results, uri.path);
                    vscode.window.showInformationMessage(`Found ${results.length} file(s)`);
                }
                else {
                    vscode.window.showInformationMessage(`No files found matching "${searchTerm}"`);
                }
            }
        })));
    }
    registerOperation_OpenFile_fileLock() {
        this._context.subscriptions.push(vscode.workspace.onDidOpenTextDocument(td => {
            const fsPath = td.uri.fsPath;
            if (td.uri.scheme === env_1.FS_SCHEMA) {
                // In online mode, when open a file, use rocket-mv-basic as default language
                vscode.languages.setTextDocumentLanguage(td, "rocket-mvbasic");
                const dirName = this.retrieveDirName(fsPath);
                const fileName = this.retrieveFileName(fsPath);
                if (dirName === undefined || fileName === undefined) {
                    return;
                }
                mvClient.fileLock(dirName, fileName);
            }
            if (td.uri.scheme === "debug") {
                this.storeBreakpoints(fsPath);
                setTimeout(() => this.applyBreakpoints(td.uri), 500);
                vscode.languages.setTextDocumentLanguage(td, "rocket-mvbasic");
            }
        }));
        this._context.subscriptions.push(vscode.debug.onDidChangeBreakpoints(event => {
            if (event.added.length > 0) {
                event.added.forEach(bp => {
                    if (bp instanceof vscode.SourceBreakpoint) {
                        const debugFileUri = bp.location.uri;
                        if (debugFileUri.scheme === "debug") {
                            const originalFileUri = this.getOriginalFileUri(debugFileUri);
                            if (originalFileUri) {
                                this.mapBreakpointsToOriginal(originalFileUri, debugFileUri);
                            }
                        }
                    }
                });
            }
            if (event.removed.length > 0) {
                event.removed.forEach(bp => {
                    if (bp instanceof vscode.SourceBreakpoint) {
                        const debugFileUri = bp.location.uri;
                        if (debugFileUri.scheme === "debug") {
                            const originalFileUri = this.getOriginalFileUri(debugFileUri);
                            if (originalFileUri) {
                                this.removeBreakpointsFromOriginal(originalFileUri, bp);
                            }
                        }
                    }
                });
            }
        }));
        this._context.subscriptions.push(vscode.workspace.onDidCloseTextDocument(td => {
            if (td.uri.scheme === "debug") {
                this.resetBreakpoints(td.uri);
            }
        }));
    }
    getOriginalFileUri(debugFileUri) {
        const debugFileName = path.basename(debugFileUri.fsPath).toLowerCase();
        const matchingFile = vscode.workspace.textDocuments.find(doc => path.basename(doc.uri.fsPath).toLowerCase() === debugFileName && doc.uri.scheme !== "debug");
        return matchingFile ? matchingFile.uri : undefined;
    }
    mapBreakpointsToOriginal(originalFileUri, debugFileUri) {
        const debugBreakpoints = vscode.debug.breakpoints.filter(bp => bp instanceof vscode.SourceBreakpoint && bp.location.uri.toString() === debugFileUri.toString());
        if (debugBreakpoints.length === 0) {
            return;
        }
        const existingOriginalBreakpoints = vscode.debug.breakpoints.filter(bp => bp instanceof vscode.SourceBreakpoint && bp.location.uri.toString() === originalFileUri.toString());
        const existingLines = new Set(existingOriginalBreakpoints.map(bp => bp.location.range.start.line));
        const newMappedBreakpoints = debugBreakpoints
            .filter(bp => !existingLines.has(bp.location.range.start.line)) // Avoid remapping existing ones
            .map(bp => {
            const location = new vscode.Location(originalFileUri, new vscode.Position(bp.location.range.start.line, bp.location.range.start.character));
            return new vscode.SourceBreakpoint(location, bp.enabled);
        });
        if (newMappedBreakpoints.length > 0) {
            vscode.debug.addBreakpoints(newMappedBreakpoints);
        }
    }
    removeBreakpointsFromOriginal(originalFileUri, removedBreakpoint) {
        if (this.isResettingBreakpoints) {
            return;
        }
        const matchingBreakpoints = vscode.debug.breakpoints.filter(bp => bp instanceof vscode.SourceBreakpoint &&
            bp.location.uri.toString() === originalFileUri.toString() &&
            bp.location.range.start.line === removedBreakpoint.location.range.start.line);
        if (matchingBreakpoints.length > 0) {
            vscode.debug.removeBreakpoints(matchingBreakpoints);
        }
    }
    resetBreakpoints(debugFileUri) {
        return __awaiter(this, void 0, void 0, function* () {
            this.isResettingBreakpoints = true;
            const breakpointsToRemove = vscode.debug.breakpoints.filter(bp => bp instanceof vscode.SourceBreakpoint &&
                bp.location.uri.toString() === debugFileUri.toString());
            if (breakpointsToRemove.length > 0) {
                vscode.debug.removeBreakpoints(breakpointsToRemove);
                yield new Promise(resolve => setTimeout(resolve, 300));
            }
            this.isResettingBreakpoints = false;
        });
    }
    storeBreakpoints(fsPath) {
        const expectedFileName = path.basename(fsPath).toLowerCase();
        storedBreakpoints = vscode.debug.breakpoints
            .filter(bp => bp instanceof vscode.SourceBreakpoint)
            .map(bp => bp)
            .filter(bp => {
            const breakpointPath = bp.location.uri.fsPath;
            const breakpointFileName = path.basename(breakpointPath).toLowerCase();
            return breakpointFileName === expectedFileName;
        })
            .map(bp => ({
            uri: bp.location.uri.fsPath,
            line: bp.location.range.start.line,
            column: bp.location.range.start.character
        }));
    }
    applyBreakpoints(fileUri) {
        const breakpointsToAdd = storedBreakpoints.map(storedBp => {
            const location = new vscode.Location(fileUri, new vscode.Position(storedBp.line, storedBp.column));
            return new vscode.SourceBreakpoint(location, true);
        });
        vscode.debug.addBreakpoints(breakpointsToAdd);
    }
    registerOperation_CloseFile_releaseFileLock() {
        this._context.subscriptions.push(vscode.workspace.onDidCloseTextDocument(td => {
            if (td.uri.scheme === env_1.FS_SCHEMA) {
                const fsPath = td.uri.fsPath;
                const dirName = this.retrieveDirName(fsPath);
                const fileName = this.retrieveFileName(fsPath);
                if (dirName === undefined || fileName === undefined) {
                    return;
                }
                mvClient.releaseFileLock(dirName, fileName);
            }
        }));
    }
    /**
     * Sort BASIC program files by file name
     */
    registerOperation_SortByName() {
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.editing.sort.by.name.ascending', item => {
            this._fsProvider.setSort(true);
            this._treeProvider.refresh();
        }));
        this._context.subscriptions.push(vscode.commands.registerCommand('vscode-rocket.mv.basic.command.online.editing.sort.by.name.descending', item => {
            this._fsProvider.setSort(false);
            this._treeProvider.refresh();
        }));
    }
    hasSameAddress(address) {
        if (!this._connectServer) {
            return false;
        }
        if (this._connectServer.address === address) {
            return true;
        }
        return false;
    }
    setOnline(online) {
        vscode.commands.executeCommand("setContext", "online", online);
    }
    writeDirRecord(filePath, content) {
        let parts = filePath.split("\\");
        if (parts.length < 3) {
            parts = filePath.split("/");
            if (parts.length < 3) {
                return;
            }
        }
        const fileName = parts[parts.length - 1];
        const dirName = parts[parts.length - 2];
        const accountName = parts[2];
        mvClient.logon(accountName).then(() => {
            mvClient.writeDirRecord(accountName, dirName, fileName, content).then((msg) => {
                if (msg != undefined && msg.length != 0) {
                    vscode.window.showErrorMessage(msg);
                }
            });
        });
    }
    addFile(uri) {
        this._fsProvider.addFile(uri, Buffer.from(""), { create: true, overwrite: true });
        this._fsProvider.openFile(uri);
    }
    retrieveDirName(filePath) {
        let parts = filePath.split("\\");
        if (parts.length < 2) {
            parts = filePath.split("/");
            if (parts.length < 2) {
                return;
            }
        }
        return parts[parts.length - 2];
    }
    retrieveFileName(filePath) {
        let parts = filePath.split("\\");
        if (parts.length < 2) {
            parts = filePath.split("/");
            if (parts.length < 2) {
                return;
            }
        }
        return parts[parts.length - 1];
    }
    getAccountUri() {
        const rootUri = vscode.Uri.parse(env_1.FS_SCHEMA + ':/');
        const account = mvClient.getAccount();
        if (account !== undefined) {
            return vscode.Uri.joinPath(rootUri, account.address, account.getName());
        }
    }
    getAccount() {
        return mvClient.getAccount();
    }
}
exports.Online = Online;
//# sourceMappingURL=online.js.map