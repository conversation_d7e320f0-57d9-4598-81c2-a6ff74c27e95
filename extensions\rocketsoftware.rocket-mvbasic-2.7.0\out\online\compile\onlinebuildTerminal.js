"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineBuildTaskTerminal = void 0;
const buildTerminal_1 = require("../../compile/buildTerminal");
class OnlineBuildTaskTerminal extends buildTerminal_1.BasicBuildTaskTerminal {
    constructor(_files, definition, bCatalog) {
        super(_files, definition, bCatalog, undefined);
        this._files = _files;
    }
    doBuild() {
        return __awaiter(this, void 0, void 0, function* () {
            for (const file of this.files) {
                yield this.build(file);
            }
            this.showResult();
        });
    }
}
exports.OnlineBuildTaskTerminal = OnlineBuildTaskTerminal;
//# sourceMappingURL=onlinebuildTerminal.js.map