{"version": 3, "file": "message.js", "sourceRoot": "", "sources": ["../src/message.ts"], "names": [], "mappings": ";;;;;;;;;;;AAkDA,kBAMC;AAOD,gCAOC;AAED,4CAuBC;AAED,4BAkBC;AAMD,oBAqBC;AAOD,oBAwBC;AA7KD,iCAAiC;AA2CjC,IAAI,QAAQ,GAA0B,SAAS,CAAC;AAEhD;;;;GAIG;AACH,SAAgB,GAAG,CAAC,EAAU;IAC1B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QACzB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,EAAU;IACjC,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACpB,OAAO,GAAG,CAAC,OAAO,CAAC;IACvB,CAAC;IAED,OAAO,kCAAkC,CAAC;AAC9C,CAAC;AAED,SAAgB,gBAAgB,CAAC,EAAU;IACvC,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACpB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,MAAM,UAAU,GAAG;QACf,OAAO;QACP,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,eAAe;QACf,MAAM;QACN,OAAO;QACP,SAAS;QACT,eAAe;KAClB,CAAC;IAEF,IAAI,EAAE,IAAI,UAAU,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QACpC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,OAAO,UAAU,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;AAED,SAAgB,QAAQ,CAAC,EAAU;IAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACpB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,MAAM,MAAM,GAAG;QACX,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,UAAU;KACb,CAAC;IACF,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QAChC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC;AAED;;;GAGG;AACH,SAAsB,IAAI,CAAC,GAA4B;;QACnD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,eAAe,CAAC;QACpC,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACnF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC3E,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;YACvC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iBAAiB,WAAW,IAAI,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO;QACX,CAAC;IACL,CAAC;CAAA;AAED;;;;GAIG;AACH,SAAgB,IAAI,CAAC,EAAU;IAC3B,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACnC,OAAO;IACX,CAAC;IAED,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAC9B,QAAQ,KAAK,EAAE,CAAC;QACZ,KAAK,CAAC;YACF,kCAAkC;YAClC,MAAM;QACV,KAAK,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM;QACV,KAAK,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM;QACV,KAAK,CAAC,CAAC;QACP,KAAK,CAAC;YACF,kCAAkC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM;IACd,CAAC;AACL,CAAC"}