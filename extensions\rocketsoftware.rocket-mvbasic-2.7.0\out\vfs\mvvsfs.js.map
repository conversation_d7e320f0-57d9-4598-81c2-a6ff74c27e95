{"version": 3, "file": "mvvsfs.js", "sourceRoot": "", "sources": ["../../src/vfs/mvvsfs.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAC7B,yBAAyB;AACzB,iDAAiD;AACjD,6CAAoD;AAEpD,MAAa,cAAc;IAA3B;QAEY,aAAQ,GAAG,IAAI,MAAM,CAAC,YAAY,EAA4B,CAAC;QAC9D,oBAAe,GAA2C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;IAqG3F,CAAC;IAnGG,KAAK,CAAC,GAAe,EAAE,OAAoD;QACvE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,CAAC,GAAe;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAChD,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAErC,OAAO;YACH,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,CAAC;YAC5B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;YAC7B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;YAC7B,IAAI,EAAE,MAAM,CAAC,IAAI;SACpB,CAAC;IACN,CAAC;IAED,aAAa,CAAC,GAAe;QACzB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED,eAAe,CAAC,GAAe;QAC3B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED,QAAQ,CAAC,GAAe;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAChD,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,OAAO,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC1C,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CACvC,UAAU,IAAI,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC,CAAC;IACP,CAAC;IAED,SAAS,CAAC,GAAe,EAAE,OAAmB,EAAE,OAAiD;QAC7F,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,GAAe,EAAE,OAAgC;QACpD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,MAAkB,EAAE,MAAkB,EAAE,OAAgC;QAC3E,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAEO,OAAO,CAAC,MAAkB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEO,YAAY;QAChB,MAAM,SAAS,GAAG,IAAA,yBAAgB,GAAE,CAAC;QACrC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAc,CAAC;QACrC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAc,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvE,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,WAAW;QACf,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,MAAM,CAAC,EAAE,CAAC;IACrB,CAAC;CACJ;AAxGD,wCAwGC"}