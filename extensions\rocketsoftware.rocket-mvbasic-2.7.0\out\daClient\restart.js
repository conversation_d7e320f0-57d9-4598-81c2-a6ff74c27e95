"use strict";
/**
 *  restart - restart debug related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateTimestamp = updateTimestamp;
exports.isRunning = isRunning;
exports.saveDebugFiles = saveDebugFiles;
exports.getDebugFiles = getDebugFiles;
let timestamp_debug_stop = -1;
const RESTART_TIME_INTERVAL = 1500;
let debugFiles = [];
function updateTimestamp() {
    timestamp_debug_stop = Date.now();
}
function isRunning() {
    const current_timestamp = Date.now();
    if (current_timestamp - timestamp_debug_stop > RESTART_TIME_INTERVAL) {
        return false;
    }
    return true;
}
function saveDebugFiles(files) {
    debugFiles = files;
}
function getDebugFiles() {
    return debugFiles;
}
//# sourceMappingURL=restart.js.map