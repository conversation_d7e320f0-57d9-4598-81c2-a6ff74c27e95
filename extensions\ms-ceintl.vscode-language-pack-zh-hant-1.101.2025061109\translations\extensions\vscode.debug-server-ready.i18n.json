{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Format uri ('{0}') must contain exactly one substitution placeholder.": "格式 URI ('{0}’) 必須剛好包含一個替代預留位置。", "Format uri ('{0}') uses a substitution placeholder but pattern did not capture anything.": "格式 URI (’{0}’) 使用替代預留位置，但模式並未擷取任何內容。"}, "package": {"debug.server.ready.action.debugWithChrome.description": "使用 'Debugger for Chrome’ 開始偵錯。", "debug.server.ready.action.description": "當伺服器已就緒時，如何處理 URI。", "debug.server.ready.action.openExternally.description": "使用預設應用程式在外部開啟 URI。", "debug.server.ready.action.startDebugging.description": "執行其他啟動組態。", "debug.server.ready.debugConfig.description": "要執行的偵錯組態。", "debug.server.ready.debugConfigName.description": "要執行的啟動組態名稱。", "debug.server.ready.killOnServerStop.description": "當父代工作階段停止時，停止子系工作階段。", "debug.server.ready.pattern.description": "若此模式出現在偵錯主控台上，表示伺服器已就緒。第一個擷取的群組必須包含 URI 或連接埠號碼。", "debug.server.ready.serverReadyAction.description": "當正在偵錯的伺服器程式已就緒後，對 URL 採取動作 (作用方式是傳送輸出給偵錯主控台，格式為 'listening on port 3000' 或 'Now listening on: https://localhost:5001'。)", "debug.server.ready.uriFormat.description": "從連接埠號碼建構 URI 時使用的格式字串。第一個 '%s’ 會替代為連接埠號碼。", "debug.server.ready.webRoot.description": "傳遞給 ‘Debugger for Chrome’ 偵錯組態的值。", "description": "若正在進行偵錯的伺服器已就緒，則在瀏覽器中開啟 URI。", "displayName": "伺服器就緒動作"}}}