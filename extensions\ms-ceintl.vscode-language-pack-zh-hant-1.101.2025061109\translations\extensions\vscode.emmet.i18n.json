{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Emmet Abbreviation": "Emmet 縮寫", "Enter Abbreviation": "輸入縮寫", "Invalid emmet.variables field. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example.": "emmet.variables 欄位無效。請參閱 https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration 以取得有效的範例。", "Invalid snippets file. See https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets for a valid example.": "程式碼片段檔案無效。請參閱 https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets 以取得有效的範例。", "Invalid syntax profile. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example.": "語法設定檔無效。請參閱 https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration 以取得有效的範例。"}, "package": {"command.balanceIn": "平衡(向內)", "command.balanceOut": "平衡(向外)", "command.decrementNumberByOne": "依 1 遞減", "command.decrementNumberByOneTenth": "依 0.1 遞減", "command.decrementNumberByTen": "依 10 遞減", "command.evaluateMathExpression": "評估數學運算式", "command.incrementNumberByOne": "依 1 遞增", "command.incrementNumberByOneTenth": "依 0.1 遞增", "command.incrementNumberByTen": "依 10 遞增", "command.matchTag": "前往相符的配對", "command.mergeLines": "合併行", "command.nextEditPoint": "移至下一個編輯端點", "command.prevEditPoint": "移至上一個編輯端點", "command.reflectCSSValue": "反射 CSS 值", "command.removeTag": "移除標籤", "command.selectNextItem": "選取下一個項目", "command.selectPrevItem": "選取上一個項目", "command.showEmmetCommands": "顯示 Emmet 命令", "command.splitJoinTag": "分割/聯結標籤", "command.toggleComment": "切換註解", "command.updateImageSize": "更新影像大小", "command.updateTag": "更新標籤", "command.wrapWithAbbreviation": "使用縮寫換行", "description": "VS Code 的 Emmet 支援", "emmetExclude": "不應展開 Emmet 縮寫的語言陣列。", "emmetExtensionsPath": "路徑陣列，其中每個路徑可以包含 Emmet syntaxProfiles 和/或程式碼片段檔案。\r\n發生衝突時，稍後路徑的設定檔/程式碼片段會覆蓋先前路徑的設定檔/程式碼片段。\r\n請參閱 https://code.visualstudio.com/docs/editor/emmet，以了解更多資訊和範例程式碼片段檔案。", "emmetExtensionsPathItem": "包含 Emmet syntaxProfiles 和/或程式碼片段的路徑。", "emmetIncludeLanguages": "在預設不支援的語言中啟用 Emmet 縮寫。在這裡新增此語言與支援 Emmet 之語言間的對應。\r\n例如: `{\"vue-html\": \"html\", \"javascript\": \"javascriptreact\"}`", "emmetOptimizeStylesheetParsing": "當設定為 `false` 時，會剖析整個檔案以判斷目前的位置是否適用於展開 Emmet 縮寫。當設定為 `true` 時，只會剖析 CSS/SCSS/Less 檔案中目前位置周圍的內容。", "emmetPreferences": "喜好設定，用以修改某些動作的行為及 Emmet 的解析程式。", "emmetPreferencesAllowCompactBoolean": "若為 `true`，則產生布林值屬性的精簡標記法。", "emmetPreferencesBemElementSeparator": "使用 BEM 篩選時用於類別的元素分隔符號。", "emmetPreferencesBemModifierSeparator": "使用 BEM 篩選時用於類別的修飾詞分隔符號。", "emmetPreferencesCssAfter": "展開 CSS 縮寫時，要放在 CSS 屬性結尾的符號。", "emmetPreferencesCssBetween": "展開 CSS 縮寫時，要放在 CSS 屬性與值之間的符號。", "emmetPreferencesCssColorShort": "若為 'true'，則諸如 `#f` 的色彩值將會展開為 `#fff` 而非 `#ffffff`。", "emmetPreferencesCssFuzzySearchMinScore": "模糊比對的縮寫應該達到最低分數(從 0 到 1)。較低數值可能產生錯誤的比對，較高的數值可能會降低可能的比對。", "emmetPreferencesCssMozProperties": "在以 `-` 開頭的 Emmet 縮寫中使用時，會取得 'moz' 廠商前綴的逗點分隔 CSS 屬性。設定為空白字串可避免總是取得 'moz' 前綴。", "emmetPreferencesCssMsProperties": "在以 `-` 開頭的 Emmet 縮寫中使用時，會取得 'ms' 廠商前綴的逗點分隔 CSS 屬性。設定為空白字串可避免總是取得 'ms' 前綴。", "emmetPreferencesCssOProperties": "在以 `-` 開頭的 Emmet 縮寫中使用時，會取得 'o' 廠商前綴的逗點分隔 CSS 屬性。設定為空白字串可避免總是取得 'o' 前綴。", "emmetPreferencesCssWebkitProperties": "在以 `-` 開頭的 Emmet 縮寫中使用時，會取得 'webkit' 廠商前綴的逗點分隔 CSS 屬性。設定為空白字串可避免總是取得 'webkit' 前綴。", "emmetPreferencesFilterCommentAfter": "套用註解篩選時必須置於相符元素後的註解定義。 ", "emmetPreferencesFilterCommentBefore": "套用註解篩選時必須置於相符元素前的註解定義。", "emmetPreferencesFilterCommentTrigger": "必須採用縮寫以套用註解篩選的屬性名稱逗點分隔清單。", "emmetPreferencesFloatUnit": "浮點值的預設單位。", "emmetPreferencesFormatForceIndentTags": "陣列的標籤名稱應總是向內縮排。", "emmetPreferencesFormatNoIndentTags": "陣列的標籤名稱一律不應向內縮排。", "emmetPreferencesIntUnit": "整數值的預設單位。", "emmetPreferencesOutputInlineBreak": "要在這些元素間放置之分行符號所需的同層級內嵌元素數目。如果是 `0`，則內嵌元素永遠會展開到單一行。", "emmetPreferencesOutputReverseAttributes": "若為 `true`，則會在解析程式碼片段時反轉屬性的合併方向。", "emmetPreferencesOutputSelfClosingStyle": "自我關閉標籤的樣式: html ('<br>')、xml ('<br/>') 或 xhtml ('<br />')。", "emmetPreferencesSassAfter": "在 SASS 檔案中展開 CSS 縮寫時，要放在 CSS 屬性結尾的符號。", "emmetPreferencesSassBetween": "在 SASS 檔案中展開 CSS 縮寫時，要放在 CSS 屬性與值之間的符號。", "emmetPreferencesStylusAfter": "在手寫筆檔案中展開 CSS 縮寫時，要放在 CSS 屬性結尾的符號。", "emmetPreferencesStylusBetween": "在手寫筆檔案中展開 CSS 縮寫時，要放在 CSS 屬性與值之間的符號。", "emmetShowAbbreviationSuggestions": "依建議顯示可能的 Emmet 縮寫。在樣式表內或在 emmet.showExpandedAbbreviation 設為 `\"never\"` 時不適用。", "emmetShowExpandedAbbreviation": "依建議顯示展開的 Emmet 縮寫。\r\n選項 `\"inMarkupAndStylesheetFilesOnly\"` 適用於 html、haml、jade、slim、xml、xsl、css、scss、sass、less 和 stylus。\r\n選項 `\"always\"` 適用於檔案的所有部分，不管有無標記/css。", "emmetShowSuggestionsAsSnippets": "若為 true，則 Emmet 建議會顯示為程式碼片段，可讓您依據 `#editor.snippetSuggestions#` 加以排序。", "emmetSyntaxProfiles": "為指定的語法定義設定檔，或透過特定規則使用自己的設定檔。", "emmetTriggerExpansionOnTab": "啟用時，即使完成未顯示，按 TAB 鍵時，也會展開 Emmet 縮寫。停用時，仍可透過按 TAB 來接受顯示完成。", "emmetUseInlineCompletions": "如果為 'true'，Emmet 將會使用內嵌完成來建議擴充。若要避免非內嵌完成項目提供者在此設定為 'true' 時頻繁顯示，請針對 'other' 項目將 '#editor.quickSuggestions#' 設為 'inline' 或 'off'。", "emmetVariables": "在 Emmet 程式碼片段中使用的變數。"}}}