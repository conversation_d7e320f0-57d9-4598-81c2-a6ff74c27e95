{"name": "vscode-speech-language-pack-zh-tw", "displayName": "Chinese (Traditional, Taiwan) language support for VS Code Speech", "version": "0.5.0", "icon": "logo.png", "publisher": "ms-vscode", "author": {"name": "Microsoft Corporation"}, "description": "Chinese (Traditional, Taiwan) language support for speech-to-text and other voice capabilities in VS Code.", "license": "SEE LICENSE IN LICENSE.txt", "engines": {"vscode": "^1.87.0"}, "extensionDependencies": ["ms-vscode.vscode-speech"], "contributes": {"vscodeSpeechModels": [{"version": "2", "modelName": "Microsoft Speech Recognizer zh-TW FP Model V2.1", "modelPath": "./assets/stt", "locale": "zh-TW"}], "vscodeSynthesizerModels": [{"version": "2", "modelName": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, YunJheNeural)", "modelPath": "./assets/tts", "locale": "zh-TW"}]}, "keywords": ["Accessibility", "a11y", "STT", "ai", "co-pilot", "Cha<PERSON>", "Voice", "Transcription", "Microsoft", "multi-root ready"], "categories": ["Other"], "private": true, "homepage": "https://github.com/microsoft/vscode/wiki/VS-Code-Speech", "bugs": {"url": "https://github.com/Microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Aworkbench-voice"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-speech-assets"}, "extensionKind": ["ui"], "__metadata": {"installedTimestamp": 1748830790732, "targetPlatform": "undefined", "size": 170267721}}