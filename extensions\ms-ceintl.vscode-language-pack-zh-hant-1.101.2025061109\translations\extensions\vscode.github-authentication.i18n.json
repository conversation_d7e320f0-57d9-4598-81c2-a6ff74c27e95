{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Continue to GitHub": "繼續前往 GitHub", "Continue to GitHub to create a Personal Access Token (PAT)": "繼續前往 GitHub 以建立個人存取權杖 (PAT)", "Copy & Continue to GitHub": "複製並繼續前往 GitHub", "GitHub Enterprise Server URI is not a valid URI: {0}": "GitHub Enterprise 伺服器 URI 不是有效的 URI: {0}", "Having trouble logging in? Would you like to try a different way? ({0})": "登入時遇到問題嗎? 要嘗試其他方式嗎? ({0})", "No": "否", "Open [{0}]({0}) in a new tab and paste your one-time code: {1}/The [{0}]({0}) will be a url and the {1} will be a code, e.g. 123-456{Locked=\"[{0}]({0})\"}": "在新索引標籤中開啟 [{0}]({0}) 並貼上您的一次性代碼: {1}", "Sign in failed: {0}": "登入失敗: {0}", "Sign out failed: {0}": "登出失敗: {0}", "Signing in to {0}.../The {0} will be a url, e.g. github.com": "正在登入 {0}...", "To finish authenticating, navigate to GitHub and paste in the above one-time code.": "若要完成驗證，請瀏覽至 GitHub 並貼上上述的一次性程式碼。", "To finish authenticating, navigate to GitHub to create a PAT then paste the PAT into the input box.": "若要完成驗證，請瀏覽至 GitHub 以建立 PAT，然後將 PAT 貼到輸入方塊中。", "Yes": "是", "You have not yet finished authorizing this extension to use GitHub. Would you like to try a different way? ({0})": "您尚未完成授權此延伸模組使用 GitHub。要嘗試其他方式嗎? ({0})", "Your Code: {0}/The {0} will be a code, e.g. 123-456": "您的代碼: {0}", "device code": "裝置代碼", "local server": "本機伺服器", "personal access token": "個人存取權杖", "url handler": "URL 處理常式"}, "package": {"config.github-enterprise.title": "GHE.com 與GitHub Enterprise伺服器驗證", "config.github-enterprise.uri.description": "GHE.com 或 GitHub Enterprise Server 實例的 URI。\r\n\r\n例子：\r\n* GHE.com： 'https://octocat.ghe.com`\r\n* GitHub Enterprise 伺服器： 'https://github.octocat.com`\r\n\r\n> **注意：** 這應該將 _not_ 設為 GitHub.com URI。如果您的帳戶存在於 GitHub.com 或是GitHub Enterprise的受管理使用者，您不需要任何其他設定，只需登入 GitHub 即可。", "description": "GitHub 驗證提供者", "displayName": "GitHub 驗證"}}}