"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.recognize = exports.KeywordRecognitionStatusCode = exports.createSynthesizer = exports.SynthesizerStatusCode = exports.createTranscriber = exports.TranscriptionStatusCode = exports.speechapi = void 0;
exports.speechapi = require('bindings')('speechapi.node');
//#region Transcription
var TranscriptionStatusCode;
(function (TranscriptionStatusCode) {
    TranscriptionStatusCode[TranscriptionStatusCode["STARTED"] = 1] = "STARTED";
    TranscriptionStatusCode[TranscriptionStatusCode["RECOGNIZING"] = 2] = "RECOGNIZING";
    TranscriptionStatusCode[TranscriptionStatusCode["RECOGNIZED"] = 3] = "RECOGNIZED";
    TranscriptionStatusCode[TranscriptionStatusCode["NOT_RECOGNIZED"] = 4] = "NOT_RECOGNIZED";
    TranscriptionStatusCode[TranscriptionStatusCode["INITIAL_SILENCE_TIMEOUT"] = 5] = "INITIAL_SILENCE_TIMEOUT";
    TranscriptionStatusCode[TranscriptionStatusCode["END_SILENCE_TIMEOUT"] = 6] = "END_SILENCE_TIMEOUT";
    TranscriptionStatusCode[TranscriptionStatusCode["SPEECH_START_DETECTED"] = 7] = "SPEECH_START_DETECTED";
    TranscriptionStatusCode[TranscriptionStatusCode["SPEECH_END_DETECTED"] = 8] = "SPEECH_END_DETECTED";
    TranscriptionStatusCode[TranscriptionStatusCode["STOPPED"] = 9] = "STOPPED";
    TranscriptionStatusCode[TranscriptionStatusCode["DISPOSED"] = 10] = "DISPOSED";
    TranscriptionStatusCode[TranscriptionStatusCode["ERROR"] = 11] = "ERROR";
})(TranscriptionStatusCode || (exports.TranscriptionStatusCode = TranscriptionStatusCode = {}));
function createTranscriber({ modelPath, modelName, modelKey, phrases, logsPath }, callback) {
    const id = exports.speechapi.createTranscriber(modelPath, modelName, modelKey, logsPath ?? undefined, phrases ?? [], callback);
    return {
        start: () => exports.speechapi.startTranscriber(id),
        stop: () => exports.speechapi.stopTranscriber(id),
        dispose: () => exports.speechapi.disposeTranscriber(id)
    };
}
exports.createTranscriber = createTranscriber;
//#endregion
//#region Synthesis
var SynthesizerStatusCode;
(function (SynthesizerStatusCode) {
    SynthesizerStatusCode[SynthesizerStatusCode["STARTED"] = 1] = "STARTED";
    SynthesizerStatusCode[SynthesizerStatusCode["STOPPED"] = 9] = "STOPPED";
    SynthesizerStatusCode[SynthesizerStatusCode["DISPOSED"] = 10] = "DISPOSED";
    SynthesizerStatusCode[SynthesizerStatusCode["ERROR"] = 11] = "ERROR";
})(SynthesizerStatusCode || (exports.SynthesizerStatusCode = SynthesizerStatusCode = {}));
function createSynthesizer({ modelPath, modelName, modelKey, logsPath }, callback) {
    const id = exports.speechapi.createSynthesizer(modelPath, modelName, modelKey, logsPath ?? undefined, callback);
    return {
        synthesize: (text) => exports.speechapi.synthesize(id, text),
        stop: () => exports.speechapi.stopSynthesizer(id),
        dispose: () => exports.speechapi.disposeSynthesizer(id)
    };
}
exports.createSynthesizer = createSynthesizer;
//#endregion
//#region Keyword Recognition
var KeywordRecognitionStatusCode;
(function (KeywordRecognitionStatusCode) {
    KeywordRecognitionStatusCode[KeywordRecognitionStatusCode["RECOGNIZED"] = 3] = "RECOGNIZED";
    KeywordRecognitionStatusCode[KeywordRecognitionStatusCode["STOPPED"] = 9] = "STOPPED";
    KeywordRecognitionStatusCode[KeywordRecognitionStatusCode["ERROR"] = 11] = "ERROR";
})(KeywordRecognitionStatusCode || (exports.KeywordRecognitionStatusCode = KeywordRecognitionStatusCode = {}));
function recognize({ modelPath, signal }) {
    return new Promise((resolve, reject) => {
        const id = exports.speechapi.recognize(modelPath, (error, result) => {
            if (error) {
                reject(error);
            }
            else {
                resolve(result);
            }
        });
        const onAbort = () => {
            exports.speechapi.unrecognize(id);
            signal.removeEventListener('abort', onAbort);
        };
        signal.addEventListener('abort', onAbort);
    });
}
exports.recognize = recognize;
//#endregion
//# sourceMappingURL=index.js.map