"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.InternalLogger = void 0;
/**
 * In a browser/web worker we use a NOP-logger for now.
 */
class InternalLogger {
    dispose() {
        return undefined;
    }
    log(msg, level, prependTimestamp) {
    }
    setup(options) {
        return undefined;
    }
}
exports.InternalLogger = InternalLogger;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW50ZXJuYWxMb2dnZXJTdHViLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL3dlYi9pbnRlcm5hbExvZ2dlclN0dWIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Z0dBR2dHOzs7QUFJaEc7O0dBRUc7QUFDSCxNQUFhLGNBQWM7SUFDMUIsT0FBTztRQUNOLE9BQU8sU0FBUyxDQUFDO0lBQ2xCLENBQUM7SUFDRCxHQUFHLENBQUMsR0FBVyxFQUFFLEtBQWUsRUFBRSxnQkFBMEI7SUFDNUQsQ0FBQztJQUNELEtBQUssQ0FBQyxPQUErQjtRQUNwQyxPQUFPLFNBQVMsQ0FBQztJQUNsQixDQUFDO0NBQ0Q7QUFURCx3Q0FTQyIsInNvdXJjZXNDb250ZW50IjpbIi8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiAgQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiAgTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLiBTZWUgTGljZW5zZS50eHQgaW4gdGhlIHByb2plY3Qgcm9vdCBmb3IgbGljZW5zZSBpbmZvcm1hdGlvbi5cbiAqLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5pbXBvcnQgeyBJSW50ZXJuYWxMb2dnZXIsIElJbnRlcm5hbExvZ2dlck9wdGlvbnMsIExvZ0xldmVsIH0gZnJvbSBcIi4uL2xvZ2dlclwiO1xuXG4vKipcbiAqIEluIGEgYnJvd3Nlci93ZWIgd29ya2VyIHdlIHVzZSBhIE5PUC1sb2dnZXIgZm9yIG5vdy5cbiAqL1xuZXhwb3J0IGNsYXNzIEludGVybmFsTG9nZ2VyIGltcGxlbWVudHMgSUludGVybmFsTG9nZ2VyIHtcblx0ZGlzcG9zZSgpOiBQcm9taXNlPHZvaWQ+IHtcblx0XHRyZXR1cm4gdW5kZWZpbmVkO1xuXHR9XG5cdGxvZyhtc2c6IHN0cmluZywgbGV2ZWw6IExvZ0xldmVsLCBwcmVwZW5kVGltZXN0YW1wPzogYm9vbGVhbik6IHZvaWQge1xuXHR9XG5cdHNldHVwKG9wdGlvbnM6IElJbnRlcm5hbExvZ2dlck9wdGlvbnMpOiBQcm9taXNlPHZvaWQ+IHtcblx0XHRyZXR1cm4gdW5kZWZpbmVkO1xuXHR9XG59Il19