property.ROOT_LEVEL=Info
property.CONSOLE_LEVEL=Warn
property.FILE_LEVEL=Trace
property.SERVER_NAME=rocket_mv_basic_language_server
property.LOG_HOME=${LOG_HOME}/logs

status=error
monitorInterval=5

rootLogger.level=${ROOT_LEVEL}
rootLogger.appenderRef.console.ref=ConsoleAll
rootLogger.appenderRef.file.ref=RollingFileAll

appender.console.type=Console
appender.console.name=ConsoleAll
appender.console.target=SYSTEM_ERR
appender.console.layout.type=PatternLayout
appender.console.layout.pattern=%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
appender.console.filter.threshold.level=${CONSOLE_LEVEL}
appender.console.filter.threshold.type=ThresholdFilter

appender.file.type=RollingFile
appender.file.name=RollingFileAll
appender.file.filter.threshold.level=${FILE_LEVEL}
appender.file.filter.threshold.type=ThresholdFilter
appender.file.fileName=${LOG_HOME}/${SERVER_NAME}.log
appender.file.filePattern=${LOG_HOME}/backup/${SERVER_NAME}.%d{yyyy-MM-dd}-%i.log
appender.file.layout.type=PatternLayout
appender.file.layout.pattern=%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
appender.file.policies.type=Policies
appender.file.policies.time.type=TimeBasedTriggeringPolicy
appender.file.policies.time.interval=1
appender.file.policies.time.modulate=true
appender.file.policies.size.type=SizeBasedTriggeringPolicy
appender.file.policies.size.size=10M
appender.file.strategy.type=DefaultRolloverStrategy