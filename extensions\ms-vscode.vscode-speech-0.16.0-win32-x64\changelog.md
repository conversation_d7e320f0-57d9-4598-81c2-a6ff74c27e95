# Change Log

## [0.16.0]

- Update Azure Speech SDK to `1.44.0`

## [0.14.0]

- Update Azure Speech SDK to `1.43.0`

## [0.12.0]

- Update Azure Speech SDK to `1.40.0`
- Update text-to-speech and speech-to-text models for improved accuracy

## [0.10.0]

- Support for text-to-speech

## [0.8.0]

- Support for lazy activation
- Adopt `workbench.extensions.installExtension` command for ad-hoc language extension pack installation

## [0.6.0]

- Support for other languages via `accessibility.voice.speechLanguage` setting
- Faster begin of transcription by reusing speech sessions
- Update Azure Speech SDK to `1.35.0`

## [0.4.0]

- Support for keyword activation via 'Hey Code'
- Support for Linux arm64 and armHF
- Various crash and bug fixes

## [0.2.0]

- Initial release
