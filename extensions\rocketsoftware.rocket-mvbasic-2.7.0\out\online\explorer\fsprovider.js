"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MVFileSystemProvider = exports.SearchResultsProvider = exports.Server = exports.Account = exports.Directory = exports.File = void 0;
const path = require("path");
const vscode = require("vscode");
const node_1 = require("vscode-languageclient/node");
const driver = require("../../driver");
class File {
    constructor(name) {
        this.type = vscode.FileType.File;
        this.ctime = Date.now();
        this.mtime = Date.now();
        this.size = 0;
        this.name = name;
    }
}
exports.File = File;
class Directory {
    constructor(name) {
        this.type = vscode.FileType.Directory;
        this.ctime = Date.now();
        this.mtime = Date.now();
        this.size = 0;
        this.name = name;
        this.entries = new Map();
    }
}
exports.Directory = Directory;
class Account extends Directory {
}
exports.Account = Account;
class Server extends Directory {
    constructor(name) {
        super(name);
        this.address = "";
        this.username = "";
        this.password = "";
        this.port = 31438;
        this.entries = new Map();
    }
}
exports.Server = Server;
class SearchResultsProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.searchResults = [];
        this.parentPath = "";
    }
    setSearchResults(results, parentPath) {
        this.searchResults = results;
        this._onDidChangeTreeData.fire(undefined);
        this.parentPath = parentPath;
    }
    getTreeItem(element) {
        const treeItem = new vscode.TreeItem(element.getShortPath());
        treeItem.resourceUri = element.uri;
        treeItem.command = {
            command: 'vscode.open',
            title: 'Open File',
            arguments: [element.uri]
        };
        return treeItem;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(this.searchResults.map((uri) => new SearchItem(uri.fsPath, uri, this.parentPath)));
        }
        return Promise.resolve(this.searchResults.map((uri) => new SearchItem(uri.fsPath, uri, this.parentPath)));
    }
}
exports.SearchResultsProvider = SearchResultsProvider;
class SearchItem {
    constructor(label, uri, parentPath) {
        this.label = label;
        this.uri = uri;
        this.parentPath = parentPath;
    }
    getShortPath() {
        const shortParentPath = path.resolve(this.parentPath, '..');
        const relativePath = path.relative(shortParentPath, this.label);
        return `..${path.sep}${relativePath}`;
    }
}
class MVFileSystemProvider {
    constructor(_treeData) {
        this._treeData = _treeData;
        this.sortAscending = true;
        // --- manage file events
        this._emitter = new vscode.EventEmitter();
        this._bufferedEvents = [];
        this.onDidChangeFile = this._emitter.event;
    }
    setSort(isAscending) {
        this.sortAscending = isAscending;
    }
    getSort() {
        return this.sortAscending;
    }
    stat(uri) {
        return this._lookup(uri, false);
    }
    createDirectory(uri) {
        this.addDirectory(uri);
    }
    createRootDirectory(uri, entry) {
        let dirname = uri.with({ path: path.posix.dirname(uri.path) });
        this._treeData.set(entry.address, entry);
        this._fireSoon({ type: vscode.FileChangeType.Changed, uri: dirname }, { type: vscode.FileChangeType.Created, uri });
    }
    addDirectory(uri) {
        let basename = path.posix.basename(uri.path);
        let dirname = uri.with({ path: path.posix.dirname(uri.path) });
        let parent = this._lookupAsDirectory(dirname, false);
        let entry = new Directory(basename);
        parent.entries.set(entry.name, entry);
        parent.mtime = Date.now();
        parent.size += 1;
        parent.entries = this.sortByKey(parent.entries);
        this._fireSoon({ type: vscode.FileChangeType.Changed, uri: dirname }, { type: vscode.FileChangeType.Created, uri });
        return entry;
    }
    addAccount(uri) {
        let basename = path.posix.basename(uri.path);
        let dirname = uri.with({ path: path.posix.dirname(uri.path) });
        let parent = this._lookupAsDirectory(dirname, false);
        let entry = new Account(basename);
        parent.entries.set(entry.name, entry);
        parent.mtime = Date.now();
        parent.size += 1;
        parent.entries = this.sortByKey(parent.entries);
        this._fireSoon({ type: vscode.FileChangeType.Changed, uri: dirname }, { type: vscode.FileChangeType.Created, uri });
        return entry;
    }
    readDirectory(uri) {
        const entry = this._lookupAsDirectory(uri, false);
        const result = [];
        for (const [name, child] of entry.entries) {
            result.push([name, child.type]);
        }
        return result;
    }
    // --- manage file contents
    readFile(uri) {
        return __awaiter(this, void 0, void 0, function* () {
            const file = this.lookupAsFile(uri, false);
            let data = file.data;
            if (data) {
                return data;
            }
            return this.openFile(uri).then(res => {
                if (res === 0) {
                    data = file.data;
                }
                if (res !== 0 || data === undefined) {
                    throw vscode.FileSystemError.FileNotFound();
                }
                return data;
            });
        });
    }
    writeFile(uri, content, options) {
        this.addFile(uri, content, options);
    }
    writeFile2(uri, content, options) {
        return this.addFile2(uri, content, options, true);
    }
    addFile(uri, content, options) {
        let parent = this._lookupParentDirectory(uri);
        let baseName = path.posix.basename(uri.path);
        let entry = parent.entries.get(baseName);
        if (entry instanceof Directory) {
            throw vscode.FileSystemError.FileIsADirectory(uri);
        }
        if (!entry && !options.create) {
            throw vscode.FileSystemError.FileNotFound(uri);
        }
        if (entry && options.create && !options.overwrite) {
            throw vscode.FileSystemError.FileExists(uri);
        }
        if (!entry) {
            entry = new File(baseName);
            parent.entries.set(baseName, entry);
            parent.entries = this.sortByKey(parent.entries);
            this._fireSoon({ type: vscode.FileChangeType.Created, uri });
        }
    }
    addFile2(uri, content, options, isUpdateServer) {
        return __awaiter(this, void 0, void 0, function* () {
            let parent = this._lookupParentDirectory(uri);
            let baseName = path.posix.basename(uri.path);
            let entry = parent.entries.get(baseName);
            if (entry instanceof Directory) {
                throw vscode.FileSystemError.FileIsADirectory(uri);
            }
            if (!entry && !options.create) {
                throw vscode.FileSystemError.FileNotFound(uri);
            }
            if (entry && options.create && !options.overwrite) {
                throw vscode.FileSystemError.FileExists(uri);
            }
            if (!entry) {
                entry = new File(baseName);
                parent.entries.set(baseName, entry);
                parent.entries = this.sortByKey(parent.entries);
                this._fireSoon({ type: vscode.FileChangeType.Created, uri });
            }
            entry.mtime = Date.now();
            entry.size = content.byteLength;
            entry.data = content;
            if (content.toString() == "${needToGetFromServer}") {
                const text = yield loadFileContent(parent.name, entry.name)
                    .then((res) => { return Buffer.from(res); });
                entry.data = text;
            }
            if (isUpdateServer == true) {
                const parts = uri.fsPath.split("\\");
                if (parts.length < 3) {
                    return;
                }
                const accountName = parts[2];
                yield createBasicFile(accountName, parent.name, entry.name, content.toString());
            }
            return;
        });
    }
    openFile(uri) {
        return __awaiter(this, void 0, void 0, function* () {
            let file = this.lookup(uri);
            if (file === undefined) {
                return -1;
            }
            file.mtime = Date.now();
            file.size = 0;
            // Get file parent
            const parts = uri.fsPath.split("\\");
            if (parts.length <= 2) {
                return -1;
            }
            const parentName = parts[parts.length - 2];
            const text = yield this._loadFileContent(uri, parentName, file.name).then((res) => { return Buffer.from(res); });
            if (text.toString().startsWith("An unknown error: 29")) {
                return 29;
            }
            else if (text.toString().startsWith("An unknown error")) {
                return -1;
            }
            file.data = text;
            return 0;
        });
    }
    _loadFileContent(uri, dir, program) {
        //const programUri = vscode.Uri.joinPath(uri, program);
        const command = node_1.Command.create("getRecord", "getRecord", dir, program);
        return driver.send(command);
    }
    // --- manage files/folders
    rename(oldUri, newUri, options) {
        if (!options.overwrite && this._lookup(newUri, true)) {
            throw vscode.FileSystemError.FileExists(newUri);
        }
        let entry = this._lookup(oldUri, false);
        let oldParent = this._lookupParentDirectory(oldUri);
        let newParent = this._lookupParentDirectory(newUri);
        let newName = path.posix.basename(newUri.path);
        oldParent.entries.delete(entry.name);
        entry.name = newName;
        newParent.entries.set(newName, entry);
        this._fireSoon({ type: vscode.FileChangeType.Deleted, uri: oldUri }, { type: vscode.FileChangeType.Created, uri: newUri });
    }
    delete(uri) {
        return __awaiter(this, void 0, void 0, function* () {
            let dirname = uri.with({ path: path.posix.dirname(uri.path) });
            let basename = path.posix.basename(uri.path);
            let parent = this._lookupAsDirectory(dirname, false);
            if (!parent.entries.has(basename)) {
                throw vscode.FileSystemError.FileNotFound(uri);
            }
            parent.entries.delete(basename);
            parent.mtime = Date.now();
            parent.size -= 1;
            let account = getAccountFromBasicPath(uri.path);
            if (account === undefined) {
                throw vscode.FileSystemError.Unavailable(uri);
            }
            yield deleteBasicFile(account, parent.name, basename);
            this._fireSoon({ type: vscode.FileChangeType.Changed, uri: dirname }, { uri, type: vscode.FileChangeType.Deleted });
        });
    }
    findFiles(uri, query) {
        const results = [];
        let entry = this.lookup(uri);
        if (entry) {
            const lowerPattern = query.toLowerCase();
            let regexPattern = '^';
            if (lowerPattern.includes('?') || lowerPattern.includes('*')) {
                for (let i = 0; i < lowerPattern.length; i++) {
                    const char = lowerPattern[i];
                    if (char === '?') {
                        regexPattern += '.';
                    }
                    else if (char === '*') {
                        regexPattern += '.*';
                    }
                    else {
                        regexPattern += char;
                    }
                }
                regexPattern += '$';
            }
            if (entry instanceof Directory) {
                var path = uri.path;
                var res2 = this.searchFilesRecursively(entry, path, regexPattern);
                results.push(...res2);
            }
        }
        return results;
    }
    searchFilesRecursively(entry, path, query) {
        const results = [];
        if (entry instanceof Directory) {
            entry.entries.forEach(element => {
                if (element instanceof Directory) {
                    var path2 = vscode.Uri.parse("mvvs:" + path + "/" + element.name);
                    results.push(...this.searchFilesRecursively(element, path2.fsPath, query));
                }
                else if (this.matchPattern(element.name, query)) {
                    var path2 = vscode.Uri.parse("mvvs:" + path + "/" + element.name);
                    results.push(path2);
                }
            });
        }
        return results;
    }
    matchPattern(str, pattern) {
        const lowerStr = str.toLowerCase();
        const regex = new RegExp(pattern, 'i');
        return regex.test(lowerStr);
    }
    // --- lookup
    lookup(uri) {
        return this._lookup(uri, true);
    }
    _lookup(uri, silent) {
        let parts = uri.path.split('/');
        let entries = this._treeData;
        let result = undefined;
        for (const part of parts) {
            if (!part) {
                continue;
            }
            if (entries === undefined) {
                return undefined;
            }
            const node = entries.get(part);
            if (!node) {
                if (!silent) {
                    throw vscode.FileSystemError.FileNotFound(uri);
                }
                else {
                    return undefined;
                }
            }
            result = node;
            if (node instanceof Directory) {
                entries = node.entries;
            }
            else {
                entries = undefined;
            }
        }
        return result;
    }
    _lookupAsDirectory(uri, silent) {
        let entry = this._lookup(uri, silent);
        if (entry instanceof Directory) {
            return entry;
        }
        throw vscode.FileSystemError.FileNotADirectory(uri);
    }
    lookupAsFile(uri, silent) {
        let entry = this._lookup(uri, silent);
        if (entry instanceof File) {
            return entry;
        }
        throw vscode.FileSystemError.FileIsADirectory(uri);
    }
    _lookupParentDirectory(uri) {
        const dirname = uri.with({ path: path.posix.dirname(uri.path) });
        return this._lookupAsDirectory(dirname, false);
    }
    sortByKey(map) {
        let keys = new Array();
        for (var key of map.keys()) {
            keys.push(key);
        }
        if (this.sortAscending) {
            keys.sort(function (key1, key2) {
                return key1 > key2 ? 1 : -1;
            });
        }
        else {
            keys.sort(function (key1, key2) {
                return key1 > key2 ? -1 : 1;
            });
        }
        let newMap = new Map();
        keys.forEach(key => {
            newMap.set(key, map.get(key));
        });
        return newMap;
    }
    watch(uri, options) {
        return new vscode.Disposable(() => { });
    }
    _fireSoon(...events) {
        this._bufferedEvents.push(...events);
        if (this._fireSoonHandle) {
            clearTimeout(this._fireSoonHandle);
        }
        this._fireSoonHandle = setTimeout(() => {
            this._emitter.fire(this._bufferedEvents);
            this._bufferedEvents.length = 0;
        }, 5);
    }
    clear() {
        for (let item of this._treeData) {
            item[1].entries.clear();
        }
    }
}
exports.MVFileSystemProvider = MVFileSystemProvider;
function deleteBasicFile(account, dir, program) {
    return __awaiter(this, void 0, void 0, function* () {
        const command = node_1.Command.create("deleteBasicFile", "deleteBasicFile", account, dir, program);
        const res = yield driver.send(command);
        showErrorMessage(res);
    });
}
function showErrorMessage(res) {
    if (res !== "") {
        vscode.window.showInformationMessage(res);
    }
}
function loadFileContent(dir, program) {
    const command = node_1.Command.create("getRecord", "getRecord", dir, program);
    return driver.send(command);
}
function createBasicFile(accountName, dir, program, context) {
    return __awaiter(this, void 0, void 0, function* () {
        const command = node_1.Command.create("createBasicFile", "createBasicFile", accountName, dir, program, context);
        const res = yield driver.send(command);
        showErrorMessage(res);
        ;
    });
}
function getAccountFromBasicPath(uri) {
    const parts = uri.split('/');
    const filteredParts = parts.filter(part => part.length > 0);
    if (filteredParts.length < 3) {
        return undefined;
    }
    return filteredParts[1];
}
//# sourceMappingURL=fsprovider.js.map