{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Checkout on vscode.dev": "在 vscode.dev 簽出", "Commit Changes": "認可變更", "Copy Anyway": "仍要複製", "Copy vscode.dev Link": "複製 vscode.dev 連結", "Create Fork": "建立分支", "Create GitHub fork": "建立 GitHub 分支", "Create PR": "建立 PR", "Creating GitHub Pull Request...": "正在建立 GitHub 提取要求...", "Creating first commit": "正在建立第一個提交", "Forking \"{0}/{1}\"...": "正在派生 \"{0}/{1}\"...", "Learn More": "深入了解", "Log level: {0}": "記錄層級: {0}", "No": "否", "No GitHub remotes found that contain this commit.": "找不到包含此認可的 GitHub 遠端。", "No template": "沒有範本", "Open PR": "開啟 PR", "Open on GitHub": "在 GitHub 上開啟", "Pick a folder to publish to GitHub": "挑選要發布至 GitHub 的資料夾", "Publish Branch & Copy Link": "發佈分支與複製連結", "Publishing to a private GitHub repository": "正在發布到私人 GitHub 存放庫", "Publishing to a public GitHub repository": "正在發布到公用 GitHub 存放庫", "Pull Changes & Copy Link": "提取變更與複製連結", "Push Commits & Copy Link": "推送認可與複製連結", "Pushing changes...": "正在推送變更...", "Select the Pull Request template": "選取提取要求範本", "Select which files should be included in the repository.": "選取要包含在存放庫中的檔案。", "Successfully published the \"{0}\" repository to GitHub.": "已成功將 \"{0}\" 存放庫發佈到 GitHub。", "The PR \"{0}/{1}#{2}\" was successfully created on GitHub.": "已成功在 GitHub 上建立 PR \"{0}/{1}#{2}\"。", "The current branch has unpublished commits. Would you like to push your commits before copying a link?": "目前的分支有未發佈的認可。是否要先推送您的認可，再複製連結?", "The current branch is not published to the remote. Would you like to publish your branch before copying a link?": "目前的分支未發佈至遠端。是否要先發佈您的分支，再複製連結?", "The current branch is not up to date. Would you like to pull before copying a link?": "最新分支不是最新的。在複製連結之前，是否要提取?", "The current file has uncommitted changes. Please commit your changes before copying a link.": "目前的檔案有未認可的變更。請先提交您的變更，再複製連結。", "The fork \"{0}\" was successfully created on GitHub.": "已成功在 GitHub 上建立分支 \"{0}\"。", "Uploading files": "正在上傳檔案", "You don't have permissions to push to \"{0}/{1}\" on GitHub. Would you like to create a fork and push to it instead?": "您無權在 GitHub 上推送至「{0}/{1}」。要建立分支並改為推送至該分支嗎?", "Your push to \"{0}/{1}\" was rejected by GitHub because push protection is enabled and one or more secrets were detected.": "GitHub 已拒絕您對 \"{0}/{1}\" 的推送，因為已啟用推送保護，並且偵測到一或多個祕密。", "{0} Open on GitHub": "{0} 在 GitHub 上開啟"}, "package": {"command.copyVscodeDevLink": "複製 vscode.dev 連結", "command.openOnGitHub": "在 GitHub 上開啟", "command.openOnVscodeDev": "在 vscode.dev 中開啟", "command.publish": "發佈至 GitHub", "config.branchProtection": "控制是否要查詢 GitHub 存放庫的存放庫規則", "config.gitAuthentication": "控制是否要在 VS Code 中為 git 命令啟用自動 GitHub 驗證。", "config.gitProtocol": "控制要用來複製 GitHub 存放庫的通訊協定", "config.showAvatar": "控制是否要在各種暫留中顯示認可作者的 GitHub 虛擬人偶，(例如： Git、Timeline、Source Control Graph 等 )", "description": "適用於 VS Code 的 GitHub 功能", "displayName": "GitHub", "welcome.publishFolder": "您可以直接將此資料夾發布到 GitHub 存放庫。發佈之後，您就可以存取 Git 與 GitHub 所支援的原始檔控制功能。\r\n[$(github) 發佈至 GitHub](command:github.publish)", "welcome.publishWorkspaceFolder": "您可以直接將工作區資料夾發布到 GitHub 存放庫。發佈之後，您就可以存取 Git 與 GitHub 所支援的原始檔控制功能。\r\n[$(github) 發佈至 GitHub](command:github.publish)"}}}