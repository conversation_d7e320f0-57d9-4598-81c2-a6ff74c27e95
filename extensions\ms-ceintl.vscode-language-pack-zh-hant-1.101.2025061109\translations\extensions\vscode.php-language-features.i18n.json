{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Cannot validate since a PHP installation could not be found. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "無法驗證，因為找不到 PHP 安裝。使用設定 'php.validate.executablePath' 來設定 PHP 可執行檔。", "Cannot validate since no PHP executable is set. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "因為未設定任何 PHP 可執行檔，所以無法驗證。您可以使用 'php.validate.executablePath' 設定來設定 PHP 可執行檔。", "Cannot validate since {0} is not a valid php executable. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "因為 {0} 不是有效的 PHP 可執行檔，所以無法驗證。您可以使用設定 'php.validate.executablePath' 設定 PHP 可執行檔。", "Failed to run php using path: {0}. Reason is unknown.": "無法使用路徑 {0} 執行 PHP。原因不明。", "Open Settings": "開啟設定"}, "package": {"command.untrustValidationExecutable": "禁止 PHP 驗證可執行檔 (定義為工作區設定)", "commands.categroy.php": "PHP", "configuration.suggest.basic": "設定是否啟用內建 PHP 語言建議。此支援會建議 PHP 全域和變數。", "configuration.title": "PHP", "configuration.validate.enable": "啟用/停用內建 PHP 驗證。", "configuration.validate.executablePath": "指向 PHP 可執行檔。", "configuration.validate.run": "是否在儲存或輸入時執行 linter。", "description": "為 PHP 檔案提供豐富的語言支援。", "displayName": "php 語言功能", "workspaceTrust": "當 'php.validate.executablePath' 設定將在工作區中載入 PHP 版本時，延伸模組需要工作區信任。"}}}