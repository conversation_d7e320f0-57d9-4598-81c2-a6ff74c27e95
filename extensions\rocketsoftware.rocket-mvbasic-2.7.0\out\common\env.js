"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FS_SCHEMA = exports.NEWLINE = exports.LANGUAGE_ID = exports.DEVELOPMENT_MODEL = exports.COMMAND_PREFIX = exports.NO_GRPC_SERVICE_ERROR_INT = exports.NO_GRPC_SUPPORT_ERROR_INT = exports.ERROR_INT = exports.DEFAULT_HOST_DA_PORT = exports.DEFAULT_HOST_LSP_PORT = exports.DEFAULT_HOST_NAME = void 0;
exports.getJavaCmd = getJavaCmd;
exports.setLspJarPath = setLspJarPath;
exports.getLspJarPath = getLspJarPath;
exports.javaExeConvert = javaExeConvert;
const vscode = require("vscode");
const msg_1 = require("../msg");
const path = require("path");
exports.DEFAULT_HOST_NAME = 'localhost';
exports.DEFAULT_HOST_LSP_PORT = 54321;
exports.DEFAULT_HOST_DA_PORT = 54322;
exports.ERROR_INT = -9999999;
exports.NO_GRPC_SUPPORT_ERROR_INT = -9999998;
exports.NO_GRPC_SERVICE_ERROR_INT = -9999997;
exports.COMMAND_PREFIX = "${command:";
exports.DEVELOPMENT_MODEL = "rocketMvBasic.languageServer.developmentMode";
exports.LANGUAGE_ID = "rocket-mvbasic";
exports.NEWLINE = "\r\n";
exports.FS_SCHEMA = "mvvs";
let jarPath;
function getJavaCmd() {
    let command = 'java';
    let result = getJavaVersion("");
    if (result[0] != undefined || isNaN(result[1]) || result[1] < 11) {
        let jdkPath = vscode.workspace.getConfiguration().get('rocketMvBasic.languageServer.jdkEnvironment') || '';
        if (jdkPath == undefined || jdkPath.length == 0) {
            vscode.window.showErrorMessage(msg_1.Msg.JAVA_ENV_NOT_SET, "OK");
            return undefined;
        }
        else {
            result = getJavaVersion(jdkPath);
            if (result[0] != undefined) {
                vscode.window.showErrorMessage(msg_1.Msg.JAVA_UNEXPECT, "OK");
                return undefined;
            }
            if (isNaN(result[1]) || result[1] < 11) {
                vscode.window.showErrorMessage(msg_1.Msg.JAVA_LOW_VERSION, "OK");
                return undefined;
            }
        }
        command = path.join(jdkPath, "java");
    }
    return command;
}
function setLspJarPath(extensionPath) {
    jarPath = path.join(extensionPath, "server/ls4b.jar");
}
function getLspJarPath() {
    return jarPath;
}
function javaExeConvert(javaExe) {
    if (process.platform === "win32" && !javaExe.endsWith(".exe")) {
        javaExe += ".exe";
    }
    return javaExe;
}
function getJavaVersion(jdkPath) {
    let command;
    if (jdkPath != undefined && jdkPath.length != 0) {
        let javaPath = path.join(jdkPath, "java");
        command = '"' + javaPath + '"' + ' -version 2>&1';
    }
    else {
        command = 'java -version 2>&1';
    }
    var exec = require('child_process').execSync;
    let data = '';
    try {
        let lines = exec(command).toString().split('\n');
        for (let line of lines) {
            let version = pickJavaVersionFromString(line);
            if (version != undefined) {
                return [undefined, parseFloat(version)];
            }
        }
    }
    catch (error) {
        return [error, -1];
    }
    return [undefined, -1];
}
function pickJavaVersionFromString(javaLine) {
    var version = new RegExp('java version').test(javaLine) ? javaLine.split(' ')[2].replace(/"/g, '') : undefined;
    if (version != undefined) {
        return version;
    }
    return new RegExp('openjdk version').test(javaLine) ? javaLine.split(' ')[2].replace(/"/g, '') : undefined;
}
//# sourceMappingURL=env.js.map