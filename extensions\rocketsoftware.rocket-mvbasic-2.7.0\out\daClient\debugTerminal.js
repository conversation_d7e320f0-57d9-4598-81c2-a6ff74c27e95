"use strict";
/**
 *  buildTerminal - debug terminal related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicDebugTerminal = void 0;
const vscode = require("vscode");
const stream_terminal_1 = require("./stream_terminal");
class BasicDebugTerminal {
    activate(arg) {
        if (this.pty) {
            this.pty.closeEmitter.fire(0);
        }
        this.pty = new stream_terminal_1.StreamTerminal(arg.port);
        this.terminal = vscode.window.createTerminal({ name: `Basic debugging.`, pty: this.pty });
        this.terminal.show();
    }
    close() {
        if (this.pty) {
            this.pty.closeEmitter.fire(0);
        }
        if (this.terminal) {
            this.terminal.dispose();
        }
        this.pty = undefined;
        this.terminal = undefined;
    }
}
exports.BasicDebugTerminal = BasicDebugTerminal;
//# sourceMappingURL=debugTerminal.js.map