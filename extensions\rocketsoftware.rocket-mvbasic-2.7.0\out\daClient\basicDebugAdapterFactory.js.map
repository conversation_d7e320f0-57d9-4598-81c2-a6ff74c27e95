{"version": 3, "file": "basicDebugAdapterFactory.js", "sourceRoot": "", "sources": ["../../src/daClient/basicDebugAdapterFactory.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,qCAAqC;AACrC,+CAA8C;AAC9C,uCAA+F;AAG/F,MAAa,wBAAwB;IAE1B,4BAA4B,CAAC,QAA6B,EAAE,UAAqD;QACpH,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC;QAC9C,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC1D,IAAG,MAAM,IAAI,eAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAG,MAAM,IAAI,+BAAyB,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,kHAAkH,CAAC,CAAC;YACxI,CAAC;iBAAM,IAAG,MAAM,IAAI,+BAAyB,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,wHAAwH,CAAC,CAAC;YAC9I,CAAC;iBAAK,CAAC;gBACH,OAAO,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAhBD,4DAgBC"}