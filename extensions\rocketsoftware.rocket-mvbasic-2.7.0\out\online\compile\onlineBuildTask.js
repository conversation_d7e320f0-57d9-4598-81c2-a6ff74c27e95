"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlinebuildTaskCreator = void 0;
const vscode = require("vscode");
const onlinebuildTerminal_1 = require("./onlinebuildTerminal");
const vscode_1 = require("vscode");
class OnlinebuildTaskCreator {
    constructor(_name, _source, _fsProvider) {
        this._name = _name;
        this._source = _source;
        this._fsProvider = _fsProvider;
    }
    create(files, definition, bIgnoreCatalog) {
        const execution = this._getExecution(files, definition, bIgnoreCatalog);
        const task = new vscode.Task(definition, vscode_1.TaskScope.Workspace, this._name, this._source, execution);
        task.presentationOptions.panel = vscode.TaskPanelKind.Dedicated;
        task.presentationOptions.showReuseMessage = false;
        task.presentationOptions.clear = true;
        task.group = vscode.TaskGroup.Build;
        return task;
    }
    _getExecution(files, definition, bIgnoreCatalog) {
        return new vscode.CustomExecution(() => __awaiter(this, void 0, void 0, function* () {
            return new onlinebuildTerminal_1.OnlineBuildTaskTerminal(files, definition, bIgnoreCatalog);
        }));
    }
}
exports.OnlinebuildTaskCreator = OnlinebuildTaskCreator;
//# sourceMappingURL=onlineBuildTask.js.map