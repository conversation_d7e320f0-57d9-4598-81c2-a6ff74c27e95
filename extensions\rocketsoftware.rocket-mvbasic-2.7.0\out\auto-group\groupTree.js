"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GroupTree = void 0;
const path = require("path");
const glob = require("glob");
const fs = require("fs");
const vscode = require("vscode");
const basicItems_1 = require("./basicItems");
const groupViewCfg_1 = require("./groupViewCfg");
class SubGroupTree {
    constructor(cfgFile, workspaceFolder) {
        this.ignore = [];
        this.default = { delimiter: '.', level: 2 };
        this.treeNodes = [];
        this.folderPath = workspaceFolder;
        this.gvc = new groupViewCfg_1.GroupViewConfig(workspaceFolder, cfgFile);
    }
    /**
     * Create the whole group tree.
     */
    make() {
        if (!this.gvc.init())
            return false;
        if (this.folderPath.length == 0)
            return false;
        if (!this.makeDefaultRule())
            return false;
        if (!this.makeIgnoreList())
            return false;
        if (!this.makeGroupTree())
            return false;
        return true;
    }
    getChildren() {
        return this.treeNodes;
    }
    getFolderPath() {
        return this.folderPath;
    }
    getFolderName() {
        const parts = this.folderPath.split(/[\\/]/);
        return parts[parts.length - 1];
    }
    getDefaultGroupName(item) {
        const fileName = item.label;
        const parts = fileName.split(this.default.delimiter);
        if (parts.length < this.default.level) {
            return fileName;
        }
        let groupName = "";
        for (let i = 0; i < this.default.level; i++) {
            groupName += parts[i];
            groupName += this.default.delimiter;
        }
        return groupName.substr(0, groupName.length - this.default.delimiter.length);
    }
    /**
     * Add a group file into a group.
     * @param item File.
     * @param groupName Group name.
     * @param groups Add to which group.
     */
    addGroup(item, groupName, groups) {
        const dir = this.getFileDir(item.filePath);
        for (let i = 0; i < groups.length; i++) {
            const group = groups[i];
            if (group.label === groupName && group.filePath === dir) {
                group.add(item);
                return;
            }
        }
        const group = new basicItems_1.BasicGroup(groupName, dir);
        group.add(item);
        groups.push(group);
    }
    /**
     * Create groups and single files based on given files.
     * @param files Files list.
     */
    makeDefaultItems(files) {
        try {
            const groups = [];
            for (const filePath of files) {
                const label = this.getLabel(filePath);
                if (!label) {
                    continue;
                }
                const file = new basicItems_1.BasicFile(label, filePath);
                const groupName = this.getDefaultGroupName(file);
                this.addGroup(file, groupName, groups);
            }
            let defaultGroups = [];
            let singleFiles = [];
            for (const group of groups) {
                if (group.getChildren().length > 1) {
                    defaultGroups.push(group);
                }
                else {
                    const file = group.get(0);
                    if (file != undefined) {
                        singleFiles.push(file);
                    }
                }
            }
            defaultGroups = defaultGroups.sort(this.compare);
            singleFiles = singleFiles.sort(this.compare);
            let result = [];
            result = result.concat(defaultGroups);
            result = result.concat(singleFiles);
            return result;
        }
        catch (e) {
            return undefined;
        }
    }
    /**
     * Create customize groups and related source directories.
     */
    makeGroupTree() {
        try {
            const sourceDirs = new Map;
            const groups = this.gvc.groups();
            if (!groups || groups.length == 0) {
                // When source folders are not specified, use current workspace as source dir.
                const totalFiles = glob.sync(path.join(this.folderPath, "*"));
                let files = this.excludeIgnore(totalFiles);
                files = this.excludeFolders(files);
                const basicItems = this.makeDefaultItems(files);
                if (basicItems) {
                    this.treeNodes = this.treeNodes.concat(basicItems);
                    return true;
                }
                return false;
            }
            // Create customize groups and source directories.
            for (const group of groups) {
                const srcDir = this.getSrcDir(group, sourceDirs);
                if (!srcDir) {
                    continue;
                }
                // Create customize groups
                const customeGroup = this.createCustomGroup(group, srcDir);
                if (!customeGroup) {
                    continue;
                }
                srcDir.add(customeGroup);
            }
            for (const dir of sourceDirs) {
                this.treeNodes.push(dir[1]);
                const basicItems = this.makeDefaultItems(dir[1].getPendings());
                if (basicItems) {
                    dir[1].addAll(basicItems);
                }
            }
            this.treeNodes = this.treeNodes.sort(this.compare);
            return true;
        }
        catch (e) {
            return false;
        }
    }
    excludeFolders(files) {
        const result = [];
        for (const file of files) {
            const info = fs.statSync(file);
            if (!info.isDirectory()) {
                result.push(file);
            }
        }
        return result;
    }
    excludeIgnore(files) {
        const result = [];
        for (const file of files) {
            if (!this.ignore.includes(file)) {
                result.push(file);
            }
        }
        return result;
    }
    /**
     * Create a BasicGroup according to the group config.
     * @param group Group node in json config file.
     * @param srcDir Source files directory.
     */
    createCustomGroup(group, srcDir) {
        try {
            const groupName = this.gvc.getGroupName(group);
            if (!groupName) {
                return undefined;
            }
            const includes = this.gvc.getInclude(group);
            const excludes = this.gvc.getExclude(group);
            if (groupName && includes) {
                let totalInclude = [];
                let totalExclude = [];
                for (const include of includes) {
                    const absInclude = path.join(srcDir.filePath, include);
                    const includeFiles = glob.sync(absInclude);
                    totalInclude = totalInclude.concat(includeFiles);
                }
                // Only use the files exists in the source directory.
                const includeResult = [];
                for (const includeFile of totalInclude) {
                    if (srcDir.getPendings().includes(includeFile)) {
                        if (!includeResult.includes(includeFile)) {
                            includeResult.push(includeFile);
                        }
                    }
                }
                if (excludes) {
                    for (const exclude of excludes) {
                        const absExclude = path.join(srcDir.filePath, exclude);
                        const excludeFiles = glob.sync(absExclude);
                        totalExclude = totalExclude.concat(excludeFiles);
                    }
                }
                const result = this.exlcude(includeResult, totalExclude);
                srcDir.removePendings(result);
                const customGroup = new basicItems_1.BasicGroup(groupName, srcDir.filePath);
                // Add include result to customize group.
                for (const incFile of result) {
                    const label = this.getLabel(incFile);
                    if (!label) {
                        continue;
                    }
                    const basicFile = new basicItems_1.BasicFile(label, incFile);
                    customGroup.add(basicFile);
                }
                return customGroup;
            }
            return new basicItems_1.BasicGroup(groupName, srcDir.filePath);
        }
        catch (e) {
            return undefined;
        }
    }
    /**
     * Get the source direcotry from a list, if not exists, create a new one.
     * @param group Group node in json config file.
     * @param srcDirs Source files direcotries list.
     */
    getSrcDir(group, srcDirs) {
        try {
            const src = this.gvc.getSrc(group);
            if (!src) {
                return undefined;
            }
            const srcPath = path.join(this.folderPath, src);
            let srcDir = srcDirs.get(src);
            if (!srcDir) {
                srcDir = new basicItems_1.BasicDirectory(src, srcPath);
                srcDirs.set(src, srcDir);
                const files = glob.sync(path.join(srcPath, "*"));
                const result = [];
                for (const file of files) {
                    if (this.ignore.includes(file)) {
                        continue;
                    }
                    result.push(file);
                }
                srcDir.addPendings(result);
            }
            return srcDir;
        }
        catch (e) {
            return undefined;
        }
    }
    /**
     * Exlcude files from a list and return the result list.
     * @param fromList Exclude files from this list.
     * @param excludes Files need be excluded.
     */
    exlcude(fromList, excludes) {
        const result = [];
        fromList.forEach(filePath => {
            if (!excludes.includes(filePath)) {
                result.push(filePath);
            }
        });
        return result;
    }
    /**
     * Create the ignore files list.
     */
    makeIgnoreList() {
        try {
            const igonreArray = this.gvc.ignore();
            if (!igonreArray) {
                return false;
            }
            this.ignore = [];
            igonreArray.forEach(pattern => {
                const absPath = path.join(this.folderPath, pattern).replace(/\\/g, '/');
                if (this.hasWildChar(pattern)) {
                    const result = glob.sync(absPath);
                    this.ignore = this.ignore.concat(result);
                }
                else {
                    this.ignore.push(absPath);
                }
            });
            return true;
        }
        catch (e) {
            return false;
        }
    }
    makeDefaultRule() {
        try {
            this.default.delimiter = this.gvc.defaultDemiliter();
            this.default.level = this.gvc.defaultLevel();
            return true;
        }
        catch (e) {
            return false;
        }
    }
    getFileDir(filePath) {
        let pos = filePath.lastIndexOf("\\");
        if (pos < 0) {
            pos = filePath.lastIndexOf("/");
            if (pos < 0) {
                return '';
            }
        }
        return filePath.substring(0, pos);
    }
    getLabel(filePath) {
        let pos = filePath.lastIndexOf("\\");
        if (pos < 0) {
            pos = filePath.lastIndexOf("/");
            if (pos < 0) {
                return undefined;
            }
        }
        return filePath.substring(pos + 1);
    }
    hasWildChar(rule) {
        return rule.indexOf('*') > 0 || rule.indexOf('?') > 0;
    }
    compare(a, b) {
        const aName = a.label;
        const bName = b.label;
        return aName.localeCompare(bName);
    }
}
class GroupTree {
    constructor(configFileName, folderPaths) {
        this.subTrees = [];
        this.nodes = [];
        if (folderPaths.length == 0) {
            return;
        }
        for (const folder of folderPaths) {
            const tree = new SubGroupTree(configFileName, folder);
            this.subTrees.push(tree);
        }
    }
    build() {
        for (const tree of this.subTrees) {
            tree.make();
        }
        if (this.subTrees.length == 0) {
            return false;
        }
        else if (this.subTrees.length == 1) {
            this.nodes = this.subTrees[0].getChildren();
        }
        else {
            for (const tree of this.subTrees) {
                const folderPath = tree.getFolderPath();
                const folderName = tree.getFolderName();
                const parentDir = new basicItems_1.BasicDirectory(folderName, folderPath);
                parentDir.addAll(tree.getChildren());
                this.nodes.push(parentDir);
            }
            this.nodes.sort();
        }
        return true;
    }
    /**
     * Get all children of the given node.
     * @param node Parent node.
     */
    getChildren(node) {
        if (!node) {
            if (this.nodes.length == 1 && this.nodes[0] instanceof basicItems_1.BasicDirectory) {
                return this.nodes[0].getChildren();
            }
            return this.nodes;
        }
        // Multiple workspaces
        // If only 1 source directory, don't show it.
        // This is the same behavior when only 1 workspace folder
        if (vscode.workspace.workspaceFolders
            && vscode.workspace.workspaceFolders.length > 1
            && this.isWorkspaceFolder(node)) {
            const children = node.getChildren();
            if (children.length == 1) {
                return children[0].getChildren();
            }
        }
        const route = this.getRoute(node);
        let children = this.nodes;
        for (let i = route.length - 1; i >= 0; i--) {
            const item = route[i];
            let foundNode = false;
            for (const child of children) {
                if (child.filePath === item.tooltip) {
                    children = child.getChildren();
                    foundNode = true;
                    break;
                }
            }
            if (!foundNode) {
                return [];
            }
        }
        return children;
    }
    getRoute(node) {
        const route = [];
        let root = node;
        while (root) {
            const cur = root;
            root = root.parent;
            route.push(cur);
            if (!root) {
                break;
            }
        }
        return route;
    }
    isWorkspaceFolder(node) {
        for (const folder of this.nodes) {
            if (node.filePath === folder.filePath
                && node instanceof basicItems_1.BasicDirectory) {
                return true;
            }
        }
        return false;
    }
}
exports.GroupTree = GroupTree;
//# sourceMappingURL=groupTree.js.map