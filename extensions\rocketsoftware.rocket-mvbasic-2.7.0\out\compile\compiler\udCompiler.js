"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UdCompiler = void 0;
const compiler_1 = require("./compiler");
const provider_1 = require("../provider");
const extension_1 = require("../../extension");
const ol = require("../../config/onlineConfig");
class UdCompiler extends compiler_1.Compiler {
    constructor(def) {
        super();
        this.definition = def;
    }
    status(msg) {
        if (this.offline(msg)) {
            return compiler_1.Status.SERVER_NOT_CONNECTED;
        }
        return this.isIgnored(msg) ? compiler_1.Status.INVALID_FILE : this.isFailed(msg) ? compiler_1.Status.BUILD_FAILED : compiler_1.Status.BUILD_SUCCEED;
    }
    getConfig() {
        var _a;
        const compile = this.definition.compile;
        const compile_flavor = (_a = (extension_1.online === undefined ? (0, provider_1.getCatalogParameters)() : ol.getCatalogParameters()).ud_compile_flavor) !== null && _a !== void 0 ? _a : "UniBasic";
        const opt1 = compile_flavor;
        const opt2 = compile.catalog ? compile.catalog : "";
        const opt3 = compile.arguments ? compile.arguments : "";
        return {
            option1: opt1,
            option2: opt2,
            option3: opt3,
            doCatalog: opt2 !== ""
        };
    }
    // For UniData, files start with '_' are objective files.
    validate(fileName) {
        const lower = fileName.toLowerCase();
        const parts = lower.replace("\\", "/").split("/");
        const last = parts[parts.length - 1];
        return super.validate(fileName)
            || last.startsWith('_');
    }
}
exports.UdCompiler = UdCompiler;
//# sourceMappingURL=udCompiler.js.map