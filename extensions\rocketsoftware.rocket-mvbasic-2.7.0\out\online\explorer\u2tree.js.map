{"version": 3, "file": "u2tree.js", "sourceRoot": "", "sources": ["../../../src/online/explorer/u2tree.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,iCAAiC;AACjC,6BAA6B;AAC7B,6CAA6F;AAC7F,0CAA6C;AAC7C,kDAAkD;AAClD,iEAAgD;AAChD,uCAAsC;AACtC,iDAAiD;AACjD,+CAAyC;AAEzC,MAAa,cAAc;IAI1B,YAAoB,WAAiC,EAAU,SAA8B;QAAzE,gBAAW,GAAX,WAAW,CAAsB;QAAU,cAAS,GAAT,SAAS,CAAqB;QAFrF,yBAAoB,GAA4C,IAAI,MAAM,CAAC,YAAY,EAAsB,CAAC;QAC7G,wBAAmB,GAAqC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAGjG,CAAC;IAED,OAAO;QACN,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,WAAW,CAAC,OAAe;QAC1B,OAAO,OAAO,CAAC;IAChB,CAAC;IAED,WAAW,CAAC,OAAgB;QAC3B,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACP,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,eAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAClE,CAAC;IACF,CAAC;IAEa,gBAAgB,CAAC,GAAe;;;YAC7C,IAAI,OAAO,GAAkB,IAAI,KAAK,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAE3C,kFAAkF;YAClF,IAAI,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,EAAC,CAAC;gBAC5B,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;gBACjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAClC,CAAC;YACF,CAAC;iBAAM,IAAI,KAAK,YAAY,oBAAO,EAAE,CAAC;gBACrC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC9B,iDAAiD;oBACjD,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;wBAC5B,mBAAmB;wBACnB,MAAA,QAAQ,CAAC,UAAU,EAAE,0CAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;wBACxC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;4BACzB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;wBAC7B,CAAC,CAAC,CAAC;oBACJ,CAAC;gBACF,CAAC;YACF,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAS,EAAE,CAAC;gBACvC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC9B,iDAAiD;oBACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;oBACtC,IAAI,OAAO,EAAE,CAAC;wBACb,IAAI,KAAK,YAAY,mBAAM,EAAE,CAAC;4BAC7B,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE,CAAC;gCACvC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gCACtC,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gCAC9C,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC;oCACf,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;wCAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;oCAC1C,CAAC;gCACF,CAAC;4BACF,CAAC;wBACF,CAAC;6BAAM,CAAC;4BACP,mGAAmG;4BACnG,+CAA+C;4BAC/C,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACrC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACtB,OAAO,EAAE,CAAC;4BACX,CAAC;4BACD,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;4BAC5C,IAAI,WAAW,KAAK,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;gCACvC,MAAM,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gCAClC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;4BACtC,CAAC;4BAED,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;gCAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;4BAC1C,CAAC;wBACF,CAAC;oBACF,CAAC;gBACF,CAAC;gBACD,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACzB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,YAAY,iBAAI,EAAE,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACjC,CAAC;YAED,OAAO,OAAO,CAAC;QAChB,CAAC;KAAA;IAEO,KAAK,CAAC,GAAe,EAAE,KAAY,EAAE,OAAsB;QAClE,IAAI,KAAK,YAAY,mBAAM,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACxD,IAAI,MAAM,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE9D,wDAAwD;YACxD,MAAM,oBAAoB,GAAG,kBAAM,CAAC,uBAAuB,EAAE,CAAC;YAC9D,IAAI,QAAQ,CAAC,WAAW,EAAE,IAAI,oBAAoB,KAAK,SAAS,IAAI,oBAAoB,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC;gBAC3G,MAAM,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACvD,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,KAAK,YAAY,oBAAO,EAAE,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,KAAK,YAAY,iBAAI,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAS,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAC9C,CAAC;IACF,CAAC;IAEa,IAAI,CAAC,GAAe,EAAE,KAAY,EAAE,WAAmB;;;YACpE,IAAI,WAAW,GAAG,kBAAM,CAAC,uBAAuB,EAAE,CAAC;YACnD,IAAG,WAAW,KAAK,SAAS;gBAC3B,OAAO;YAER,IAAI,OAAO,GAAG,GAAG,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAE7D,IAAI,KAAK,YAAY,mBAAM,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC;iBAAM,IAAI,CAAA,MAAA,QAAQ,CAAC,UAAU,EAAE,0CAAE,OAAO,EAAE,MAAK,KAAK,CAAC,IAAI,EAAE,CAAC;gBAC5D,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACP,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;KAAA;IAEY,SAAS,CAAC,GAAe,EAAE,KAAa;;YACpD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;KAAA;IAEO,cAAc,CAAC,KAAa;QACnC,MAAM,QAAQ,GAAG,eAAS,GAAG,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;QAClD,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC1C,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,+BAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC;QACnF,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtB,CAAC;IAEa,SAAS,CAAC,GAAe,EAAE,WAAmB,EAAE,aAAqB;;YAClF,MAAM,OAAO,GAAG,+BAAO,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC3F,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;oBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,EAAE;wBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBAC7C,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACP,IAAI,OAAO,GAA0B,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;oBACrD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC/B,0CAA0C,GAAG,WAAW,GAAG,gDAAgD,EAC3G,OAAO,CAAC,CAAC;gBACX,CAAC;YACF,CAAC,CAAC,CAAC;QACJ,CAAC;KAAA;IAEa,aAAa,CAAC,GAAe,EAAE,oBAA4B;;YACxE,IAAI,QAAQ,GAAa,MAAM,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAC7E,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,sDAAsD;gBACtD,KAAK,MAAM,WAAW,IAAI,QAAQ,EAAE,CAAC;oBACpC,aAAa;oBACb,IAAI,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;oBACvD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBACzC,CAAC;YACF,CAAC;QACF,CAAC;KAAA;IAEa,eAAe,CAAC,GAAe,EAAE,WAAmB,EAAE,aAAqB;;YACxF,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,+BAAO,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;YAC9F,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC1C,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAe,EAAE,EAAE;oBACpC,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBACrD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;oBAC1F,kDAAkD;gBACnD,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAA;QACH,CAAC;KAAA;CACD;AAxLD,wCAwLC;AAED,MAAa,MAAO,SAAQ,MAAM,CAAC,QAAQ;IAE1C,YACiB,KAAa,EACb,GAAe,EACxB,gBAAiD,EACxC,OAAwB;QAExC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QALf,UAAK,GAAL,KAAK,CAAQ;QACb,QAAG,GAAH,GAAG,CAAY;QACxB,qBAAgB,GAAhB,gBAAgB,CAAiC;QACxC,YAAO,GAAP,OAAO,CAAiB;QAKzC,YAAO,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAE/B,gBAAW,GAAG,EAAE,CAAC;QACjB,iBAAY,GAAG,QAAQ,CAAC;IALxB,CAAC;CAMD;AAfD,wBAeC;AAED,MAAa,KAAM,SAAQ,MAAM;IAChC,YACiB,KAAa,EACb,GAAe;QAC/B,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAF7C,UAAK,GAAL,KAAK,CAAQ;QACb,QAAG,GAAH,GAAG,CAAY;QAIhC,iBAAY,GAAG,OAAO,CAAC;IAFvB,CAAC;CAGD;AARD,sBAQC;AAED,MAAa,MAAO,SAAQ,MAAM;IACjC,YACiB,KAAa,EACb,GAAe;QAC/B,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE;YACvD,OAAO,EAAE,sBAAsB;YAC/B,KAAK,EAAE,EAAE;YACT,SAAS,EAAE,CAAC,GAAG,CAAC;SAChB,CAAC,CAAC;QANa,UAAK,GAAL,KAAK,CAAQ;QACb,QAAG,GAAH,GAAG,CAAY;QAQhC,iBAAY,GAAG,QAAQ,CAAC;IAFxB,CAAC;CAGD;AAZD,wBAYC;AAED,MAAa,QAAS,SAAQ,MAAM;IACnC,YACiB,KAAa,EACb,OAAe,EACf,GAAe;QAC/B,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAH7C,UAAK,GAAL,KAAK,CAAQ;QACb,YAAO,GAAP,OAAO,CAAQ;QACf,QAAG,GAAH,GAAG,CAAY;QAIhC,aAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,iBAAY,GAAG,UAAU,CAAC;QAC1B,YAAO,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAJ5B,CAAC;IAMM,UAAU;QAChB,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;IACpC,CAAC;IAEM,SAAS;QACf,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAEM,YAAY;QAClB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACvB,CAAC;CACD;AAvBD,4BAuBC;AAED,MAAa,SAAU,SAAQ,MAAM;IACpC,YACiB,KAAa,EACb,GAAe;QAE/B,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,wBAAwB,CAAC,SAAS,EAAE;YAC5D,OAAO,EAAE,iBAAiB;YAC1B,KAAK,EAAE,EAAE;YACT,SAAS,EAAE,CAAC,GAAG,CAAC;SAChB,CAAC,CAAC;QAPa,UAAK,GAAL,KAAK,CAAQ;QACb,QAAG,GAAH,GAAG,CAAY;QAShC,iBAAY,GAAG,WAAW,CAAC;QAC3B,gBAAW,GAAG,EAAE,CAAC;IAHjB,CAAC;CAID;AAdD,8BAcC"}