/// <reference types="node" />
import { <PERSON><PERSON><PERSON><PERSON>, MessageWriter, Logger, ConnectionStrategy, ConnectionOptions, ProtocolConnection } from '../common/api';
export * from 'vscode-jsonrpc/node';
export * from '../common/api';
export declare function createProtocolConnection(input: MessageReader, output: MessageWriter, logger?: Logger, options?: ConnectionStrategy | ConnectionOptions): ProtocolConnection;
export declare function createProtocolConnection(input: NodeJS.ReadableStream, output: NodeJS.WritableStream, logger?: Logger, options?: ConnectionStrategy | ConnectionOptions): ProtocolConnection;
