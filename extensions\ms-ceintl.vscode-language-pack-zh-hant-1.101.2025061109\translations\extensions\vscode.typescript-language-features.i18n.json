{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"(loading...)/Prefix displayed for hover entries while the server is still loading": "(正在載入...)", "...1 additional file not shown": "...另外 1 個檔案未顯示", "...{0} additional files not shown": "...另外 {0} 個檔案未顯示", "1 implementation": "1 個實作", "1 reference": "1 個參考", "Acquiring typings definitions for IntelliSense./Typings refers to the *.d.ts typings files that power our IntelliSense. It should not be localized": "正在為 IntelliSense 擷取 typings 定義。", "Acquiring typings.../Typings refers to the *.d.ts typings files that power our IntelliSense. It should not be localized": "正在取得 typings...", "Add all missing imports": "新增所有缺少的匯入", "Allow": "允許", "Always": "一律", "An error occurred while renaming file": "重新命名檔案時發生錯誤", "Analyzing '{0}' and its dependencies": "正在分析 '{0}' 及其相依性", "Checking for update of JS/TS imports": "正在檢查 JS/TS 匯入的更新", "Configure Excludes": "設定排除項目", "Configure JSConfig": "設定 JSConfig", "Configure TSConfig": "設定 TSConfig", "Configure jsconfig.json": "設定 jsconfig.json", "Configure tsconfig.json": "設定 tsconfig.json", "Could not apply refactoring": "無法套用重構", "Could not detect a Node installation to run TS Server.": "無法偵測節點安裝以執行 TS 伺服器。", "Could not determine TypeScript or JavaScript project": "無法判斷 TypeScript 或 JavaScript 專案", "Could not determine TypeScript or JavaScript project. Unsupported file type": "無法判斷 TypeScript 或 JavaScript 專案。不支援的檔案類型", "Could not determine references": "無法判斷參考", "Could not install typings files for JavaScript language features. Please ensure that NPM is installed, or configure 'typescript.npm' in your user settings. Alternatively, check the [documentation]({0}) to learn more.": "無法為 JavaScript 語言功能安裝輸入檔案。請確認已安裝 NPM，或在使用者設定中設定 'typescript.npm'。若要深入了解，請按一下 [這裡]({0})。", "Could not load the TypeScript version at this path": "無法在此路徑載入 TypeScript 版本", "Could not open TS Server log file": "無法開啟 TS 伺服器記錄檔", "Disable logging": "停用記錄", "Disables semantic checking in a JavaScript file. Must be at the top of a file.": "停用 JavaScript 檔案的語意檢查。必須在檔案的最上面。", "Dismiss": "關閉", "Don't Show Again": "不要再顯示", "Don't show again": "不要再顯示", "Enable logging and restart TS server": "啟用記錄功能並重新啟動 TS 伺服器", "Enables semantic checking in a JavaScript file. Must be at the top of a file.": "啟用 JavaScript 檔案的語意檢查。必須在檔案的最上面。", "Enter file path": "輸入檔案路徑", "Enter new file path...": "輸入新的檔案路徑...", "Extract to constant": "擷取至常數", "Extract to function": "擷取至函式", "Failed to resolve {0} as module": "無法將 {0} 解析為模組", "Fetching data for better TypeScript IntelliSense": "正在擷取資料以改善 TypeScript IntelliSense", "File is not part of a JavaScript project. View the [jsconfig.json documentation]({0}) to learn more.": "檔案並非 JavaScript 專案的一部分。檢視 [tsconfig.json 文件]({0}) 以深入了解。", "File is not part of a TypeScript project. View the [tsconfig.json documentation]({0}) to learn more.": "檔案並非 TypeScript 專案的一部分。檢視 [tsconfig.json 文件]({0}) 以深入了解。", "File is not part opened folders": "檔案不是開啟資料夾的一部分", "Find file references failed. No resource provided.": "尋找檔案參考失敗。未提供任何資源。", "Find file references failed. Requires TypeScript 4.2+.": "尋找檔案參考失敗。需要 TypeScript 4.2+。", "Find file references failed. Unknown file type.": "尋找檔案參考失敗。未知的檔案類型。", "Find file references failed. Unsupported file type.": "尋找檔案參考失敗。不支援的檔案類型。", "Finding file references": "正在尋找檔案參考", "Finding source definitions": "正在尋找來源定義", "Fix all fixable JS/TS issues": "修正所有可修正的 JS/TS 問題", "Follow link": "追蹤連結", "Go to Source Definition failed. No resource provided.": "前往來源定義失敗。未提供資源。", "Go to Source Definition failed. Requires TypeScript 4.7+.": "前往來源定義失敗。需要 TypeScript 4.7+。", "Go to Source Definition failed. Unknown file type.": "前往來源定義失敗。不明檔案類型。", "Go to Source Definition failed. Unsupported file type.": "前往來源定義失敗。不支援的檔案類型。", "Initializing '{0}'": "正在初始化 '{0}'", "JS/TS IntelliSense Status": "JS/TS IntelliSense 狀態", "JSDoc comment": "JSDoc 註解", "Learn More": "深入了解", "Learn more about JS/TS refactorings": "深入了解 JS/TS 重構", "Learn more about managing TypeScript versions": "深入了解管理 TypeScript 版本", "Loading IntelliSense status": "正在載入 IntelliSense 狀態", "Move to File": "移動至檔案", "Never": "永不", "Never in this Workspace": "絕不在此工作區使用", "No": "否", "No jsconfig": "無 jsconfig", "No opened folders": "沒有開啟的資料夾", "No source definitions found.": "找不到來源定義。", "No tsconfig": "無 tsconfig", "Not now": "現在不要", "Open Config File": "開啟設定檔", "Open on GitHub": "在 GitHub 上開啟", "Organize Imports": "組織匯入", "Partial mode": "部分模式", "Paste with imports": "以匯入貼上", "Please open a folder in VS Code to use a TypeScript or JavaScript project": "請在 VS Code 中開啟資料夾，以使用 TypeScript 或 JavaScript 專案", "Please report an issue against Yarn PnP": "請針對 Yarn PnP 回報問題", "Please update your TypeScript version": "請更新您的 TypeScript 版本", "Project wide IntelliSense not available": "無法使用整個專案的 IntelliSense", "Remove Unused Imports": "移除未使用的匯入", "Remove all unused code": "移除所有未使用的程式碼", "Report Issue": "回報問題", "Report issue against Yarn PnP": "針對 Yarn PnP 回報問題", "Select Version": "選取版本", "Select code action to apply": "選擇要套用的程式碼動作", "Select existing file...": "選取現有的檔案...", "Select move destination": "選取移動目的地", "Select the TypeScript version used for JavaScript and TypeScript language features": "選取 JavaScript 與 TypeScript 功能使用的 TypeScript 版本", "Sort Imports": "排序匯入", "Suppresses @ts-check errors on the next line of a file, expecting at least one to exist.": "隱藏檔案下一行上的 @ts-check 錯誤，預計至少會有一個。", "Suppresses @ts-check errors on the next line of a file.": "隱藏下一行@ts-check 的錯誤警告。", "TS Server has not started logging.": "TS 伺服器尚未開始記錄。", "TS Server logging is currently enabled which may impact performance.": "TS 伺服器記錄目前已啟用，這可能會影響效能。", "TS Server logging is off. Please set 'typescript.tsserver.log' and restart the TS server to enable logging": "TS 伺服器記錄功能已關閉。請設定 'typescript.tsserver.log' 並重新啟動 TS 伺服器，以啟用記錄功能", "The JS/TS language service crashed 5 times in the last 5 Minutes.": "JS/TS 語言服務過去 5 分鐘內當機 5 次。", "The JS/TS language service crashed 5 times in the last 5 Minutes.\nThis may be caused by a plugin contributed by one of these extensions: {0}\nPlease try disabling these extensions before filing an issue against VS Code.": "JS/TS 語言服務過去 5 分鐘內當機 5 次。\n這可能是下列其中一個延伸模組所提供的外掛程式所造成: {0}\n請先嘗試停用這些延伸模組，再提出 VS Code 問題。", "The JS/TS language service crashed.": "JS/TS 語言服務已當機。", "The JS/TS language service crashed.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.": "JS/TS 語言服務已當機。\n這可能是下列其中一個延伸模組所提供的外掛程式所造成: {0}。\n請先嘗試停用這些延伸模組，再提出 VS Code 問題。", "The JS/TS language service immediately crashed 5 times. The service will not be restarted.": "JS/TS 語言服務立即當機 5 次。服務將不會重新啟動。", "The JS/TS language service immediately crashed 5 times. The service will not be restarted.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.": "JS/TS 語言服務立即當機 5 次。服務將不會重新啟動。\n這可能是下列其中一個延伸模組所提供的外掛程式所造成: {0}。\n請先嘗試停用這些延伸模組，再提出 VS Code 問題。", "The TypeScript Go extension is not installed.": "未安裝 TypeScript Go 延伸模組。", "The current selection cannot be extracted": "無法擷取目前的選取", "The path {0} doesn't point to a valid Node installation to run TS Server. Falling back to bundled Node.": "路徑 {0} 未指向有效的節點安裝來執行 TS Server。跌回至綑綁節點。", "The path {0} doesn't point to a valid tsserver install. Falling back to bundled TypeScript version.": "路徑 {0} 未指向有效的 tsserver 安裝。即將回復為配套的 TypeScript 版本。", "The workspace is using a version of the TypeScript Server that has been patched by Yarn PnP. This patching is a common source of bugs.": "工作區使用的 TypeScript Server 版本已由 Yarn PnP 修補。此修補是錯誤 (bug) 的常見來源。", "The workspace is using an old version of TypeScript ({0}).\n\nBefore reporting an issue, please update the workspace to use TypeScript {1} or newer to make sure the bug has not already been fixed.": "工作區使用的是舊版 TypeScript ({0})。\n\n在回報問題之前，請先將工作區更新為使用 TypeScript {1} 或更新，以確保錯誤尚未修正。", "This workspace contains a TypeScript version. Would you like to use the workspace TypeScript version for TypeScript and JavaScript language features?": "此工作區包含 TypeScript 版本。要使用工作區 TypeScript 版本以取得 TypeScript 和 JavaScript 語言功能嗎?", "This workspace wants to use the Node installation at '{0}' to run TS Server. Would you like to use it?": "此工作區想要使用位於 '{0}' 的節點安裝來執行 TS 伺服器。您要使用它嗎?", "To enable project-wide JavaScript/TypeScript language features, exclude folders with many files, like: {0}": "若要讓整個專案都能使用 JavaScript/TypeScript 語言功能，請排除內含許多檔案的資料夾，例如: {0}", "To enable project-wide JavaScript/TypeScript language features, exclude large folders with source files that you do not work on.": "若要讓整個專案都能使用 JavaScript/TypeScript 語言功能，請排除內含您未使用之來源檔案的大型資料夾。", "TypeScript Server Log": "TypeScript 伺服器記錄", "TypeScript Task in tasks.json contains \"\\\\\". TypeScript tasks tsconfig must use \"/\"": "tasks.json 中的 TypeScript 工作含有 \"\\\\\"。TypeScript 工作 tsconfig 必須使用 \"/\"", "TypeScript Version": "TypeScript 版本", "TypeScript language server exited with error. Error message is: {0}": "TypeScript 語言伺服器因發生錯誤而結束。錯誤訊息為: {0}", "TypeScript version": "TypeScript 版本", "TypeScript: Configure Excludes": "TypeScript: 組態排除", "Update imports for '{0}'?": "是否更新 '{0}' 的匯入?", "Update imports for the following {0} files?": "是否更新下列 {0} 檔案的匯入?", "Use VS Code's Version": "使用 VS Code 的版本", "Use Workspace Version": "使用工作區版本", "VS Code's tsserver was deleted by another application such as a misbehaving virus detection tool. Please reinstall VS Code.": "其他應用程式已刪除了 VS Code 的 tsserver，例如行為不當的病毒偵測工具。請重新安裝 VS Code。", "Yes": "是", "build - {0}": "建置 - {0}", "destination files": "目的地檔案", "invalid version": "無效的版本", "watch - {0}": "監看 - {0}", "{0} (Fix all in file)": "{0} (檔案中修復全部)", "{0} implementations": "{0} 個實作", "{0} references": "{0} 個參考"}, "package": {"configuration.expandableHover": "啟用滑鼠懸停時的展開/收合功能，以顯示更多或更少來自 TS 伺服器的資訊。需要 TypeScript 5.9+。", "configuration.format": "格式化", "configuration.hover.maximumLength": "一次暫留的字元數上限。如果暫留長度超過此值，就會被截斷。需要 TypeScript 5.9+。", "configuration.implicitProjectConfig.checkJs": "啟用/停用 JavaScript 檔案的語意檢查。現有的 `jsconfig.json` 或 `tsconfig.json` 檔案會覆寫此設定。", "configuration.implicitProjectConfig.experimentalDecorators": "在不屬於專案的 JavaScript 檔案中，啟用/停用 `experimentalDecorators`。現有的 `jsconfig.json` 或 `tsconfig.json` 檔案會覆寫此設定。", "configuration.implicitProjectConfig.module": "設定程式的模組系統。查看更多: https://www.typescriptlang.org/tsconfig#module。", "configuration.implicitProjectConfig.strictFunctionTypes": "在不屬於專案的 JavaScript 和 TypeScript 檔案中，啟用/停用[嚴格函式類型](https://www.typescriptlang.org/tsconfig#strictFunctionTypes)。現有的 `jsconfig.json` 或 `tsconfig.json` 檔案會覆寫此設定。", "configuration.implicitProjectConfig.strictNullChecks": "在不屬於專案的 JavaScript 和 TypeScript 檔案中，啟用/停用[嚴格 Null 檢查](https://www.typescriptlang.org/tsconfig#strictNullChecks)。現有的 `jsconfig.json` 或 `tsconfig.json` 檔案會覆寫此設定。", "configuration.implicitProjectConfig.target": "為發出的 JavaScript 設定目標 JavaScript 語言版本，並包含程式庫宣告。查看更多: https://www.typescriptlang.org/tsconfig#target。", "configuration.inlayHints": "內嵌提示", "configuration.inlayHints.enumMemberValues.enabled": "針對列舉宣告上的成員值啟用/停用內嵌提示:\r\n```typescript\r\n\r\nenum MyValue {\r\n\tA /* = 0 */;\r\n\tB /* = 1 */;\r\n}\r\n \r\n```", "configuration.inlayHints.functionLikeReturnTypes.enabled": "針對函數簽章上的隱含類型啟用/停用內嵌提示:\r\n```typescript\r\n\r\nfunction foo() /* :number */ {\r\n\treturn Date.now();\r\n} \r\n \r\n```", "configuration.inlayHints.parameterNames.enabled": "啟用/停用參數名稱的內嵌提示:\r\n```typescript\r\n\r\nparseInt(/* str: */ '123', /* radix: */ 8)\r\n \r\n```", "configuration.inlayHints.parameterNames.suppressWhenArgumentMatchesName": "在其文字與參數名稱相同的引數上隱藏參數名稱提示。", "configuration.inlayHints.parameterTypes.enabled": "針對隱含參數類型啟用/停用內嵌提示:\r\n```typescript\r\n\r\nel.addEventListener('click', e /* :MouseEvent */ => ...)\r\n \r\n```", "configuration.inlayHints.propertyDeclarationTypes.enabled": "針對屬性宣告上的隱含類型啟用/停用內嵌提示:\r\n```typescript\r\n\r\nclass Foo {\r\n\tprop /* :number */ = Date.now();\r\n}\r\n \r\n```", "configuration.inlayHints.variableTypes.enabled": "啟用/停用隱含變數類型的內嵌提示:\r\n```typescript\r\n\r\nconst foo /* :number */ = Date.now();\r\n \r\n```", "configuration.inlayHints.variableTypes.suppressWhenTypeMatchesName": "抑制名稱與類型名稱相同之變數的類型提示。", "configuration.preferGoToSourceDefinition": "讓 [前往定義] 盡可能避免型別宣告檔案，方法是改為觸發 [前往來源定義]。這會允許使用滑鼠手勢觸發 [前往來源定義]。", "configuration.preferences": "喜好設定", "configuration.server": "TS 伺服器", "configuration.suggest": "建議", "configuration.suggest.autoImports": "啟用/停用自動匯入建議。", "configuration.suggest.classMemberSnippets.enabled": "啟用/停用來自類別成員的程式碼片段完成。", "configuration.suggest.completeFunctionCalls": "使用其參數簽章完成函式。", "configuration.suggest.completeJSDocs": "啟用/停用完成 JSDoc 註解的建議。", "configuration.suggest.includeAutomaticOptionalChainCompletions": "啟用/停用顯示插入選擇性鏈結呼叫之潛在未定義值的自動完成。需要啟用嚴格 Null 檢查。", "configuration.suggest.includeCompletionsForImportStatements": "在已部分鍵入的匯入陳述式上啟用/停用自動匯入樣式完成。", "configuration.suggest.jsdoc.generateReturns": "啟用/停用對 JSDoc 範本產生的 `@returns` 注釋。", "configuration.suggest.names": "啟用/停用在 JavaScript 建議中包含檔案的唯一名稱。請注意，在使用 `@ts-check` 或 `checkJs` 進行語意檢查的 JavaScript 程式碼中，一律會停用名稱建議。", "configuration.suggest.objectLiteralMethodSnippets.enabled": "為物件常值中的方法啟用/停用程式碼片段完成。", "configuration.suggest.paths": "啟用/停用匯入陳述式和要求呼叫中的路徑建議。", "configuration.tsserver.experimental.enableProjectDiagnostics": "啟用整個專案的錯誤報告。", "configuration.tsserver.maxTsServerMemory": "配置給 TypeScript 伺服器處理序的記憶體 (MB) 量上限。若要使用大於 4 GB 的記憶體限制，請使用 '#typescript.tsserver.nodePath#' 以自訂節點安裝來執行 TS Server。", "configuration.tsserver.nodePath": "在自訂節點安裝上執行 TS 伺服器。如果您要 VS Code偵測節點安裝，這可以是節點可執行檔的路徑，也可以是 'node'。", "configuration.tsserver.useSeparateSyntaxServer": "啟用/停用繁衍可更快回應語法相關作業 (例如計算摺疊或計算文件符號) 的獨立 TypeScript 伺服器。", "configuration.tsserver.useSyntaxServer": "控制 TypeScript 是否會啟動專用伺服器，以更快速地處理與語法相關的作業，例如運算程式碼摺疊功能。", "configuration.tsserver.useSyntaxServer.always": "使用輕量化的語法伺服器來處理所有 IntelliSense 作業。此語法伺服器只能為開啟的檔案提供 IntelliSense。", "configuration.tsserver.useSyntaxServer.auto": "同時繁衍專用於語法作業的完整伺服器和輕量化伺服器。語法伺服器可用來加快語法作業，並在載入專案時提供 IntelliSense。", "configuration.tsserver.useSyntaxServer.never": "不要使用專用的語法伺服器。使用單一伺服器來處理所有 IntelliSense 作業。", "configuration.tsserver.useVsCodeWatcher": "使用 VS Code 的檔案監看員，而非 TypeScript 的。需要在工作區中使用 TypeScript 5.4+。", "configuration.tsserver.watchOptions": "設定要用來追蹤檔案與目錄的監視策略。", "configuration.tsserver.watchOptions.fallbackPolling": "使用檔案系統事件時，此選項會指定當系統用完原生檔案監控程式且 (或) 不支援原生檔案監控程式時，所要使用的輪詢策略。", "configuration.tsserver.watchOptions.fallbackPolling.dynamicPriorityPolling ": "使用動態佇列，使較少修改的檔案檢查頻率較低。", "configuration.tsserver.watchOptions.fallbackPolling.fixedPollingInterval": "以固定間隔每秒檢查所有檔案的變更數次。", "configuration.tsserver.watchOptions.fallbackPolling.priorityPollingInterval": "每秒檢查所有檔案的變更數次，但使用啟發學習法檢查特定類型的檔案 (頻率較其他類型低)。", "configuration.tsserver.watchOptions.synchronousWatchDirectory": "停用目錄的延遲監視。如果有大量檔案變更可能同時發生時 (例如執行 npm install 的 node_modules 變更)，延遲監視相當實用，但建議您使用此旗標予以停用以執行某些不太常見的設定。", "configuration.tsserver.watchOptions.vscode": "使用 VS Code 的檔案監看員，而非 TypeScript 的。需要在工作區中使用 TypeScript 5.4+。", "configuration.tsserver.watchOptions.watchDirectory": "在缺乏遞迴檔案監視功能的系統下，監視整個目錄樹狀的策略。", "configuration.tsserver.watchOptions.watchDirectory.dynamicPriorityPolling": "使用動態佇列，使較少修改的目錄檢查頻率較低。", "configuration.tsserver.watchOptions.watchDirectory.fixedChunkSizePolling": "定期輪詢區塊中的目錄。", "configuration.tsserver.watchOptions.watchDirectory.fixedPollingInterval": "以固定間隔每秒檢查所有目錄的變更數次。", "configuration.tsserver.watchOptions.watchDirectory.useFsEvents": "嘗試使用作業系統/檔案系統的原生事件，來進行目錄變更。", "configuration.tsserver.watchOptions.watchFile": "監視個別檔案的策略。", "configuration.tsserver.watchOptions.watchFile.dynamicPriorityPolling": "使用動態佇列，使較少修改的檔案檢查頻率較低。", "configuration.tsserver.watchOptions.watchFile.fixedChunkSizePolling": "定期輪詢區塊中的檔案。", "configuration.tsserver.watchOptions.watchFile.fixedPollingInterval": "以固定間隔每秒檢查所有檔案的變更數次。", "configuration.tsserver.watchOptions.watchFile.priorityPollingInterval": "每秒檢查所有檔案的變更數次，但使用啟發學習法檢查特定類型的檔案 (頻率較其他類型低)。", "configuration.tsserver.watchOptions.watchFile.useFsEvents": "嘗試使用作業系統/檔案系統的原生事件，來進行檔案變更。", "configuration.tsserver.watchOptions.watchFile.useFsEventsOnParentDirectory": "嘗試使用作業系統/檔案系統的原生事件在包含目錄的檔案上接聽變更。這會使用較少的檔案監控程式，但可能較不準確。", "configuration.tsserver.web.projectWideIntellisense.enabled": "啟用/停用全專案 IntelliSense 網頁。要求 VS 程式碼在受信任的內容中執行。", "configuration.tsserver.web.projectWideIntellisense.suppressSemanticErrors": "即使已啟用全專案 IntelliSense，也抑制網頁語意錯誤。全專案 IntelliSense 未啟用或無法使用時，一律會啟用此項目。請參閱 `#typescript.tsserver.web.projectWideIntellisense.enabled#`", "configuration.tsserver.web.typeAcquisition.enabled": "啟用/停用網路上的套件擷取。這會為匯入的套件啟用 IntelliSense。需要 `#typescript.tsserver.web.projectWideIntellisense.enabled#`。目前不支援 Safari。", "configuration.typescript": "TypeScript", "configuration.updateImportsOnPaste": "貼上程式碼時自動更新匯入。需要 TypeScript 5.6+。", "description": "為 JavaScript 和 TypeScript 提供豐富的語言支援。", "displayName": "TypeScript 和 JavaScript 語言功能", "format.indentSwitchCase": "將 switch 陳述式中的大小寫子句縮排。需要在工作區中使用 TypeScript 5.1+。", "format.insertSpaceAfterCommaDelimiter": "定義逗號分隔符號後的空格處理。", "format.insertSpaceAfterConstructor": "定義建構函式關鍵字後的空格處理方式。", "format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": "定義匿名函式之函式關鍵字後的空格處理。", "format.insertSpaceAfterKeywordsInControlFlowStatements": "定義控制流程陳述式內關鍵字後的空格處理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces": "定義左大括弧與右大括弧之間空白時的空格處理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": "定義開啟 JSX 運算式大括號後和將其關閉前的空格處理。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": "定義非空白的左大括弧後及右大括弧前的空格處理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": "定義左右非空白括弧間的空格處理。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": "定義左右非空白括弧間的空格處理。", "format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": "定義開啟範本字串大括號後和將其關閉前的空格處理。 ", "format.insertSpaceAfterSemicolonInForStatements": "定義 for 陳述式內分號後的空格處理。", "format.insertSpaceAfterTypeAssertion": "定義 TypeScript 中類型判斷提示後的空格處理方式。", "format.insertSpaceBeforeAndAfterBinaryOperators": "定義二元運算子後的空格處理。", "format.insertSpaceBeforeFunctionParenthesis": "定義函式引數括號之前的空格處理。", "format.placeOpenBraceOnNewLineForControlBlocks": "定義是否將左大括弧放入控制區塊的新行。", "format.placeOpenBraceOnNewLineForFunctions": "定義是否將左大括弧放入函式的新行。", "format.semicolons": "定義選擇性分號的處理。", "format.semicolons.ignore": "不要插入或移除任何分號。", "format.semicolons.insert": "在陳述式結尾插入分號。", "format.semicolons.remove": "移除不必要的分號。", "inlayHints.parameterNames.all": "只對常值和非常值引數啟用參數名稱提示。", "inlayHints.parameterNames.literals": "只對常值引數啟用參數名稱提示。", "inlayHints.parameterNames.none": "停用參數名稱提示。", "javascript.format.enable": "啟用/停用預設 JavaScript 格式器。", "javascript.goToProjectConfig.title": "前往專案設定 (jsconfig / tsconfig)", "javascript.preferences.jsxAttributeCompletionStyle.auto": "根據屬性類型，在屬性名稱後插入 `={}` 或 `=\"\"`。請參閱 `#javascript.preferences.quoteStyle#`，以控制用於字串屬性的引號類型。", "javascript.preferences.organizeImports": "控制匯入順序的進階喜好設定。", "javascript.referencesCodeLens.enabled": "在 JavaScript 檔案啟用/停用參考 CodeLens。", "javascript.referencesCodeLens.showOnAllFunctions": "啟用/停用 JavaScript 檔案中所有函式的參考 CodeLens。", "javascript.suggestionActions.enabled": "在編輯器中為 JavaScript 檔案啟用/停用建議診斷。", "javascript.validate.enable": "啟用/停用 JavaScript 驗證。", "reloadProjects.title": "重新載入專案", "taskDefinition.tsconfig.description": "定義 TS 組建的 tsconfig 檔案。", "typescript.autoClosingTags": "啟用/停用 JSX 標籤的自動結束。", "typescript.check.npmIsInstalled": "檢查是否已為 [自動類型取得](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition) 安裝 npm.", "typescript.disableAutomaticTypeAcquisition": "停用 [自動類型取得](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition)。自動類型取得會從 npm 擷取 '@types' 套件，以改善外部程式庫的 IntelliSense。", "typescript.enablePromptUseWorkspaceTsdk": "可提示使用者為 Intellisense 使用在工作區中設定的 TypeScript 版本。", "typescript.findAllFileReferences": "尋找檔案參考", "typescript.format.enable": "啟用/停用預設 TypeScript 格式器。", "typescript.goToProjectConfig.title": "前往專案設定 (tsconfig)", "typescript.goToSourceDefinition": "前往來源定義", "typescript.implementationsCodeLens.enabled": "啟用/停用實作 CodeLens。此 CodeLens 會顯示介面的實作。", "typescript.implementationsCodeLens.showOnInterfaceMethods": "在介面方法上啟用/停用 CodeLens 實作。", "typescript.locale": "設定用於回報 JavaScript 與 TypeScript 錯誤的地區設定。預設值會使用 VS Code 的地區設定。", "typescript.locale.auto": "使用 VS Code 設定的顯示語言。", "typescript.npm": "指定用於 [自動類型取得](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition) 的 npm 可執行檔路徑。", "typescript.openTsServerLog.title": "開啟 TS 伺服器記錄", "typescript.preferences.autoImportFileExcludePatterns": "指定要從自動匯入排除之檔案的 Glob 模式。相對路徑已相對於工作區根目錄解析。使用 tsconfig.json [`exclude`](https://www.typescriptlang.org/tsconfig#exclude) 語意評估模式。", "typescript.preferences.autoImportSpecifierExcludeRegexes": "指定規則運算式，以排除具有相符匯入規範的自動匯入。範例:\r\n\r\n- `^node:`\r\n- 'lib/internal' (斜線不需要逸出...)\r\n- '/lib\\/internal/i' (...除非包含 `i` 或 `u` 旗標的周圍斜線)\r\n- '^lodash$' (只允許從 lodash 匯入子路徑)", "typescript.preferences.importModuleSpecifier": "自動匯入的偏好路徑樣式。", "typescript.preferences.importModuleSpecifier.nonRelative": "建議使用以您 `jsconfig.json` / `tsconfig.json` 中設定之 `baseUrl` 或 `paths` 為基礎的非相對匯入。", "typescript.preferences.importModuleSpecifier.projectRelative": "只有在相對匯入路徑會離開套件或專案目錄時，才建議使用非相對匯入。", "typescript.preferences.importModuleSpecifier.relative": "建議使用匯入之檔案位置的相對路徑。", "typescript.preferences.importModuleSpecifier.shortest": "只有在非相對匯入的路徑區段比相對匯入更少時，才建議使用非相對匯入。", "typescript.preferences.importModuleSpecifierEnding": "自動匯入的慣用路徑結尾。", "typescript.preferences.importModuleSpecifierEnding.auto": "使用專案設定，選取預設值。", "typescript.preferences.importModuleSpecifierEnding.index": "將 `./component/index.js` 縮短為 `./component/index`。", "typescript.preferences.importModuleSpecifierEnding.js": "請勿縮短路徑結尾; 要包含 `.js` 或 `.ts` 副檔名。", "typescript.preferences.importModuleSpecifierEnding.label.js": ".js / .ts", "typescript.preferences.importModuleSpecifierEnding.minimal": "將 `./component/index.js` 縮短為 `./component`。", "typescript.preferences.includePackageJsonAutoImports": "啟用/停用可以自動匯入之項目搜尋 'package.json' 相依性的功能。", "typescript.preferences.includePackageJsonAutoImports.auto": "依據預估對效能的影響搜尋相依性。", "typescript.preferences.includePackageJsonAutoImports.off": "一律不搜尋相依性。", "typescript.preferences.includePackageJsonAutoImports.on": "一律搜尋相依性。", "typescript.preferences.jsxAttributeCompletionStyle": "JSX 屬性完成的慣用樣式。", "typescript.preferences.jsxAttributeCompletionStyle.auto": "根據屬性類型，在屬性名稱後插入 `={}` 或 `=\"\"`。請參閱 `#typescript.preferences.quoteStyle#`，以控制用於字串屬性的引號類型。", "typescript.preferences.jsxAttributeCompletionStyle.braces": "在屬性名稱後面插入 `={}`。", "typescript.preferences.jsxAttributeCompletionStyle.none": "僅插入屬性名稱。", "typescript.preferences.organizeImports": "控制匯入順序的進階喜好設定。", "typescript.preferences.organizeImports.accentCollation": "需要 `organizeImports.unicodeCollation: 'unicode'`。將具有變音符號的字元比較為與基底字元不相等。", "typescript.preferences.organizeImports.caseFirst": "需要 `organizeImports.unicodeCollation: 'unicode'`，且 `organizeImports.caseSensitivity` 不是 `caseInsensitive`。指出大寫是否將排序在小寫之前。", "typescript.preferences.organizeImports.caseFirst.default": "`locale` 提供的預設順序。", "typescript.preferences.organizeImports.caseFirst.lower": "小寫在大寫之前。例如 `a、A、z、Z`。", "typescript.preferences.organizeImports.caseFirst.upper": "大寫在小寫之前。例如 `A、a、B、b`。", "typescript.preferences.organizeImports.caseSensitivity": "指定如何針對區分大小寫排序匯入。如果為 `auto` 或未指定，我們會偵測每個檔案的區分大小寫", "typescript.preferences.organizeImports.caseSensitivity.auto": "偵測用於匯入排序的區分大小寫。", "typescript.preferences.organizeImports.caseSensitivity.insensitive": "排序匯入時不區分大小寫。", "typescript.preferences.organizeImports.caseSensitivity.sensitive": "排序匯入時區分大小寫。", "typescript.preferences.organizeImports.locale": "需要 `organizeImports.unicodeCollation: 'unicode'`。覆寫用於定序的地區設定。指定 `auto` 以使用 UI 地區設定。", "typescript.preferences.organizeImports.numericCollation": "需要 `organizeImports.unicodeCollation: 'unicode'`。依整數值排序數值字串。", "typescript.preferences.organizeImports.typeOrder": "指定應如何排序僅限類型的具名匯入。", "typescript.preferences.organizeImports.typeOrder.auto": "偵測應在何處排序僅限類型的具名匯入。", "typescript.preferences.organizeImports.typeOrder.first": "僅限類型的具名匯入會排序至匯入清單的開頭。例如，`import { type A, type Y, B, Z } from 'module';`", "typescript.preferences.organizeImports.typeOrder.inline": "具名匯入僅會依名稱排序。例如，`import { type A, B, type Y, Z } from 'module';`", "typescript.preferences.organizeImports.typeOrder.last": "僅限類型的具名匯入會排序至匯入清單的結尾。例如，`import { B, Z, type A, type Y } from 'module';`", "typescript.preferences.organizeImports.unicodeCollation": "指定是否要使用 Unicode 或序數定序排序匯入。", "typescript.preferences.organizeImports.unicodeCollation.ordinal": "使用每個程式碼項目的數值排序匯入。", "typescript.preferences.organizeImports.unicodeCollation.unicode": "使用 Unicode 程式碼定序排序匯入。", "typescript.preferences.preferTypeOnlyAutoImports": "盡可能在自動匯入中包括 `type` 關鍵字。需要在工作區中使用 TypeScript 5.3+。", "typescript.preferences.quoteStyle": "用於快速修正的慣用引號樣式。", "typescript.preferences.quoteStyle.auto": "從現有代碼推斷引號類型", "typescript.preferences.quoteStyle.double": "一律使用雙引號: `\"`", "typescript.preferences.quoteStyle.single": "一律使用單引號: `'`", "typescript.preferences.renameMatchingJsxTags": "在 JSX 標籤上時，請嘗試重命命名相符的標籤，而不是重新命名符號。需要在工作區中使用 TypeScript 5.1+。", "typescript.preferences.useAliasesForRenames": "啟用/停用在重新命名期間為物件速記屬性引入別名的功能。", "typescript.problemMatchers.tsc.label": "TypeScript 問題", "typescript.problemMatchers.tscWatch.label": " TypeScript 問題 (監看模式)", "typescript.referencesCodeLens.enabled": "在 TypeScript 檔案中啟用/停用參考 CodeLens。", "typescript.referencesCodeLens.showOnAllFunctions": "啟用/停用 TypeScript 檔案中所有函式的參考 CodeLens。", "typescript.removeUnusedImports": "移除未使用的匯入", "typescript.reportStyleChecksAsWarnings": "將樣式檢查回報為警告。", "typescript.restartTsServer": "重新啟動 TS 伺服器", "typescript.selectTypeScriptVersion.title": "選取 TypeScript 版本…", "typescript.sortImports": "排序匯入", "typescript.suggest.enabled": "啟用/停用自動完成建議。", "typescript.suggestionActions.enabled": "在編輯器中為 TypeScript 檔案啟用/停用建議診斷。", "typescript.tsc.autoDetect": "控制 tsc 工作的自動偵測。", "typescript.tsc.autoDetect.build": "僅建立單一執行編譯工作。", "typescript.tsc.autoDetect.off": "停用此功能。", "typescript.tsc.autoDetect.on": "同時建立建置及監看工作。", "typescript.tsc.autoDetect.watch": "僅建立編譯及監看工作。", "typescript.tsdk.desc": "指定 TypeScript 安裝下要用於 IntelliSense 的 tsserver 與 `lib*.d.ts` 檔案資料夾路徑，例如: `./node_modules/typescript/lib`。\r\n\r\n- 若指定為使用者設定，來自 `typescript.tsdk` 的 TypeScript 版本會自動取代內建的 TypeScript 版本。\r\n- 若指定為工作區設定，則 `typescript.tsdk` 可讓您以 `TypeScript: Select TypeScript version` 命令，改為對 IntelliSense 使用該工作區版本的 TypeScript。\r\n\r\n如需如何管理 TypeScript 版本的詳細資料，請參閱 [TypeScript 文件](https://code.visualstudio.com/docs/typescript/typescript-compiling#_using-newer-typescript-versions)。", "typescript.tsserver.enableRegionDiagnostics": "在 TypeScript 中啟用以區域為基礎的診斷。需要在工作區中使用 TypeScript 5.6+。", "typescript.tsserver.enableTracing": "允許追蹤 TS 伺服器效能到目錄。這些追蹤檔案可用於診斷 TS 伺服器效能問題。記錄可能包含檔案路徑、原始程式碼及您專案中可能具有敏感性的其他資訊。", "typescript.tsserver.log": "允許 TS 伺服器記錄到檔案。此記錄可用來診斷 TS 伺服器問題。記錄可能包含檔案路徑、原始程式碼及您專案中可能具有敏感性的其他資訊。", "typescript.tsserver.pluginPaths": "探索 TypeScript 語言服務外掛程式的其他路徑。", "typescript.tsserver.pluginPaths.item": "無論是絕對路徑或是相對路徑。相對路徑將會根據工作區資料夾進行解析。", "typescript.tsserver.trace": "允許將訊息追蹤傳送到 TS 伺服器。此追蹤可用來診斷 TS 伺服器問題。追蹤可能包含檔案路徑、原始程式碼及您專案中可能具有敏感性的其他資訊。", "typescript.updateImportsOnFileMove.enabled": "當您在 VS Code 中重新命名檔案或移動檔案時，啟用/停用匯入路徑的自動更新。", "typescript.updateImportsOnFileMove.enabled.always": "一律自動更新路徑。", "typescript.updateImportsOnFileMove.enabled.never": "一律不為路徑重新命名，也不提示。", "typescript.updateImportsOnFileMove.enabled.prompt": "在每次重新命名時提示。", "typescript.useTsgo": "停用 TypeScript 和 JavaScript 語言功能，允許使用 TypeScript Go 實驗性延伸模組。需要安裝及設定 TypeScript Go。變更此設定之後，需要重新載入延伸模組。", "typescript.validate.enable": "啟用/停用 TypeScript 驗證。", "typescript.workspaceSymbols.excludeLibrarySymbols": "排除來自 [前往工作區中的符號] 結果中程式庫檔案的符號。需要在工作區中使用 TypeScript 5.3+。", "typescript.workspaceSymbols.scope": "控制 [前往工作區中的符號](https://code.visualstudio.com/docs/editor/editingevolved#_open-symbol-by-name) 會搜尋到哪些檔案。", "typescript.workspaceSymbols.scope.allOpenProjects": "搜尋所有開啟的 JavaScript 或 TypeScript 專案的符號。", "typescript.workspaceSymbols.scope.currentProject": "只在目前的 JavaScript 或 TypeScript 專案中搜尋符號。", "virtualWorkspaces": "在虛擬工作區中，不支援解析和尋找跨檔案的參照。", "walkthroughs.nodejsWelcome.debugJsFile.altText": "使用 Visual Studio Code 在 Node.js 中偵錯並執行您的 JavaScript 程式碼。", "walkthroughs.nodejsWelcome.debugJsFile.description": "安裝 Node.js後，您可以在終端機執行 JavaScript 程式，方式是輸入 ``node your-file-name.js``\r\nNode.js 程式執行的另一個簡單方法是使用 VS Code 偵錯工具，讓您執行程式碼、在不同點暫停，並協助您了解逐步的內容。\r\n[開始偵錯](command:javascript-walkthrough.commands.debugJsFile)", "walkthroughs.nodejsWelcome.debugJsFile.title": "執行並偵錯 JavaScript", "walkthroughs.nodejsWelcome.description": "充分利用 Visual Studio Code 的一流 JavaScript 體驗。", "walkthroughs.nodejsWelcome.downloadNode.forLinux.description": "Node.js 是執行 JavaScript 程式碼的簡單方法。您可以使用它來快速建置命令列應用程式和伺服器。Node.js 也隨附 npm，這是一款套件管理員，可讓您輕鬆重複使用及共用 JavaScript 程式碼。\r\n[安裝 Node.js](https://nodejs.org/en/download/package-manager/)", "walkthroughs.nodejsWelcome.downloadNode.forLinux.title": "安裝 Node.js", "walkthroughs.nodejsWelcome.downloadNode.forMacOrWindows.description": "Node.js 是執行 JavaScript 程式碼的簡單方法。您可以使用它來快速建置命令列應用程式和伺服器。Node.js 也隨附 npm，這是一款套件管理員，可讓您輕鬆重複使用及共用 JavaScript 程式碼。\r\n[安裝 Node.js](https://nodejs.org/en/download/)", "walkthroughs.nodejsWelcome.downloadNode.forMacOrWindows.title": "安裝 Node.js", "walkthroughs.nodejsWelcome.learnMoreAboutJs.altText": "深入了解 Visual Studio Code 中的 JavaScript 及 Node.js。", "walkthroughs.nodejsWelcome.learnMoreAboutJs.description": "想要更熟悉 JavaScript、Node.js 和 VS Code 嗎? 請務必查看我們的文件!\r\n我們有許多資源可用於學習[JavaScript](https://code.visualstudio.com/docs/nodejs/working-with-javascript) 和 [Node.js](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)。\r\n\r\n[深入了解](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)", "walkthroughs.nodejsWelcome.learnMoreAboutJs.title": "探索更多", "walkthroughs.nodejsWelcome.makeJsFile.description": "讓我們寫下第一個 JavaScript 檔案。我們必須建立新檔案，並在檔案名稱結尾以 ``.js`` 副檔名儲存檔案。\r\n[建立 JavaScript 檔案](command:javascript-walkthrough.commands.createJsFile)", "walkthroughs.nodejsWelcome.makeJsFile.title": "建立 JavaScript 檔案", "walkthroughs.nodejsWelcome.title": "開始使用 JavaScript 與 Node.js", "workspaceTrust": "使用工作區版本時，延伸模組需要工作區信任，因為它會執行工作區指定的程式碼。"}}}