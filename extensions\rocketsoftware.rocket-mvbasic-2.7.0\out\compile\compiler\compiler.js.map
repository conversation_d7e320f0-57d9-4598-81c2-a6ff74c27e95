{"version": 3, "file": "compiler.js", "sourceRoot": "", "sources": ["../../../src/compile/compiler/compiler.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,0CAA2C;AAC3C,kDAAkD;AAalD,IAAY,MAKX;AALD,WAAY,MAAM;IACjB,oDAAiB,CAAA;IACjB,oEAAyB,CAAA;IACzB,qDAAiB,CAAA;IACjB,mDAAgB,CAAA;AACjB,CAAC,EALW,MAAM,sBAAN,MAAM,QAKjB;AAED,MAAsB,QAAQ;IAM7B,8BAA8B;IACjB,GAAG,CAAC,IAAY,EAAE,OAAgB,EAAE,OAAgB;;YAChE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,OAAO;oBACN,MAAM,EAAE,MAAM,CAAC,YAAY;oBAC3B,OAAO,EAAE,YAAY,GAAG,IAAI,GAAG,+CAA+C,GAAG,aAAO;iBACxF,CAAC;YACH,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,EAAE,CAAC;YACb,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,GAAG,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBACP,GAAG,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1F,CAAC;YAED,OAAO;gBACN,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;gBACxB,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;aAC3B,CAAC;QACH,CAAC;KAAA;IAED,6DAA6D;IACnD,OAAO,CAAC,GAAW;QAC5B,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,mCAAmC,CAAC;eACpE,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IAEY,SAAS,CAAC,GAAW;QAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC;eACvC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IACpD,CAAC;IAEY,QAAQ,CAAC,GAAW;QAC1B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC;eACtC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAChD,CAAC;IAGD,+CAA+C;IACxC,QAAQ,CAAC,QAAgB;QAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACrC,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;eAC5B,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;eACpB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;eACtB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;eACtB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,QAAQ,CAAC,GAAW;QAC3B,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9C,OAAO,2CAA2C,CAAC;QACpD,CAAC;QAED,OAAO,GAAG,CAAC;IACT,CAAC;CAEJ;AAlED,4BAkEC"}