"use strict";
/**
 *  basicDebugProvider - debug related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicInlineValuesProvider = exports.BasicEvaluatableExpressionProvider = exports.BasicDynamicConfigurationProvider = exports.BasicConfigurationProvider = void 0;
const vscode = require("vscode");
const basicDebug_1 = require("./basicDebug");
const basicDebugConfig_1 = require("./basicDebugConfig");
const provider_1 = require("../compile/provider");
const editor_1 = require("../common/editor");
const restart = require("./restart");
class BasicConfigurationProvider {
    /**
     * Massage a debug configuration just before a debug session is being launched,
     * e.g. add all missing attributes to the debug configuration.
     */
    resolveDebugConfiguration(folder, config, token) {
        return __awaiter(this, void 0, void 0, function* () {
            let newConfig = config;
            //folder is not correct in params when run "Run and debug"
            if (config.name == undefined) {
                folder = convertWorkspaceFolder(folder);
            }
            newConfig = yield new basicDebugConfig_1.BasicDebugConfig(config, folder).checkAndReset();
            if (newConfig !== undefined && newConfig.dependencies.length > 0) {
                provider_1.BasicBuildTaskProvider.getInstance().clearFilesList();
                provider_1.BasicBuildTaskProvider.getInstance().addQuickBuildFiles(newConfig.dependencies, newConfig.isSameAccount);
                // Save debug files for further using
                restart.saveDebugFiles(newConfig.dependencies);
            }
            return newConfig;
        });
    }
}
exports.BasicConfigurationProvider = BasicConfigurationProvider;
class BasicDynamicConfigurationProvider {
    provideDebugConfigurations(folder, token) {
        const config = {
            type: basicDebug_1.DEBUG_TYPE,
            name: "Dynamic Launch",
            request: "launch"
        };
        return [config];
    }
}
exports.BasicDynamicConfigurationProvider = BasicDynamicConfigurationProvider;
class BasicEvaluatableExpressionProvider {
    provideEvaluatableExpression(document, position) {
        const wordRange = document.getWordRangeAtPosition(position);
        return wordRange ? new vscode.EvaluatableExpression(wordRange) : undefined;
    }
}
exports.BasicEvaluatableExpressionProvider = BasicEvaluatableExpressionProvider;
class BasicInlineValuesProvider {
    provideInlineValues(doc, view_port, context) {
        const allValues = [];
        for (let l = view_port.start.line; l <= context.stoppedLocation.end.line; l++) {
            const line = doc.lineAt(l);
            var regExp = /local_[ifso]/ig;
            do {
                var m = regExp.exec(line.text);
                if (m) {
                    const variableName = m[0];
                    const variableRange = new vscode.Range(l, m.index, l, m.index + variableName.length);
                    allValues.push(new vscode.InlineValueVariableLookup(variableRange, variableName, false));
                }
            } while (m);
        }
        return allValues;
    }
}
exports.BasicInlineValuesProvider = BasicInlineValuesProvider;
function convertWorkspaceFolder(folder) {
    var _a;
    const editor = vscode.window.activeTextEditor;
    if (editor !== undefined)
        folder = vscode.workspace.getWorkspaceFolder(editor.document.uri);
    if (folder === undefined) {
        var textUri = (_a = (0, editor_1.getBasicEditor)()) === null || _a === void 0 ? void 0 : _a.document.uri;
        if (textUri !== undefined) {
            folder = vscode.workspace.getWorkspaceFolder(textUri);
        }
    }
    return folder;
}
//# sourceMappingURL=basicDebugProvider.js.map