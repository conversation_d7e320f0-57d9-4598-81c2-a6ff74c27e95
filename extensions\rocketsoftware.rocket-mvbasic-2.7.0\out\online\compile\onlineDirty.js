"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineDirtyFileBox = void 0;
const vscode = require("vscode");
const extension_1 = require("../../extension");
const dirty_1 = require("../../compile/dirty");
class OnlineDirtyFileBox extends dirty_1.DirtyFileBox {
    pick(uri) {
        // Add current opened file to dirty file list.
        // Because user click compile on current opened file.
        const currentFile = uri.fsPath;
        this.add(currentFile);
        // If only one file and its current open document,
        // then no need show the list.
        if (this.dirtyFiles.length <= 1) {
            const fileName = this.getFileName(currentFile);
            if (fileName === undefined) {
                return Promise.resolve(undefined);
            }
            const result = [];
            result.push({ label: fileName, detail: currentFile });
            return Promise.resolve(result);
        }
        const items = [];
        for (const dirtyItem of this.dirtyFiles) {
            const filePath = dirtyItem[0];
            const fileName = dirtyItem[1];
            items.push({ label: fileName, detail: filePath });
        }
        return vscode.window.showQuickPick(items, { canPickMany: true });
    }
    add(file) {
        // Check whether the file existing in the dirty box
        const lowerFilePath = file.toLowerCase().replace("\\", "/");
        for (const item of this.dirtyFiles) {
            const dirtyFilePath = item[0].toLowerCase().replace("\\", "/");
            if (dirtyFilePath === lowerFilePath) {
                return;
            }
        }
        const fileName = this.getFileName(file);
        if (fileName === undefined) {
            return;
        }
        this.dirtyFiles.push([file, fileName]);
    }
    getFiles(pickItems) {
        const result = [];
        pickItems.forEach(item => {
            if (item.detail) {
                result.push(item.detail);
            }
        });
        return result;
    }
    getFileName(filePath) {
        const accountUri = extension_1.online.getAccountUri();
        const pathToReplace = accountUri ? accountUri.fsPath : undefined;
        return filePath.replace(pathToReplace + "\\", "").replace(pathToReplace + "/", "");
    }
}
exports.OnlineDirtyFileBox = OnlineDirtyFileBox;
//# sourceMappingURL=onlineDirty.js.map