{"version": 3, "file": "buildTerminal.js", "sourceRoot": "", "sources": ["../../src/compile/buildTerminal.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AAEH,6BAA8B;AAC9B,iCAAgC;AAChC,uCAAwC;AACxC,kDAAiF;AACjF,sDAAmD;AACnD,sDAAmD;AACnD,yCAAoD;AACpD,+CAA+C;AAE/C,MAAa,sBAAsB;IAgBlC,YAAsB,KAAe,EAAU,UAAoC,EAAE,QAAiB,EAAE,aAAwC;QAA1H,UAAK,GAAL,KAAK,CAAU;QAAU,eAAU,GAAV,UAAU,CAA0B;QAf3E,iBAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;QACzD,eAAU,GAAyB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QACnD,iBAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;QACzD,eAAU,GAA0B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QAMpD,YAAO,GAAG,CAAC,CAAC;QACZ,WAAM,GAAG,CAAC,CAAC;QACX,UAAK,GAAG,CAAC,CAAC;QACV,YAAO,GAAG,CAAC,CAAC;QAInB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,QAAQ,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACvC,KAAK,UAAU;gBACd,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAU,CAAC,UAAU,CAAC,CAAC;gBAC3C,MAAM;YAEP;gBACC,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAU,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC;IACF,CAAC;IAED,IAAI,CAAC,iBAAwD;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,yBAAyB;QACzB,iCAAsB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;IACvD,CAAC;IAED,KAAK;QACJ,UAAU;IACX,CAAC;IAEe,OAAO;;YACtB,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,YAAY,CAAC,4BAA4B,CAAC,CAAC;gBAChD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACJ,OAAO;YACjB,CAAC;YACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBACtD,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;oBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;oBACnC,SAAS;gBACV,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE,CAAC;oBACrC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACnD,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;oBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;gBACD,IAAI,OAAO,IAAI,CAAC,aAAa,IAAI,QAAQ,EAAE,CAAC;oBAC3C,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;oBAChC,MAAM,UAAU,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC;oBAChE,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC;yBAC7E,IAAI,CAAC,CAAM,SAAS,EAAC,EAAE;wBACvB,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;4BAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;4BAC1B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACxB,CAAC;6BAAM,CAAC;4BACP,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;4BAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACnB,CAAC;oBACF,CAAC,CAAA,CAAC,CAAC;gBACL,CAAC;YACF,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QACnB,CAAC;KAAA;IAEe,KAAK,CAAC,IAAY;;YACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,iBAAM,CAAC,YAAY;oBAAE,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;oBAAC,MAAM;gBACnD,KAAK,iBAAM,CAAC,aAAa;oBAAE,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;oBAAC,MAAM;gBACpD,KAAK,iBAAM,CAAC,YAAY;oBAAE,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;oBAAC,MAAM;gBAClD,KAAK,iBAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBAClC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;oBAC5B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACb,SAAS;gBACV,CAAC;gBACD,OAAO,CAAC,CAAC,MAAM;YAChB,CAAC;YACD,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QACjB,CAAC;KAAA;IAES,MAAM,CAAC,IAAY,EAAE,MAAe;QAC7C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,mBAAmB,GAAG,aAAO,CAAC,CAAC;QACpG,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,mBAAmB,CAAC,CAAC;YACzF,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,aAAO,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;IACnB,CAAC;IAES,UAAU;QACnB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAEhC,MAAM,GAAG,GAAG,+BAA+B,GAAG,IAAI,CAAC,KAAK,GAAG,WAAW,GAAE,aAAO;YAC9E,kBAAkB,GAAG,IAAI,CAAC,OAAO,GAAG,aAAO;YAC3C,eAAe,GAAG,IAAI,CAAC,MAAM,GAAG,aAAO;YACvC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;QAEhC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjB,IAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACX,CAAC;IACR,CAAC;IAES,YAAY,CAAC,GAAW;QACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,aAAO,CAAC,CAAC;IACvC,CAAC;IAES,IAAI,CAAC,MAAc;QAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CACD;AAlID,wDAkIC"}