"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Compiler = exports.Status = void 0;
const env_1 = require("../../common/env");
const mvClient = require("../../mvClient/client");
var Status;
(function (Status) {
    Status[Status["INVALID_FILE"] = -2] = "INVALID_FILE";
    Status[Status["SERVER_NOT_CONNECTED"] = -1] = "SERVER_NOT_CONNECTED";
    Status[Status["BUILD_SUCCEED"] = 0] = "BUILD_SUCCEED";
    Status[Status["BUILD_FAILED"] = 1] = "BUILD_FAILED";
})(Status || (exports.Status = Status = {}));
class Compiler {
    // Build a BASIC program file.
    run(file, bIgnore, content) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.validate(file)) {
                return {
                    status: Status.INVALID_FILE,
                    message: "Warning: <" + file + "> is not a valid BASIC program file. Skip it." + env_1.NEWLINE
                };
            }
            const config = this.getConfig();
            let msg = "";
            if (config.doCatalog && !bIgnore) {
                msg = yield mvClient.catalog(file, config.option1, config.option2, config.option3);
            }
            else {
                msg = yield mvClient.compile("do", file, config.option1, config.option2, config.option3);
            }
            return {
                status: this.status(msg),
                message: this.readable(msg)
            };
        });
    }
    // Check if the error message indicates server not connected.
    offline(msg) {
        return msg.toLowerCase().startsWith("error: u2 server is not connected")
            || msg.toLowerCase().includes("rpc failed");
    }
    isIgnored(msg) {
        const trimMsg = msg.toLowerCase().trimStart();
        return trimMsg.includes("compilation ignored")
            || trimMsg.includes("cataloged ignored");
    }
    isFailed(msg) {
        const trimMsg = msg.toLowerCase().trimStart();
        return trimMsg.includes("compilation failed")
            || trimMsg.includes("cataloged failed");
    }
    // Validate whether the files is a BASIC or not
    validate(fileName) {
        const lower = fileName.toLowerCase();
        return !(lower.endsWith(".json")
            || lower.endsWith(".c")
            || lower.endsWith(".txt")
            || lower.endsWith(".cpp")
            || lower.endsWith(".py"));
    }
    readable(msg) {
        if (msg.toLowerCase().includes("rpc failed")) {
            return "U2 server unavailable. Skip compilation. ";
        }
        return msg;
    }
}
exports.Compiler = Compiler;
//# sourceMappingURL=compiler.js.map