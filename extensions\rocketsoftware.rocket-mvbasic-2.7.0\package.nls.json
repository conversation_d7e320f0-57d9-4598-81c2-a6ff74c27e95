{"vscode-rocket.mv.basic.command.activate": "Activate Rocket MV BASIC", "vscode-rocket.mv.basic.command.editconfig": "Edit Configuration of Rocket MV BASIC", "vscode-rocket.mv.basic.command.group.show": "Show group view", "vscode-rocket.mv.basic.command.group.hide": "Hide group view", "vscode-rocket.mv.basic.command.group.refresh": "Refresh", "vscode-rocket.mv.basic.command.u2connect": "Connect/Disconnect U2 server", "vscode-rocket.mv.basic.command.compile.test": "Test compile BASIC programs", "vscode-rocket.mv.basic.command.compile.do": "Compile BASIC programs", "vscode-rocket.mv.basic.command.catalog.do": "Catalog BASIC programs", "vscode-rocket.mv.basic.command.compile.select": "Compile selected BASIC programs", "vscode-rocket.mv.basic.command.codelens.show": "Enable code lens", "vscode-rocket.mv.basic.command.codelens.hide": "Disable code lens", "vscode-rocket.mv.basic.command.compile.do.ud.pick": "Compile with 'Pick' flavor", "vscode-rocket.mv.basic.command.compile.do.ud.unibasic": "Compile with 'UniBasic' flavor", "vscode-rocket.mv.basic.command.compile.do.ud.revelation": "Compile with 'Advanced Revelation' flavor", "vscode-rocket.mv.basic.command.compile.do.ud.douglas": "Compile with 'McDonnell <PERSON>' flavor", "vscode-rocket.mv.basic.command.online.server.refresh": "Refresh", "vscode-rocket.mv.basic.command.online.server.add": "Add New Server", "vscode-rocket.mv.basic.command.online.server.connect": "Connect", "vscode-rocket.mv.basic.command.online.server.edit": "Edit Server Config", "vscode-rocket.mv.basic.command.online.server.delete": "Delete Server", "vscode-rocket.mv.basic.command.online.server.disconnect": "Disconnect", "vscode-rocket.mv.basic.command.online.server.add.file": "Add New File", "vscode-rocket.mv.basic.command.online.server.configure.pattern": "Configure <PERSON><PERSON>", "vscode-rocket.mv.basic.command.online.server.rename.file": "<PERSON><PERSON>", "vscode-rocket.mv.basic.command.online.server.delete.file": "Delete File", "vscode-rocket.mv.basic.command.online.config.open": "Open Config File", "vscode-rocket.mv.basic.command.online.basic.config.open": "Open BASIC Config File", "vscode-rocket.mv.basic.command.online.search": "Search", "vscode-rocket.mv.basic.command.online.editing.sort.by.name.ascending": "Ascending", "vscode-rocket.mv.basic.command.online.editing.sort.by.name.descending": "Descending", "vscode-rocket.mv.basic.config.mode": "Please keep this option disable. This is **only** for extension developers.", "vscode-rocket.mv.basic.config.jdk11": "If you have setup Java 11 or Open JDK 11 environment already, please leave this empty. If not, you could specify JDK 11 directory here.\n\nFor example: *C:\\ThirdParty\\open-jdk-11\\bin*"}