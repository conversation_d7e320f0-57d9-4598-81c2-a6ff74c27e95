"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicGroup = exports.BasicDirectory = exports.BasicFile = exports.BasicItem = void 0;
const vscode = require("vscode");
class BasicItem extends vscode.TreeItem {
    constructor(label, filePath, state, command) {
        super(label, state);
        this.label = label;
        this.filePath = filePath;
        this.state = state;
        this.command = command;
        this.children = [];
        this.tooltip = this.filePath;
    }
    equals(o) {
        if (this.label === o.label
            && this.filePath === o.filePath) {
            return true;
        }
        return false;
    }
    add(item) {
        item.parent = this;
        this.children.push(item);
    }
    getChildren() {
        return this.children;
    }
}
exports.BasicItem = BasicItem;
class BasicFile extends BasicItem {
    constructor(label, filePath) {
        super(label, filePath, vscode.TreeItemCollapsibleState.None, {
            command: 'vscode-rocket.mv.basic.group.openFile',
            title: '',
            arguments: [filePath]
        });
        this.label = label;
        this.filePath = filePath;
    }
}
exports.BasicFile = BasicFile;
class BasicDirectory extends BasicItem {
    constructor(label, filePath) {
        super(label, filePath, vscode.TreeItemCollapsibleState.Collapsed);
        this.label = label;
        this.filePath = filePath;
        this.pendings = new Map;
    }
    addPendings(files) {
        files.forEach(file => {
            this.pendings.set(file, file);
        });
    }
    removePendings(files) {
        files.forEach(file => {
            this.pendings.delete(file);
        });
    }
    getPendings() {
        const result = [];
        this.pendings.forEach(item => {
            result.push(item);
        });
        return result;
    }
    addAll(items) {
        for (const item of items) {
            item.parent = this;
            this.add(item);
        }
    }
}
exports.BasicDirectory = BasicDirectory;
class BasicGroup extends BasicItem {
    constructor(label, filePath) {
        super(label, filePath, vscode.TreeItemCollapsibleState.Collapsed);
        this.label = label;
        this.filePath = filePath;
    }
    get(index) {
        if (index < this.getChildren().length) {
            return this.getChildren()[index];
        }
        return undefined;
    }
}
exports.BasicGroup = BasicGroup;
//# sourceMappingURL=basicItems.js.map