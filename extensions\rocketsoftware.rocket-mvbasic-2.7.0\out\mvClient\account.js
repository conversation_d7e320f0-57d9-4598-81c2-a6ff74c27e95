"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Account = void 0;
class Account {
    constructor(address, userName, password, accountName, port, dataSource, folderPath, rpcName) {
        this.address = address;
        this.userName = userName;
        this.password = password;
        this.accountName = accountName;
        this.port = Number(port);
        this.dataSource = dataSource;
        this.folderPath = folderPath;
        this.isConnect = false;
        this.rpcName = rpcName;
    }
    getName() {
        return this.accountName;
    }
    setName(name) {
        this.accountName = name;
    }
    setIsConnect(isConnect) {
        this.isConnect = isConnect;
    }
    getIsConnect() {
        return this.isConnect;
    }
    getFolderPath() {
        return this.folderPath;
    }
    getDataSource() {
        return this.dataSource;
    }
    getRpcName() {
        return this.rpcName;
    }
}
exports.Account = Account;
//# sourceMappingURL=account.js.map