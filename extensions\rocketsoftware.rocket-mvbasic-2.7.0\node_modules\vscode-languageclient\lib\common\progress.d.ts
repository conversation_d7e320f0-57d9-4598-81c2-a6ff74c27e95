import { ClientCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, FeatureState, StaticFeature } from './features';
export declare class ProgressFeature implements StaticFeature {
    private _client;
    private readonly activeParts;
    constructor(_client: FeatureClient<object>);
    getState(): FeatureState;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(): void;
    dispose(): void;
}
