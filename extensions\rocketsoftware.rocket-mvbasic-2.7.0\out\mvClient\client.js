"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setAccount = setAccount;
exports.setAccountName = setAccountName;
exports.connect = connect;
exports.disconnect = disconnect;
exports.setStatusTooltip = setStatusTooltip;
exports.showConnectIcon = showConnectIcon;
exports.silentDisconnect = silentDisconnect;
exports.isConnected = isConnected;
exports.getAccount = getAccount;
exports.isSameWorkspace = isSameWorkspace;
exports.isSameAccount = isSameAccount;
exports.compile = compile;
exports.writeDirRecord = writeDirRecord;
exports.fileLock = fileLock;
exports.releaseFileLock = releaseFileLock;
exports.catalog = catalog;
exports.startDebugger = startDebugger;
exports.logon = logon;
exports.getAllAccounts = getAllAccounts;
exports.getAccounts = getAccounts;
const vscode_languageclient_1 = require("vscode-languageclient");
const vscode = require("vscode");
const extConfig = require("../config/extConfig");
const msg_1 = require("../msg");
const status = require("./status");
const account_1 = require("./account");
const driver = require("../driver");
const path = require("path");
const ds = require("../mvClient/dataSource");
const extension_1 = require("../extension");
var ConnStatus;
(function (ConnStatus) {
    ConnStatus[ConnStatus["UNCONNECT"] = 0] = "UNCONNECT";
    ConnStatus[ConnStatus["CONNECTING"] = 1] = "CONNECTING";
    ConnStatus[ConnStatus["CONNECTED"] = 2] = "CONNECTED";
})(ConnStatus || (ConnStatus = {}));
let connStatus = ConnStatus.UNCONNECT;
let account = undefined;
// TODO: This is a temp function, need refactor
// Only for online editing 
function setAccount(address, userName, password, accountName, port, dataSource) {
    account = new account_1.Account(address, userName, password, accountName, port, dataSource, "");
}
function setAccountName(name) {
    if (account) {
        account.setName(name);
    }
}
function connect(address, username, password, accountname, port, folderPath) {
    return __awaiter(this, void 0, void 0, function* () {
        const wsfs = vscode.workspace.workspaceFolders;
        if (wsfs === undefined) {
            return;
        }
        const workspace = wsfs[0];
        if (extConfig.rmvExists(workspace)) {
            connect_offline();
        }
        else {
            connect_online(address, username, password, accountname, port, folderPath);
        }
    });
}
function connect_online(address, username, password, accountname, port, folderPath) {
    if (connStatus != ConnStatus.UNCONNECT) {
        return;
    }
    if (address === undefined || address.trim().length == 0) {
        return;
    }
    if (accountname === undefined || accountname.trim().length == 0) {
        return;
    }
    if (port === undefined) {
        // input manually
    }
    if (username === undefined) {
        return;
    }
    if (password === undefined) {
        return;
    }
    if (port === undefined) {
        return;
    }
    const statusMsg = "Connecting to address '" + address + "' ...";
    connecting_online(statusMsg, address, username, password, accountname, port, folderPath);
}
function connect_offline() {
    return __awaiter(this, void 0, void 0, function* () {
        if (connStatus != ConnStatus.UNCONNECT) {
            return;
        }
        // If there are multiple workspaces, let user select an account to connect
        // Consider about when account information is empty
        const folders = extConfig.listFolders();
        if (folders.length > 1) {
            const accounts = yield getAccounts();
            if (accounts.length === 0) {
                const selected = yield selectFolder();
                if (selected) {
                    connectManually(selected);
                }
                return;
            }
            const options = [];
            for (const account of accounts) {
                const accountName = yield account.getName();
                const folderPath = account.getPath();
                if (!accountName || !folderPath) {
                    continue;
                }
                options.push({ label: accountName, description: folderPath });
            }
            const manually = "Input manually ... ";
            options.push({ label: manually });
            // Let user select account folder or input account information manually.
            vscode.window.showQuickPick(options).then((s) => __awaiter(this, void 0, void 0, function* () {
                if (!s || !s.label) {
                    return;
                }
                // Try to get folder path
                if (s.label === manually) {
                    const selected = yield selectFolder();
                    if (selected) {
                        connectManually(selected);
                    }
                    return;
                }
                const folderPath = s.description;
                if (folderPath) {
                    connectAuto(folderPath);
                }
            }));
        }
        else if (folders.length == 1) {
            // If only one workspace folder, connect to that account directly.
            const folder = folders[0];
            connectAuto(folder[1]);
        }
        else {
            vscode.window.showErrorMessage(msg_1.Msg.MV_NO_ACCOUNT_FOLDER);
        }
    });
}
function disconnectDap() {
    var dapSession = vscode.debug.activeDebugSession;
    if (dapSession) {
        vscode.debug.stopDebugging(dapSession);
    }
}
function disconnectFromU2() {
    return __awaiter(this, void 0, void 0, function* () {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length == 0) {
            vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
            return;
        }
        const command = vscode_languageclient_1.Command.create("disconnect", "disconnect");
        const disconnFail = !(yield driver.send(command));
        let message;
        if (disconnFail) {
            message = msg_1.Msg.MV_DISCONNECT_FAIL;
        }
        else {
            nextState();
            message = msg_1.Msg.MV_DISCONNECT_SUCCESS;
        }
        vscode.window.showInformationMessage(message);
        if (!disconnFail) {
            status.disconnect();
        }
        // if online mode, clear content of the tree
        if (!extConfig.rmvExists(workspaceFolders[0])) {
            extension_1.online.clear();
        }
    });
}
function disconnect() {
    return __awaiter(this, void 0, void 0, function* () {
        if (connStatus != ConnStatus.CONNECTED) {
            return;
        }
        if (extension_1.online) {
            extension_1.online.setOnline(false);
        }
        if (account !== undefined) {
            account.setIsConnect(false);
        }
        // If DAP session exists
        if (vscode.debug.activeDebugSession) {
            // Show message box to select close the session or not
            let options = { modal: true };
            let itemYes = "Yes";
            var ret = yield vscode.window.showWarningMessage(msg_1.Msg.DEBUG_DISCONNECT, options, itemYes);
            if (ret !== itemYes) {
                return;
            }
            disconnectDap();
        }
        disconnectFromU2();
    });
}
function setStatusTooltip(cacheType) {
    status.setTooltip(cacheType);
}
function showConnectIcon(show) {
    status.show(show);
}
function silentDisconnect() {
    if (connStatus == ConnStatus.CONNECTED) {
        vscode.window.showInformationMessage(msg_1.Msg.MV_DISCONNECT_SUCCESS);
    }
    connStatus = ConnStatus.UNCONNECT;
    status.disconnect();
    if (extension_1.online) {
        vscode.commands.executeCommand('vscode-rocket.mv.basic.command.online.server.disconnect');
        extension_1.online.setOnline(false);
    }
}
function isConnected() {
    return connStatus == ConnStatus.CONNECTED;
}
function getAccount() {
    return account;
}
function isSameWorkspace(filePath) {
    var _a, _b, _c;
    var currentWorkspaceFolderPath = "";
    if (extension_1.online === undefined) {
        const currentWorkspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(filePath));
        currentWorkspaceFolderPath = (_a = currentWorkspaceFolder === null || currentWorkspaceFolder === void 0 ? void 0 : currentWorkspaceFolder.uri.fsPath) !== null && _a !== void 0 ? _a : "";
    }
    else {
        currentWorkspaceFolderPath = (_c = (_b = extension_1.online.getAccountUri()) === null || _b === void 0 ? void 0 : _b.fsPath) !== null && _c !== void 0 ? _c : "";
        if (currentWorkspaceFolderPath !== "")
            return true;
    }
    if (account !== undefined) {
        if (currentWorkspaceFolderPath !== (account === null || account === void 0 ? void 0 : account.getFolderPath())) {
            return msg_1.Msg.WRONG_WORKSPACE.replace("${account}", account.getName());
        }
    }
    return true;
}
function isSameAccount(filePath) {
    const predictFileAccount = path.basename(path.dirname(path.dirname(filePath)));
    let accountName = account === null || account === void 0 ? void 0 : account.getName();
    if (accountName !== undefined && accountName !== predictFileAccount) {
        return msg_1.Msg.ACCOUNT_NOT_CONSISTENT.replace("${predict.account}", predictFileAccount).replace("${connect.account}", accountName);
    }
    return true;
}
function compile(cmd, file, opt1, opt2, opt3) {
    return __awaiter(this, void 0, void 0, function* () {
        let msg = "";
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length == 0) {
            vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
            return msg;
        }
        const command = vscode_languageclient_1.Command.create("compile", "compile", cmd, file, opt1, opt2, opt3);
        msg = yield driver.send(command);
        return msg;
    });
}
function writeDirRecord(accountName, dirName, fileName, content) {
    return __awaiter(this, void 0, void 0, function* () {
        if (account === undefined) {
            return "";
        }
        const command = vscode_languageclient_1.Command.create("writeDirRecord", "writeDirRecord", accountName, dirName, fileName, content);
        return driver.send(command);
    });
}
function fileLock(dirName, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        if (account === undefined) {
            return "";
        }
        const command = vscode_languageclient_1.Command.create("fileLock", "fileLock", account.getName(), dirName, fileName);
        return driver.send(command);
    });
}
function releaseFileLock(dirName, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        if (account === undefined) {
            return "";
        }
        const command = vscode_languageclient_1.Command.create("releaseFileLock", "releaseFileLock", account.getName(), dirName, fileName);
        return driver.send(command);
    });
}
function catalog(file, opt1, opt2, opt3) {
    return __awaiter(this, void 0, void 0, function* () {
        let msg = "";
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length == 0) {
            vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
            return msg;
        }
        const command = vscode_languageclient_1.Command.create("catalog", "catalog", file, opt1, opt2, opt3);
        msg = yield driver.send(command);
        return msg;
    });
}
function startDebugger(args) {
    return __awaiter(this, void 0, void 0, function* () {
        const command = vscode_languageclient_1.Command.create("startDebug", "startDebug", args);
        return yield driver.send(command);
    });
}
function logon(accountName) {
    return __awaiter(this, void 0, void 0, function* () {
        if (isConnected()) {
            const command = vscode_languageclient_1.Command.create('logon', 'logon', accountName);
            return yield driver.send(command);
        }
        return "Error";
    });
}
function getAllAccounts(accountFilterPattern) {
    return __awaiter(this, void 0, void 0, function* () {
        const command = vscode_languageclient_1.Command.create("getAccounts", "getAccounts", accountFilterPattern);
        return yield driver.send(command);
    });
}
function connectManually(path) {
    return __awaiter(this, void 0, void 0, function* () {
        const hostName = yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_ADDRESS });
        if (!hostName)
            return;
        const accountName = yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_ACCOUNT });
        if (!accountName)
            return;
        const userName = yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_USER_NAME });
        if (!userName)
            return;
        const password = yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_WORD, password: true });
        if (!password)
            return;
        const port = yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_PORT });
        if (!port)
            return;
        const statusMsg = connectingMsg(accountName);
        connecting_offline(statusMsg, hostName, userName, password, accountName, port, path);
    });
}
function connectingMsg(accountName) {
    return "Connecting to account '" + accountName + "'";
}
function connecting_online(msg, address, userName, password, accountName, port, folderPath) {
    return __awaiter(this, void 0, void 0, function* () {
        if (folderPath === undefined) {
            return;
        }
        const datasource = ds.get();
        if (datasource === undefined) {
            return;
        }
        nextState();
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            cancellable: false,
            title: msg
        }, (progress) => __awaiter(this, void 0, void 0, function* () {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length == 0) {
                vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
                return;
            }
            account = new account_1.Account(address, userName, password, accountName, port, datasource, folderPath);
            const command = vscode_languageclient_1.Command.create("connect", "connect", account);
            progress.report({ increment: 0 });
            const message = yield driver.send(command);
            yield Promise.resolve();
            progress.report({ increment: 100 });
            const connectSucceed = message.includes("establish");
            if (connectSucceed) {
                vscode.window.showInformationMessage(message);
                status.connect(accountName);
                nextState();
                account.setIsConnect(true);
                // if online mode, refresh the tree
                if (!extConfig.rmvExists(workspaceFolders[0])) {
                    extension_1.online.updateTree();
                    extension_1.online.setOnline(true);
                }
            }
            else {
                vscode.window.showErrorMessage(message);
                status.disconnect();
                unconnected();
                account.setIsConnect(false);
            }
        }));
    });
}
function connecting_offline(msg, address, userName, password, accountName, port, folderPath, rpcName) {
    return __awaiter(this, void 0, void 0, function* () {
        const dataSource = extConfig.getDb(folderPath).dataSource;
        nextState();
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            cancellable: false,
            title: msg
        }, (progress) => __awaiter(this, void 0, void 0, function* () {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length == 0) {
                vscode.window.showErrorMessage(msg_1.Msg.MV_NO_WORKSPACE);
                return;
            }
            account = new account_1.Account(address, userName, password, accountName, port, dataSource, folderPath, rpcName);
            const command = vscode_languageclient_1.Command.create("connect", "connect", account);
            progress.report({ increment: 0 });
            const message = yield driver.send(command);
            yield Promise.resolve();
            progress.report({ increment: 100 });
            const connSucc = message.includes("establish");
            if (connSucc) {
                vscode.window.showInformationMessage(message);
                status.connect(accountName);
                nextState();
            }
            else {
                vscode.window.showErrorMessage(message);
                status.disconnect();
                unconnected();
            }
        }));
    });
}
function connectAuto(path) {
    return __awaiter(this, void 0, void 0, function* () {
        const account = new U2ServerAccount("", path);
        const host = yield account.getAddress();
        if (!host)
            return;
        const userName = yield account.getUserName();
        if (!userName)
            return;
        const password = yield account.getPassword();
        if (!password)
            return;
        const port = yield account.getPort();
        if (!port)
            return;
        const accountName = yield account.getName();
        if (!accountName)
            return;
        const statusMsg = connectingMsg(accountName);
        let rpcName;
        rpcName = yield account.getRpcName();
        connecting_offline(statusMsg, host, userName, password, accountName, port, path, rpcName);
    });
}
function selectFolder() {
    return __awaiter(this, void 0, void 0, function* () {
        const pairs = extConfig.listFolders();
        const items = [];
        for (const s of pairs) {
            items.push({
                label: s[0],
                description: s[1]
            });
        }
        const selected = yield vscode.window.showQuickPick(items);
        if (selected) {
            return selected.description;
        }
        return undefined;
    });
}
function nextState() {
    switch (connStatus) {
        case ConnStatus.CONNECTED:
            status.show(true);
            connStatus = ConnStatus.UNCONNECT;
            break;
        case ConnStatus.CONNECTING:
            status.show(true);
            connStatus = ConnStatus.CONNECTED;
            break;
        case ConnStatus.UNCONNECT:
            status.show(false);
            connStatus = ConnStatus.CONNECTING;
            break;
    }
}
function unconnected() {
    connStatus = ConnStatus.UNCONNECT;
}
class U2ServerAccount {
    constructor(folderName, path) {
        this._folderName = folderName;
        this._path = path;
    }
    getPath() { return this._path; }
    getFolderName() { return this._folderName; }
    getName() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            if (!db.account || db.account.length === 0) {
                return yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_ACCOUNT });
            }
            return db.account;
        });
    }
    getNameDirectly() {
        const db = extConfig.getDb(this._path);
        if (!db)
            return undefined;
        return db.account;
    }
    getAddress() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            if (!db.host || db.host.length === 0) {
                return yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_ADDRESS });
            }
            return db.host;
        });
    }
    getUserName() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            if (!db.userName || db.userName.length === 0) {
                return yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_USER_NAME });
            }
            return db.userName;
        });
    }
    getPassword() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            if (!db.password || db.password.length === 0) {
                return yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_WORD, password: true });
            }
            return db.password;
        });
    }
    getPort() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            if (!db.port || db.port.length === 0) {
                return yield vscode.window.showInputBox({ placeHolder: msg_1.Msg.MV_NEED_PORT });
            }
            return db.port;
        });
    }
    getRpcName() {
        return __awaiter(this, void 0, void 0, function* () {
            const db = extConfig.getDb(this._path);
            if (!db)
                return undefined;
            return db.rpcName;
        });
    }
}
function enableStringAutoCompletion() {
    const config = vscode.workspace.getConfiguration("editor");
    const subConfig = config.get("quickSuggestions") || {};
    if (subConfig !== undefined) {
        subConfig["strings"] = true;
        config.update('quickSuggestions', subConfig, vscode.ConfigurationTarget.Workspace);
    }
}
function getAccounts() {
    const folders = extConfig.listFolders();
    const accounts = [];
    for (const folder of folders) {
        const account = new U2ServerAccount(folder[0], folder[1]);
        const accountName = account.getNameDirectly();
        if (accountName && accountName.length > 0) {
            accounts.push(account);
        }
    }
    return accounts;
}
//# sourceMappingURL=client.js.map