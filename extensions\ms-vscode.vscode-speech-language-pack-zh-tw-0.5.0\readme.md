# Chinese (Traditional, Taiwan) language support for Visual Studio Code Speech

<p align="center">
    <img src="https://github.com/microsoft/vscode/assets/900690/38106cff-2a99-4715-934c-cb1711bbf72c" alt="Logo">
</p>

Provides Chinese (Traditional, Taiwan) language support for the [Visual Studio Code Speech](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-speech) extension.

## Getting Started

Install the [GitHub Copilot Chat](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot-chat) extension and sign in. You will see a microphone icon in all chat interfaces that GitHub Copilot Chat extension provides.

## Usage

Configure the setting `accessibility.voice.speechLanguage` to the desired language.
