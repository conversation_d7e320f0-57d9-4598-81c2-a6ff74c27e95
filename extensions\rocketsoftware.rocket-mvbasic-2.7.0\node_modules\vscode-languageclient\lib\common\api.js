"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticPullMode = exports.vsdiag = void 0;
__exportStar(require("vscode-languageserver-protocol"), exports);
__exportStar(require("./features"), exports);
var diagnostic_1 = require("./diagnostic");
Object.defineProperty(exports, "vsdiag", { enumerable: true, get: function () { return diagnostic_1.vsdiag; } });
Object.defineProperty(exports, "DiagnosticPullMode", { enumerable: true, get: function () { return diagnostic_1.DiagnosticPullMode; } });
__exportStar(require("./client"), exports);
