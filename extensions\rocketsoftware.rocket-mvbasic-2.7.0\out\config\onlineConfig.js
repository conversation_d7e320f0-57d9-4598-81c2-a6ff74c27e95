"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWorkingState = getWorkingState;
exports.readConfig = readConfig;
exports.getServers = getServers;
exports.getCatalogParameters = getCatalogParameters;
exports.hasDuplicateNameServer = hasDuplicateNameServer;
exports.getFilterPatternFor = getFilterPatternFor;
const vscode = require("vscode");
const extConfig = require("./extConfig");
const path = require("path");
const fs = require("fs");
const fsprovider_1 = require("../online/explorer/fsprovider");
/**
 * Get current workspace status.
 * @returns Current workspace status, which could be used to control the welcome view.
 */
function getWorkingState() {
    let workspaces = vscode.workspace.workspaceFolders;
    if (workspaces != undefined) {
        if (extConfig.rmvExists(workspaces[0])) {
            return 2; // has rmv folder - init directly
        }
        if (hasOnlineConfig(workspaces[0])) {
            return 3; // has rmvonline folder, init directly
        }
        if (isEmptyDir(workspaces[0].uri.fsPath) == false) {
            return 2; // this should be active by "file associate" and has files/dirs folder without .rmv and .onlinermv, so init offline schema
        }
        return 4; // empty folder without .rmv and .rmvonline folder, manual activate local; panel do not initi workspace
    }
    else {
        return 1; // no workspace, activate local
    }
}
/**
 * Read online editing configuration file from current workspace.
 */
function readConfig() {
    const folders = vscode.workspace.workspaceFolders;
    if (folders === undefined || folders.length == 0) {
        return undefined;
    }
    const currentFolder = folders[0];
    const currentFolderPath = currentFolder.uri.fsPath;
    const nodes = extConfig.read(currentFolderPath, extConfig.servers, extConfig.onlineMode);
    if (nodes === undefined) {
        return undefined;
    }
    return nodes.servers;
}
/**
 * Get servers from configuration file. If have same name servers, only keep the first one.
 * @returns servers array.
 */
function getServers() {
    const servers = readConfig();
    if (servers === undefined) {
        return [];
    }
    let uniqueServers = new Array();
    servers.forEach(server => {
        let serverName = server.name ? server.name : server.address;
        if (server.address === undefined || server.address.trim().length == 0) {
            return;
        }
        if (!uniqueServers.some(uniqueServer => uniqueServer.name === serverName)) {
            let uniqueServer = new fsprovider_1.Server(serverName);
            uniqueServer.address = server.address;
            uniqueServer.username = server.username;
            uniqueServer.password = server.password;
            uniqueServer.port = server.port;
            uniqueServers.push(uniqueServer);
        }
    });
    return uniqueServers;
}
function getCatalogParameters() {
    const wsfs = vscode.workspace.workspaceFolders;
    if (wsfs === undefined || wsfs.length == 0) {
        return {
            catalog: "",
            initialCharacter: "",
            catalog_arguments: "",
            ud_compile_flavor: "",
            compile_arguments: ""
        };
    }
    const config = extConfig.read(wsfs[0].uri.fsPath, extConfig.basic_config, extConfig.onlineMode);
    const catalogType = config.catalog || "";
    const initialCharacter = config.initialCharacter || "";
    const catalog_arguments = config.catalog_arguments || "";
    const ud_compile_flavor = config.ud_compile_flavor;
    const compile_arguments = config.compile_arguments || "";
    return {
        catalog: catalogType,
        initialCharacter: initialCharacter,
        catalog_arguments: catalog_arguments,
        ud_compile_flavor: ud_compile_flavor,
        compile_arguments: compile_arguments
    };
}
/**
 *
 */
function hasDuplicateNameServer() {
    const servers = readConfig();
    if (servers === undefined) {
        return false;
    }
    let names = new Array();
    for (var server of servers) {
        let serverName = server.name ? server.name : server.address;
        for (var name of names) {
            if (serverName === name) {
                return true;
            }
        }
        names.push(serverName);
    }
    return false;
}
/**
 * Check whether the workspace has folder .rmvonline.
 * @param wf Workspace folder needs to be checked.
 * @returns true if exists.
 */
function hasOnlineConfig(wf) {
    const wsf = wf.uri.fsPath;
    const rmv = path.join(wsf, ".rmvonline");
    if (fs.existsSync(rmv)) {
        return true;
    }
    return false;
}
function isEmptyDir(dirPath) {
    const files = fs.readdirSync(dirPath);
    return files.length === 0;
}
function getFilterPatternFor(uri, serverLabel) {
    var _a, _b;
    const folders = vscode.workspace.workspaceFolders;
    if (folders === undefined || folders.length == 0) {
        return undefined;
    }
    const currentFolder = folders[0];
    const currentFolderPath = currentFolder.uri.fsPath;
    const configJson = extConfig.read(currentFolderPath, extConfig.servers, extConfig.onlineMode);
    ;
    const server = configJson.servers.find((srv) => srv.name === serverLabel);
    const pattern = (_b = (_a = server.filterPatterns) === null || _a === void 0 ? void 0 : _a.find((pattern) => pattern.path === uri)) === null || _b === void 0 ? void 0 : _b.value;
    return pattern === undefined ? "" : pattern;
}
//# sourceMappingURL=onlineConfig.js.map