import * as code from 'vscode';
import * as proto from 'vscode-languageserver-protocol';
export interface FileFormattingOptions {
    trimTrailingWhitespace?: boolean;
    trimFinalNewlines?: boolean;
    insertFinalNewline?: boolean;
}
export interface Converter {
    asUri(uri: code.Uri): string;
    asTextDocumentItem(textDocument: code.TextDocument): proto.TextDocumentItem;
    asTextDocumentIdentifier(textDocument: code.TextDocument): proto.TextDocumentIdentifier;
    asVersionedTextDocumentIdentifier(textDocument: code.TextDocument): proto.VersionedTextDocumentIdentifier;
    asOpenTextDocumentParams(textDocument: code.TextDocument): proto.DidOpenTextDocumentParams;
    asChangeTextDocumentParams(textDocument: code.TextDocument): proto.DidChangeTextDocumentParams;
    asChangeTextDocumentParams(event: code.TextDocumentChangeEvent, uri: code.Uri, version: number): proto.DidChangeTextDocumentParams;
    asCloseTextDocumentParams(textDocument: code.TextDocument): proto.DidCloseTextDocumentParams;
    asSaveTextDocumentParams(textDocument: code.TextDocument, includeContent?: boolean): proto.DidSaveTextDocumentParams;
    asWillSaveTextDocumentParams(event: code.TextDocumentWillSaveEvent): proto.WillSaveTextDocumentParams;
    asDidCreateFilesParams(event: code.FileCreateEvent): proto.CreateFilesParams;
    asDidRenameFilesParams(event: code.FileRenameEvent): proto.RenameFilesParams;
    asDidDeleteFilesParams(event: code.FileDeleteEvent): proto.DeleteFilesParams;
    asWillCreateFilesParams(event: code.FileCreateEvent): proto.CreateFilesParams;
    asWillRenameFilesParams(event: code.FileRenameEvent): proto.RenameFilesParams;
    asWillDeleteFilesParams(event: code.FileDeleteEvent): proto.DeleteFilesParams;
    asTextDocumentPositionParams(textDocument: code.TextDocument, position: code.Position): proto.TextDocumentPositionParams;
    asCompletionParams(textDocument: code.TextDocument, position: code.Position, context: code.CompletionContext): proto.CompletionParams;
    asSignatureHelpParams(textDocument: code.TextDocument, position: code.Position, context: code.SignatureHelpContext): proto.SignatureHelpParams;
    asWorkerPosition(position: code.Position): proto.Position;
    asPosition(value: null): null;
    asPosition(value: undefined): undefined;
    asPosition(value: code.Position): proto.Position;
    asPosition(value: code.Position | undefined | null): proto.Position | undefined | null;
    asPositions(value: readonly code.Position[], token?: code.CancellationToken): Promise<proto.Position[]>;
    asRange(value: null): null;
    asRange(value: undefined): undefined;
    asRange(value: code.Range): proto.Range;
    asRange(value: code.Range | undefined | null): proto.Range | undefined | null;
    asLocation(value: null): null;
    asLocation(value: undefined): undefined;
    asLocation(value: code.Location): proto.Location;
    asLocation(value: code.Location | undefined | null): proto.Location | undefined | null;
    asDiagnosticSeverity(value: code.DiagnosticSeverity): number;
    asDiagnosticTag(value: code.DiagnosticTag): number | undefined;
    asDiagnostic(item: code.Diagnostic): proto.Diagnostic;
    asDiagnostics(items: code.Diagnostic[], token?: code.CancellationToken): Promise<proto.Diagnostic[]>;
    asCompletionItem(item: code.CompletionItem, labelDetailsSupport?: boolean): proto.CompletionItem;
    asSymbolKind(item: code.SymbolKind): proto.SymbolKind;
    asSymbolTag(item: code.SymbolTag): proto.SymbolTag;
    asSymbolTags(items: ReadonlyArray<code.SymbolTag>): proto.SymbolTag[];
    asTextEdit(edit: code.TextEdit): proto.TextEdit;
    asReferenceParams(textDocument: code.TextDocument, position: code.Position, options: {
        includeDeclaration: boolean;
    }): proto.ReferenceParams;
    asCodeAction(item: code.CodeAction, token?: code.CancellationToken): Promise<proto.CodeAction>;
    asCodeActionContext(context: code.CodeActionContext, token?: code.CancellationToken): Promise<proto.CodeActionContext>;
    asInlineValueContext(context: code.InlineValueContext): proto.InlineValueContext;
    asCommand(item: code.Command): proto.Command;
    asCodeLens(item: code.CodeLens): proto.CodeLens;
    asFormattingOptions(options: code.FormattingOptions, fileOptions: FileFormattingOptions): proto.FormattingOptions;
    asDocumentSymbolParams(textDocument: code.TextDocument): proto.DocumentSymbolParams;
    asCodeLensParams(textDocument: code.TextDocument): proto.CodeLensParams;
    asDocumentLink(item: code.DocumentLink): proto.DocumentLink;
    asDocumentLinkParams(textDocument: code.TextDocument): proto.DocumentLinkParams;
    asCallHierarchyItem(value: code.CallHierarchyItem): proto.CallHierarchyItem;
    asTypeHierarchyItem(value: code.TypeHierarchyItem): proto.TypeHierarchyItem;
    asWorkspaceSymbol(item: code.SymbolInformation): proto.WorkspaceSymbol;
    asInlayHint(value: code.InlayHint): proto.InlayHint;
}
export interface URIConverter {
    (value: code.Uri): string;
}
export declare function createConverter(uriConverter?: URIConverter): Converter;
