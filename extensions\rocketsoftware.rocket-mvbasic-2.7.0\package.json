{"name": "rocket-mvbasic", "displayName": "Rocket MV BASIC", "description": "Rocket MV BASIC for Visual Studio Code", "publisher": "RocketSoftware", "version": "2.7.0", "icon": "logo-128x128.png", "engines": {"vscode": "^1.75.1"}, "categories": ["Programming Languages", "Linters", "Snippets", "Formatters", "Debuggers"], "keywords": ["MultiValue", "MV", "Pick", "BASIC", "PickBASIC", "MV BASIC", "UniData", "UniVerse"], "activationEvents": ["workspaceContains:.rmv", "workspaceContains:.rmvonline", "onDebugResolve:mvbasic"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "rocket-mvbasic", "aliases": ["Rocket MV BASIC", "rocketmvbasic"], "extensions": [".b", ".B"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "rocket-mvbasic", "scopeName": "source.mvbasic", "path": "./syntaxes/rocketmvbasic.tmLanguage.json"}], "commands": [{"command": "vscode-rocket.mv.basic.command.activate", "title": "%vscode-rocket.mv.basic.command.activate%"}, {"command": "vscode-rocket.mv.basic.command.editconfig", "title": "%vscode-rocket.mv.basic.command.editconfig%"}, {"command": "vscode-rocket.mv.basic.command.group.show", "title": "%vscode-rocket.mv.basic.command.group.show%"}, {"command": "vscode-rocket.mv.basic.command.group.hide", "title": "%vscode-rocket.mv.basic.command.group.hide%"}, {"command": "_vscode-rocket.mv.basic.command.group.refresh", "title": "%vscode-rocket.mv.basic.command.group.refresh%", "icon": "$(refresh)"}, {"command": "vscode-rocket.mv.basic.command.compile.do", "title": "%vscode-rocket.mv.basic.command.compile.do%"}, {"command": "vscode-rocket.mv.basic.command.catalog.do", "title": "%vscode-rocket.mv.basic.command.catalog.do%"}, {"command": "vscode-rocket.mv.basic.command.compile.select", "title": "%vscode-rocket.mv.basic.command.compile.select%"}, {"command": "vscode-rocket.mv.basic.command.codelens.show", "title": "%vscode-rocket.mv.basic.command.codelens.show%"}, {"command": "vscode-rocket.mv.basic.command.codelens.hide", "title": "%vscode-rocket.mv.basic.command.codelens.hide%"}, {"command": "_vscode-rocket.mv.basic.command.u2connect", "title": "%vscode-rocket.mv.basic.command.u2connect%"}, {"command": "vscode-rocket.mv.basic.command.online.server.connect", "title": "%vscode-rocket.mv.basic.command.online.server.connect%"}, {"command": "vscode-rocket.mv.basic.command.online.server.disconnect", "title": "%vscode-rocket.mv.basic.command.online.server.disconnect%"}, {"command": "vscode-rocket.mv.basic.command.online.server.add", "title": "%vscode-rocket.mv.basic.command.online.server.add%", "icon": "$(gist-new)"}, {"command": "vscode-rocket.mv.basic.command.online.server.refresh", "title": "%vscode-rocket.mv.basic.command.online.server.refresh%", "icon": "$(refresh)"}, {"command": "vscode-rocket.mv.basic.command.online.config.open", "title": "%vscode-rocket.mv.basic.command.online.config.open%", "icon": "$(folder-opened)"}, {"command": "vscode-rocket.mv.basic.command.online.basic.config.open", "title": "%vscode-rocket.mv.basic.command.online.basic.config.open%", "icon": "$(folder-opened)"}, {"command": "vscode-rocket.mv.basic.command.online.search", "title": "%vscode-rocket.mv.basic.command.online.search%", "icon": "$(search)"}, {"command": "vscode-rocket.mv.basic.command.online.server.delete", "title": "%vscode-rocket.mv.basic.command.online.server.delete%"}, {"command": "vscode-rocket.mv.basic.command.online.server.edit", "title": "%vscode-rocket.mv.basic.command.online.server.edit%"}, {"command": "vscode-rocket.mv.basic.command.online.server.add.file", "title": "%vscode-rocket.mv.basic.command.online.server.add.file%"}, {"command": "vscode-rocket.mv.basic.command.online.server.configure.pattern", "title": "%vscode-rocket.mv.basic.command.online.server.configure.pattern%"}, {"command": "vscode-rocket.mv.basic.command.online.server.rename.file", "title": "%vscode-rocket.mv.basic.command.online.server.rename.file%"}, {"command": "vscode-rocket.mv.basic.command.online.server.delete.file", "title": "%vscode-rocket.mv.basic.command.online.server.delete.file%"}, {"command": "vscode-rocket.mv.basic.command.online.server.init", "title": "%vscode-rocket.mv.basic.command.online.server.delete.file%"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.pick", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.pick%"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.unibasic", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.unibasic%"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.pick", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.pick%"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.unibasic", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.unibasic%"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.revelation", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.revelation%"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.douglas", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.douglas%"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.revelation", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.revelation%"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.douglas", "title": "%vscode-rocket.mv.basic.command.compile.do.ud.douglas%"}, {"command": "vscode-rocket.mv.basic.command.online.editing.sort.by.name.ascending", "title": "%vscode-rocket.mv.basic.command.online.editing.sort.by.name.ascending%"}, {"command": "vscode-rocket.mv.basic.command.online.editing.sort.by.name.descending", "title": "%vscode-rocket.mv.basic.command.online.editing.sort.by.name.descending%"}], "configuration": [{"title": "Rocket MV BASIC", "properties": {"rocketMvBasic.languageServer.developmentMode": {"type": "boolean", "scope": "application", "default": false, "markdownDescription": "%vscode-rocket.mv.basic.config.mode%"}, "rocketMvBasic.languageServer.jdkEnvironment": {"type": "string", "scope": "application", "markdownDescription": "%vscode-rocket.mv.basic.config.jdk11%"}}}], "viewsContainers": {"activitybar": [{"id": "rocket-explorer", "title": "Rocket Explorer", "icon": "logo-128x128.png"}]}, "views": {"explorer": [{"id": "rocket-mv-basic-group", "name": "Group View", "when": "groupOutlineEnabled"}], "rocket-explorer": [{"id": "online-editing", "name": "Online Editing", "icon": "logo-128x128.png"}, {"id": "searchResults", "name": "Search Results"}]}, "viewsWelcome": [{"view": "online-editing", "contents": "You need open an online-editing project or empty folder first to use this feature.\n[Open Folder](command:vscode.openFolder)\n", "when": "workingState==1"}, {"view": "online-editing", "contents": "If you want to use online editing feature, you need open an empty folder or open an online-editing project folder (contains \".rmvonline\").\n[Open Folder](command:vscode.openFolder)\n", "when": "workingState==2"}, {"view": "online-editing", "contents": "If you want to use online editing feature, you need open an empty folder or open an online-editing project folder. You can also initialize current folder as online-editing project folder.\n[Open Folder](command:vscode.openFolder)\n[Initialize](command:vscode-rocket.mv.basic.command.online.server.init)", "when": "workingState==4"}], "menus": {"view/title": [{"command": "vscode-rocket.mv.basic.command.online.server.add", "when": "view == online-editing && onlineEditingEnabled==true", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.server.refresh", "when": "view == online-editing && onlineEditingEnabled==true", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.config.open", "when": "view == online-editing && onlineEditingEnabled==true", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.basic.config.open", "when": "view == online-editing && onlineEditingEnabled==true", "group": "navigation"}, {"command": "_vscode-rocket.mv.basic.command.group.refresh", "when": "view == rocket-mv-basic-group", "group": "navigation"}], "view/item/context": [{"command": "vscode-rocket.mv.basic.command.online.server.refresh", "when": "view == online-editing", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.server.add", "when": "view == online-editing && viewItem!=U2File", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.server.connect", "when": "view==online-editing && viewItem==U2Server && online==false"}, {"command": "vscode-rocket.mv.basic.command.online.server.disconnect", "when": "view==online-editing && viewItem==U2Server && online==true"}, {"command": "vscode-rocket.mv.basic.command.online.server.edit", "when": "view==online-editing && viewItem==U2Server", "group": "modification"}, {"command": "vscode-rocket.mv.basic.command.online.server.delete", "when": "view==online-editing && viewItem==U2Server", "group": "modification"}, {"command": "vscode-rocket.mv.basic.command.online.server.add.file", "when": "view==online-editing && viewItem==U2Dir", "group": "modification"}, {"command": "vscode-rocket.mv.basic.command.online.server.configure.pattern", "when": "view==online-editing && viewItem!=U2File", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.server.delete.file", "when": "view==online-editing && viewItem==U2File", "group": "modification"}, {"submenu": "online.editing.sort.by.name", "when": "view==online-editing && viewItem!=U2Server", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.search", "when": "view == online-editing && onlineEditingEnabled==true && viewItem==U2Dir", "group": "navigation"}], "online.editing.sort.by.name": [{"command": "vscode-rocket.mv.basic.command.online.editing.sort.by.name.ascending", "group": "navigation"}, {"command": "vscode-rocket.mv.basic.command.online.editing.sort.by.name.descending", "group": "navigation"}], "commandPalette": [{"command": "vscode-rocket.mv.basic.command.activate", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.editconfig", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.group.show", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.group.hide", "when": "extensionEnabled"}, {"command": "_vscode-rocket.mv.basic.command.group.refresh", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.do", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.catalog.do", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.select", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.group.show", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.group.hide", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.codelens.show", "when": "extensionEnabled || online==true"}, {"command": "vscode-rocket.mv.basic.command.codelens.hide", "when": "extensionEnabled || online==true"}, {"command": "_vscode-rocket.mv.basic.command.u2connect", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.online.server.connect", "when": "online==true"}, {"command": "vscode-rocket.mv.basic.command.online.server.disconnect", "when": "online==true"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.pick", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.unibasic", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.pick", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.unibasic", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.revelation", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.do.ud.douglas", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.revelation", "when": "extensionEnabled"}, {"command": "vscode-rocket.mv.basic.command.compile.select.ud.douglas", "when": "extensionEnabled"}], "editor/context": [{"command": "vscode-rocket.mv.basic.command.compile.do", "when": "(extensionEnabled || online==true) && (datasource==uv || datasource==ud)"}, {"command": "vscode-rocket.mv.basic.command.catalog.do", "when": "(extensionEnabled || online==true) && (datasource==uv || datasource==ud)"}], "explorer/context": [{"command": "vscode-rocket.mv.basic.command.compile.select", "when": "(extensionEnabled || online==true) && (datasource==uv || datasource==ud)"}]}, "submenus": [{"id": "online.editing.sort.by.name", "label": "Sort by Name"}], "breakpoints": [{"language": "rocket-mvbasic"}], "debuggers": [{"type": "mv<PERSON><PERSON>", "label": "MVBasic Debug", "configurationAttributes": {"launch": {"required": [], "properties": {"program": {"type": "string", "description": "Absolute path to a MVBasic file.", "default": ""}, "arguments": {"type": "string", "description": "Additional arguments for debugging. Please refer more details from UniVerse / UniData user manual.", "default": ""}, "stopOnEntry": {"type": "boolean", "description": "Automatically stop after launch.", "default": true}, "trace": {"type": "boolean", "description": "Enable logging of the Debug Adapter Protocol.", "default": true}, "preLaunchTask": {"type": "boolean", "description": "The build task.", "default": "BASIC: Build"}, "dependencies": {"type": "array", "description": "Dependency files that need to be compiled. They should be absolute path.", "default": []}}}}, "initialConfigurations": [{"type": "mv<PERSON><PERSON>", "request": "launch", "name": "Launch Program", "program": "", "stopOnEntry": true, "preLaunchTask": "BASIC: Build", "dependencies": []}], "configurationSnippets": [{"label": "MVBasic Debug: Launch", "description": "A new configuration fro 'debugging' a user selected MVBasic file", "body": {"type": "mv<PERSON><PERSON>", "request": "launch", "name": "Launch Program", "program": "${command:vscode-rocket.mv.basic.command.getProgramName}", "stopOnEntry": true, "preLaunchTask": "BASIC: Build", "dependencies": []}}]}], "taskDefinitions": [{"type": "BASIC", "required": ["targets", "compile"], "properties": {"targets": {"type": "array", "description": "Files to be compiled. Please use related path, for example, BP/FILE_NAME."}, "compile": {"type": "object", "description": "Compilation related parameters.", "properties": {"dataSource": {"type": "string", "description": "Use UniVerse or UniData server for compiling.", "oneOf": [{"const": "UNIVERSE"}, {"const": "UNIDATA"}]}, "catalog": {"type": "string", "description": "Catalog types. For exampe, GLOBAL, LOCAL or NORMAL"}, "arguments": {"type": "string", "description": "Other arguments for compilation, please refer UniVerse/Unidata user manual for more details."}}, "if": {"properties": {"dataSource": {"const": "UNIVERSE"}}}, "then": {"required": ["dataSource"], "properties": {"catalog": {"oneOf": [{"const": "global"}, {"const": "local"}, {"const": "normal"}]}, "initialCharacter": {"type": "string", "description": "When catalog type is GLOBAL, you can specify an initial character for the catlaog program. If not specified, use * as default.", "oneOf": [{"const": "Asterisk mark (*)"}, {"const": "Exclamation mark (!)"}, {"const": "Minus sign (-)"}, {"const": "Dollars sign ($)"}, {"const": "*"}, {"const": "!"}, {"const": "-"}, {"const": "$"}]}}}, "else": {"required": ["dataSource"], "properties": {"catalog": {"oneOf": [{"const": "global"}, {"const": "local"}, {"const": "direct"}]}, "language": {"type": "string", "description": "UniData only. Select the basic language flavor: UniBasic or Pick or Advanced Revelation or McDonnell Douglas.", "oneOf": [{"const": "UniBasic"}, {"const": "Pick"}, {"const": "revelation"}, {"const": "<PERSON><PERSON><PERSON>"}]}}}}}}], "problemMatchers": [{"name": "basic-lint", "owner": "rocket-mvbasic", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": [{"regexp": "^Compiling: Source = \\'(.+)\\',.+$", "file": 1}, {"regexp": "^(\\d+)\\s+.+(\\s*)\\^(\\s*)(.+)", "line": 1, "message": 4, "loop": true}]}], "jsonValidation": [{"fileMatch": "db*mvbasic.json", "url": "./jsonschema/db-schema.json"}, {"fileMatch": "format*mvbasic.json", "url": "./jsonschema/format-schema.json"}, {"fileMatch": "group*mvbasic.json", "url": "./jsonschema/group-view-schema.json"}, {"fileMatch": "logging*mvbasic.json", "url": "./jsonschema/logging-schema.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "lint": "eslint src --ext ts -f node_modules/eslint-html-reporter/reporter.js -o eslint/index.html || exit 0", "watch": "tsc -watch -p ./", "pretest": "npm run compile", "test": "node ./out/test/runTest.js", "cov": "nyc --reporter=html npm run test"}, "devDependencies": {"@types/glob": "^7.2.0", "@types/mocha": "^9.0.0", "@types/node": "^18.0.3", "@types/vscode": "^1.70.2", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "eslint": "^7.27.0", "mocha": "^9.1.3", "nyc": "^15.1.0", "typescript": "^5.4.5", "@vscode/test-electron": "^2.1.5", "y18n": "^5.0.8", "eslint-html-reporter": "^0.7.4"}, "dependencies": {"glob": "^7.2.0", "vscode-languageclient": "^8.0.1", "@vscode/debugadapter": "^1.54.0"}, "__metadata": {"id": "f6b4e218-6271-4490-8934-353c13ac3deb", "publisherId": "7f9e2364-107c-40c6-9090-3180230b0190", "publisherDisplayName": "Rocket Software Inc.", "targetPlatform": "undefined", "isApplicationScoped": false, "updated": true, "isPreReleaseVersion": false, "installedTimestamp": 1748830217400, "preRelease": false, "size": 39909534}}