"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setContext = setContext;
exports.info = info;
exports.warn = warn;
exports.error = error;
exports.registerGroupView = registerGroupView;
const vscode = require("vscode");
const groupView_1 = require("./auto-group/groupView");
let context;
function setContext(ctx) {
    context = ctx;
}
function info(content) {
    vscode.window.showInformationMessage(content);
}
function warn(content) {
    vscode.window.showWarningMessage(content);
}
function error(content) {
    vscode.window.showErrorMessage(content);
}
function registerGroupView() {
    let groupView = new groupView_1.GroupView();
    vscode.window.registerTreeDataProvider("rocket-mv-basic-group", groupView);
    vscode.commands.executeCommand("setContext", "groupOutlineEnabled", true);
    vscode.commands.registerCommand('vscode-rocket.mv.basic.group.openFile', (filePath) => {
        const uri = vscode.Uri.file(filePath);
        vscode.commands.executeCommand('vscode.open', uri);
    });
    vscode.commands.registerCommand('_vscode-rocket.mv.basic.command.group.refresh', () => {
        groupView.refresh();
    });
    // register commands to show and hide group view
    const showTrieeView = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.group.show', () => {
        vscode.commands.executeCommand("setContext", "groupOutlineEnabled", true);
    });
    context.subscriptions.push(showTrieeView);
    const hideTreeView = vscode.commands.registerCommand('vscode-rocket.mv.basic.command.group.hide', () => {
        vscode.commands.executeCommand("setContext", "groupOutlineEnabled", false);
        groupView.refresh();
    });
    context.subscriptions.push(hideTreeView);
}
//# sourceMappingURL=ui.js.map