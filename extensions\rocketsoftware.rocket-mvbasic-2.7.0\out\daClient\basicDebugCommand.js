"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetProgramNameCommand = exports.BasicDebugCommand = void 0;
const vscode = require("vscode");
const provider_1 = require("../compile/provider");
const msg_1 = require("../msg");
const basicDebug_1 = require("./basicDebug");
const restart = require("./restart");
class BasicDebugCommand {
    constructor(resource) {
        this.resource = resource;
    }
    regist(configName, noDebug) {
        if (!this.resource && vscode.window.activeTextEditor) {
            this.resource = vscode.window.activeTextEditor.document.uri;
        }
        if (this.resource) {
            let config = {
                type: basicDebug_1.DEBUG_TYPE,
                name: configName,
                request: 'launch',
                program: this.resource.fsPath
            };
            const workspaceFolder = vscode.workspace.getWorkspaceFolder(this.resource);
            this.start(workspaceFolder, config, noDebug);
        }
        else {
            vscode.window.showErrorMessage(msg_1.Msg.DEBUG_NOT_OPEN_FILE);
        }
    }
    start(workspaceFolder, config, noDebug) {
        if (noDebug) {
            vscode.debug.startDebugging(workspaceFolder, config, { noDebug: noDebug });
        }
        else {
            vscode.debug.startDebugging(workspaceFolder, config);
        }
    }
}
exports.BasicDebugCommand = BasicDebugCommand;
class GetProgramNameCommand {
    constructor(config) {
        this.config = config;
    }
    regist() {
        const file = vscode.window.showQuickPick(vscode.workspace.findFiles("**/*").then((uris) => {
            return uris
                .filter((uri, index, array) => uri.fsPath.startsWith(`${this.config.workspacePath}`))
                .filter((uri, index, array) => uri.fsPath.indexOf(`.O`) == -1)
                .filter((uri, index, array) => uri.fsPath.indexOf(`.O`) == -1)
                .map((value, index, array) => {
                return value.fsPath;
            });
        }));
        return file.then(file => {
            if (file) {
                var path = vscode.Uri.file(file);
                var files = [file];
                vscode.commands.executeCommand('vscode.open', path);
                provider_1.BasicBuildTaskProvider.getInstance().clearFilesList();
                provider_1.BasicBuildTaskProvider.getInstance().addQuickBuildFile(file);
                // Save debug files for further using
                restart.saveDebugFiles(files);
                return file;
            }
        }, reason => {
            return undefined;
        });
    }
}
exports.GetProgramNameCommand = GetProgramNameCommand;
//# sourceMappingURL=basicDebugCommand.js.map