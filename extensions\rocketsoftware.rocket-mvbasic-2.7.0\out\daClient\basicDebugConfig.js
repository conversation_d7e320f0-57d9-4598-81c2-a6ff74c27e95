"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicDebugConfig = void 0;
const vscode = require("vscode");
const editor_1 = require("../common/editor");
const env_1 = require("../common/env");
const extension_1 = require("../extension");
const msg_1 = require("../msg");
const basicDebug_1 = require("./basicDebug");
const mvClient = require("../mvClient/client");
const extension_2 = require("../extension");
class BasicDebugConfig {
    constructor(config, workspaceFolder) {
        this.config = config;
        this.workspaceFolder = workspaceFolder;
    }
    checkAndReset() {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            let result = undefined;
            const editor = vscode.window.activeTextEditor;
            if (this.workspaceFolder === undefined) {
                vscode.window.showErrorMessage(msg_1.Msg.DEBUG_WITHOUT_WORKSPACE);
            }
            else if (!mvClient.isConnected()) {
                vscode.window.showErrorMessage(msg_1.Msg.DEBUG_WITHOUT_CONNECT);
            }
            else if (!extension_2.online && ((_a = mvClient.getAccount()) === null || _a === void 0 ? void 0 : _a.getFolderPath()) !== this.workspaceFolder.uri.fsPath) {
                vscode.window.showErrorMessage(msg_1.Msg.WRONG_WORKSPACE);
            }
            else if (editor === undefined
                && (this.config.program == undefined || this.config.program == "")) {
                // show message: need open a basic file
                //result = await this.chooseFile();
                vscode.window.showWarningMessage(msg_1.Msg.DEBUG_WITHOUT_EDITOR);
                return undefined;
            }
            else if (editor !== undefined && editor.document.languageId !== env_1.LANGUAGE_ID
                && (this.config.program == undefined || this.config.program == "")) {
                // show message language id is incorrect
                //result = await this.checkLanguageId(editor);
                vscode.window.showWarningMessage(msg_1.Msg.DEBUG_WRONG_LANGUAGE_ID);
                return undefined;
            }
            else {
                vscode.window.showInformationMessage(msg_1.Msg.DEBUG_COMPILE_CONFIG);
                result = this.doSet(editor);
            }
            if (result != undefined) {
                result = yield this.checkSameAccount(result);
            }
            return result;
        });
    }
    doSet(editor) {
        var _a, _b;
        let configTemp = undefined;
        const workspacePath = (_a = this.workspaceFolder) === null || _a === void 0 ? void 0 : _a.uri.fsPath;
        const program = this.setProgram(this.config.program, workspacePath, editor);
        if (program !== undefined) {
            configTemp = this.config;
            configTemp.javaExe = extension_1.javaExe;
            configTemp.type = this.setType(this.config.type);
            configTemp.name = this.setName(this.config.name);
            configTemp.program = program;
            configTemp.request = this.setRequest(this.config.request);
            configTemp.stopOnEntry = this.setStopOnEntry(this.config.stopOnEntry);
            configTemp.preLaunchTask = this.setPreLaunchTask(this.config.preLaunchTask);
            configTemp.account = mvClient.getAccount();
            configTemp.workspacePath = extension_2.online ? (_b = extension_2.online.getAccount()) === null || _b === void 0 ? void 0 : _b.getName() : workspacePath;
            configTemp.packagePath = (0, env_1.getLspJarPath)();
            configTemp.dependencies = this.setDependencies(program, this.config.dependencies, workspacePath);
        }
        return configTemp;
    }
    checkSameAccount(result) {
        return __awaiter(this, void 0, void 0, function* () {
            let tempResult = result;
            if (result.program != env_1.COMMAND_PREFIX + basicDebug_1.COMMAND_GET_PROGRAM_NAME + "}") {
                const isSameAccount = mvClient.isSameAccount(result.program);
                if (typeof isSameAccount == "string") {
                    const yesItem = { title: 'Yes' };
                    const cancelItem = { title: 'Cancel', isCloseAffordance: true };
                    yield vscode.window.showWarningMessage(isSameAccount, cancelItem, yesItem)
                        .then(selection => {
                        if (selection === yesItem && result !== undefined && tempResult != undefined) {
                            tempResult.isSameAccount = true;
                        }
                        else {
                            tempResult = undefined;
                        }
                    });
                }
            }
            return tempResult;
        });
    }
    setType(type) {
        if (!type) {
            type = 'mvbasic';
        }
        return type;
    }
    setName(name) {
        if (!name) {
            name = 'Debug file';
        }
        return name;
    }
    setRequest(request) {
        if (!request) {
            request = 'launch';
        }
        return request;
    }
    setStopOnEntry(stopOnEntry) {
        if (!stopOnEntry) {
            stopOnEntry = true;
        }
        return stopOnEntry;
    }
    setPreLaunchTask(preLaunchTask) {
        if (!preLaunchTask) {
            preLaunchTask = "BASIC: Build";
        }
        return preLaunchTask;
    }
    setProgram(program, workspacePath, editor) {
        var _a;
        let tempProgram = program;
        if (program === undefined || program === "") {
            if (editor) {
                tempProgram = editor.document.uri.fsPath;
            }
            else {
                tempProgram = (_a = (0, editor_1.getBasicEditor)()) === null || _a === void 0 ? void 0 : _a.document.uri.fsPath;
            }
        }
        else if (!program.includes(basicDebug_1.COMMAND_GET_PROGRAM_NAME)) {
            tempProgram = this.handleWorkspacePath(program, workspacePath);
        }
        if (!extension_2.online && tempProgram && workspacePath !== undefined &&
            !this.isIn(tempProgram, workspacePath) && !tempProgram.startsWith(env_1.COMMAND_PREFIX)) {
            vscode.window.showErrorMessage(msg_1.Msg.DEBUG_NOT_UNDER_WORKSPACE);
            tempProgram = undefined;
        }
        return tempProgram;
    }
    setDependencies(program, dependencies, workspacePath) {
        if (dependencies === undefined) {
            dependencies = [];
        }
        for (let i = 0; i < dependencies.length; i++) {
            if (dependencies[i].length != 0) {
                dependencies[i] = this.handleWorkspacePath(dependencies[i], workspacePath);
            }
        }
        if (program.length != 0 && !program.startsWith(env_1.COMMAND_PREFIX)) {
            dependencies.push(program);
        }
        return dependencies;
    }
    handleWorkspacePath(path, workspacePath) {
        const replacedStr = "${workspaceFolder}";
        let result = path;
        if (path.indexOf(replacedStr) >= 0 && workspacePath !== undefined) {
            result = path.replace("${workspaceFolder}", workspacePath);
        }
        return result;
    }
    isIn(filePath, dirPath) {
        let rFilePath = filePath.replace(/\\/g, '/').toLowerCase();
        let rDirPath = dirPath.replace(/\\/g, '/').toLowerCase();
        return rFilePath.startsWith(rDirPath);
    }
}
exports.BasicDebugConfig = BasicDebugConfig;
//# sourceMappingURL=basicDebugConfig.js.map