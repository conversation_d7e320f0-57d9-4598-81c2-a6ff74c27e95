{"version": 3, "file": "lsp.js", "sourceRoot": "", "sources": ["../src/lsp.ts"], "names": [], "mappings": ";;;AA4BA,sBAoBC;AAyFD,4BAgBC;AAED,gCAIC;AA/JD,iCAAiC;AACjC,2BAA2B;AAC3B,gDAAgD;AAEhD,qDAOoC;AACpC,oCAAoC;AACpC,oDAAoD;AACpD,2BAA0B;AAC1B,8CAA6C;AAC7C,+BAA4B;AAC5B,qCAA0C;AAC1C,kDAA8C;AAE9C,sCAAyC;AAKzC,IAAI,SAAoB,CAAC;AACzB,IAAI,OAAO,GAAY,KAAK,CAAC;AAE7B,SAAgB,KAAK,CAAC,SAAiB;IACnC,IAAI,sBAAc,EAAE,CAAC;QACjB,OAAQ;IACZ,CAAC;IAED,IAAI,aAAa,GAAG,kBAAkB,EAAE,CAAC;IACzC,IAAI,aAAa,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAClD,IAAI,CAAC,aAAa,EAAE,CAAC;QACjB,OAAQ;IACZ,CAAC;IAED,sBAAc,GAAG,IAAI,qBAAc,CAC/B,6BAA6B,EAC7B,iCAAiC,EACjC,aAAa,EACb,aAAa,CAChB,CAAC;IACF,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;IACpC,qBAAqB,CAAC,SAAS,CAAC,CAAC;IACjC,mBAAmB,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,kBAAkB;;IACvB,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,SAAS,EAAE,CAAC;QACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAG,MAAA,MAAM,CAAC,SAAS,CAAC,gBAAgB,0CAAE,MAAM,CAAA,EAAE,CAAC,EAAE,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACpH,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IACD,MAAM,aAAa,GAA0B;QACzC,+CAA+C;QAC/C,gBAAgB,EAAE;YACd,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,EAAE;YAC7C,EAAE,MAAM,EAAE,eAAS,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,EAAE;SACnD;QACD,WAAW,EAAE;YACT,UAAU,EAAE,cAAc;SAC7B;KACJ,CAAC;IAEF,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAiB;IACzC,IAAI,aAA4B,CAAC;IACjC,MAAM,eAAe,GAAY,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC;IACzG,SAAS,GAAG,IAAI,qBAAS,CAAC,SAAS,CAAC,CAAC;IACrC,IAAI,CAAC,eAAe,EAAE,CAAC;QACnB,4BAA4B;QAC5B,qFAAqF;QACrF,qEAAqE;QACrE,wCAAwC;QACxC,kBAAkB;QAClB,IAAI,OAAO,GAAuB,GAAG,CAAC,UAAU,EAAE,CAAC;QACnD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,4CAA4C;QAC5C,+DAA+D;QAC/D,IAAI,IAAI,GAAW,SAAS,CAAC;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC/C,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClC,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,aAAa,GAAG;YACZ,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE;gBACJ,YAAY;gBACZ,0EAA0E;gBACxE,MAAM;gBACN,SAAS,CAAC,kBAAkB,EAAE;gBAC9B,GAAG,CAAC,aAAa,EAAE;gBACnB,IAAI;gBACJ,QAAQ;gBACR,UAAU,CAAC,GAAG,EAAE;aACnB;YACD,OAAO,EAAE;YACN,gBAAgB;aAClB;SACJ,CAAC;IACN,CAAC;SAAM,CAAC;QAEJ,IAAI,QAAQ,GAAW,GAAG,CAAC,iBAAiB,CAAC;QAC7C,IAAI,QAAQ,GAAW,GAAG,CAAC,qBAAqB,CAAC;QAEjD,aAAa,GAAG,GAAG,EAAE;YACjB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;gBACvB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAe;gBACvB,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACjB,CAAC;YAEF,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IAED,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,SAAgB,QAAQ,CAAC,SAAiB;IACtC,KAAK,CAAC,SAAS,CAAC,CAAC;IAEjB,IAAI,sBAAc,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,sBAAc,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC7B,MAAM,OAAO,GAAY,cAAO,CAAC,MAAM,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC1H,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,EAAE;QAC9C,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrD,MAAM,OAAO,GAAY,cAAO,CAAC,MAAM,CAAC,YAAY,EAAE,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7F,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;QACjB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,sBAAc,EAAE,CAAC;QACjB,sBAAc,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAAC,SAAiB;IAC5C,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAE;QAClC,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,OAAO,EAAE,CAAC;YAChE,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAG,CAAC,eAAe,CAAC,CAAC;QACxD,CAAC;aAAM,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,OAAO,EAAE,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAG,CAAC,gBAAgB,CAAC,CAAC;YAE3D,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,SAAS,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;gBACtC,EAAE,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC;YAED,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;gBACpB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC;QAEL,CAAC;aAAM,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,KAAK,YAAK,CAAC,OAAO,EAAE,CAAC;YACtE,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,SAAG,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,mBAAmB;IACxB,sBAAc,CAAC,WAAW,CAAC,kBAAS,CAAC,CAAC;AAC1C,CAAC"}