"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicDebugAdapterFactory = void 0;
const vscode = require("vscode");
const env = require("../common/env");
const mvClient = require("../mvClient/client");
const env_1 = require("../common/env");
class BasicDebugAdapterFactory {
    createDebugAdapterDescriptor(_session, executable) {
        const args = _session.configuration.arguments;
        return mvClient.startDebugger(args ? args : "").then(result => {
            if (result == env_1.ERROR_INT) {
                throw new Error("Error - may be old u2 server");
            }
            else if (result == env_1.NO_GRPC_SUPPORT_ERROR_INT) {
                throw new Error("Error: Debugging in online mode requires U2 servers with gRPC protocol. Use Universe 11.4.1+ and Unidata 8.3.1+.");
            }
            else if (result == env_1.NO_GRPC_SERVICE_ERROR_INT) {
                throw new Error("Error: To debug in online mode, the gRPC service must be running on the server. Please check if the service is active.");
            }
            else {
                return new vscode.DebugAdapterServer(result, env.DEFAULT_HOST_NAME);
            }
        });
    }
}
exports.BasicDebugAdapterFactory = BasicDebugAdapterFactory;
//# sourceMappingURL=basicDebugAdapterFactory.js.map