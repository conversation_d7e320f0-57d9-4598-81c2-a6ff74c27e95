{"name": "@vscode/node-speech", "version": "1.6.0", "description": "Native bindings for Micrsooft Speech SDK", "repository": {"type": "git", "url": "https://github.com/Microsoft/node-speech"}, "homepage": "https://github.com/Microsoft/node-speech", "keywords": ["vscode"], "license": "MIT", "author": {"name": "Microsoft Corporation"}, "engines": {"node": ">=18.0.0"}, "main": "index.js", "types": "index.d.ts", "scripts": {"install": "pwsh setup-packages.ps1 && node preinstall.js", "postinstall": "node-gyp rebuild", "prepublish": "tsc", "prepare": "npm run test", "watch": "tsc -w", "test": "jest"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "20.x", "jest": "^29.7.0", "typescript": "^5.4.x"}, "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^8.0.0", "yauzl": "^3.1.3"}, "jest": {"testMatch": ["**/*.test.js"]}}