"use strict";
/**
 *  buildTerminal - build / debug terminal related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicBuildTaskTerminal = void 0;
const path = require("path");
const vscode = require("vscode");
const env_1 = require("../common/env");
const compiler_1 = require("./compiler/compiler");
const udCompiler_1 = require("./compiler/udCompiler");
const uvCompiler_1 = require("./compiler/uvCompiler");
const provider_1 = require("./provider");
const mvClient = require("../mvClient/client");
class BasicBuildTaskTerminal {
    constructor(files, definition, bCatalog, isSameAccount) {
        this.files = files;
        this.definition = definition;
        this.writeEmitter = new vscode.EventEmitter();
        this.onDidWrite = this.writeEmitter.event;
        this.closeEmitter = new vscode.EventEmitter();
        this.onDidClose = this.closeEmitter.event;
        this.succeed = 0;
        this.failed = 0;
        this.total = 0;
        this.ignored = 0;
        this.bIgnoreCatalog = bCatalog;
        this.isSameAccount = isSameAccount;
        switch (definition.compile.dataSource) {
            case "UNIVERSE":
                this.compiler = new uvCompiler_1.UvCompiler(definition);
                break;
            default:
                this.compiler = new udCompiler_1.UdCompiler(definition);
        }
    }
    open(initialDimensions) {
        this.doBuild();
        // Clear build files list
        provider_1.BasicBuildTaskProvider.getInstance().clearFilesList();
    }
    close() {
        // ignored
    }
    doBuild() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.files == undefined || this.files.length == 0) {
                this.printMessage("Error: no files specified.");
                this.exit(1);
                return;
            }
            for (const file of this.files) {
                const isSameWorkspace = mvClient.isSameWorkspace(file);
                if (isSameWorkspace !== true) {
                    this.ignore(file, isSameWorkspace);
                    continue;
                }
                if (this.isSameAccount == undefined) {
                    this.isSameAccount = mvClient.isSameAccount(file);
                }
                if (this.isSameAccount === true) {
                    yield this.build(file);
                }
                if (this.isSameAccount === false) {
                    this.ignore(file);
                }
                if (typeof this.isSameAccount == "string") {
                    const yesItem = { title: 'Yes' };
                    const cancelItem = { title: 'Cancel', isCloseAffordance: true };
                    yield vscode.window.showWarningMessage(this.isSameAccount, cancelItem, yesItem)
                        .then((selection) => __awaiter(this, void 0, void 0, function* () {
                        if (selection === yesItem) {
                            this.isSameAccount = true;
                            yield this.build(file);
                        }
                        else {
                            this.isSameAccount = false;
                            this.ignore(file);
                        }
                    }));
                }
            }
            this.isSameAccount = undefined;
            this.showResult();
        });
    }
    build(file) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.compiler.run(file, this.bIgnoreCatalog);
            this.printMessage(result.message);
            switch (result.status) {
                case compiler_1.Status.INVALID_FILE:
                    this.ignored += 1;
                    break;
                case compiler_1.Status.BUILD_SUCCEED:
                    this.succeed += 1;
                    break;
                case compiler_1.Status.BUILD_FAILED:
                    this.failed += 1;
                    break;
                case compiler_1.Status.SERVER_NOT_CONNECTED: {
                    mvClient.silentDisconnect();
                    this.exit(1);
                    //return;
                }
                default: break;
            }
            this.total += 1;
        });
    }
    ignore(file, reason) {
        if (reason === undefined) {
            this.printMessage("Compiling:" + path.basename(file) + " -----> " + "compiling ignored" + env_1.NEWLINE);
        }
        else {
            this.printMessage("Compiling:" + path.basename(file) + " -----> " + "compiling ignored");
            this.printMessage(reason + env_1.NEWLINE);
        }
        this.ignored += 1;
    }
    showResult() {
        this.printMessage('----------');
        const msg = "Compilation completed, total " + this.total + " file(s):" + env_1.NEWLINE +
            "    Successful: " + this.succeed + env_1.NEWLINE +
            "    Failed:  " + this.failed + env_1.NEWLINE +
            "    Ignored: " + this.ignored;
        this.printMessage(msg);
        if (this.failed > 0) {
            this.exit(1);
        }
        else {
            this.exit(0);
        }
    }
    printMessage(msg) {
        this.writeEmitter.fire(msg + env_1.NEWLINE);
    }
    exit(status) {
        this.closeEmitter.fire(status);
    }
}
exports.BasicBuildTaskTerminal = BasicBuildTaskTerminal;
//# sourceMappingURL=buildTerminal.js.map