"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InlineValueFeature = void 0;
const vscode_1 = require("vscode");
const vscode_languageserver_protocol_1 = require("vscode-languageserver-protocol");
const features_1 = require("./features");
class InlineValueFeature extends features_1.TextDocumentLanguageFeature {
    constructor(client) {
        super(client, vscode_languageserver_protocol_1.InlineValueRequest.type);
    }
    fillClientCapabilities(capabilities) {
        (0, features_1.ensure)((0, features_1.ensure)(capabilities, 'textDocument'), 'inlineValue').dynamicRegistration = true;
        (0, features_1.ensure)((0, features_1.ensure)(capabilities, 'workspace'), 'inlineValue').refreshSupport = true;
    }
    initialize(capabilities, documentSelector) {
        this._client.onRequest(vscode_languageserver_protocol_1.InlineValueRefreshRequest.type, async () => {
            for (const provider of this.getAllProviders()) {
                provider.onDidChangeInlineValues.fire();
            }
        });
        const [id, options] = this.getRegistration(documentSelector, capabilities.inlineValueProvider);
        if (!id || !options) {
            return;
        }
        this.register({ id: id, registerOptions: options });
    }
    registerLanguageProvider(options) {
        const selector = options.documentSelector;
        const eventEmitter = new vscode_1.EventEmitter();
        const provider = {
            onDidChangeInlineValues: eventEmitter.event,
            provideInlineValues: (document, viewPort, context, token) => {
                const client = this._client;
                const provideInlineValues = (document, viewPort, context, token) => {
                    const requestParams = {
                        textDocument: client.code2ProtocolConverter.asTextDocumentIdentifier(document),
                        range: client.code2ProtocolConverter.asRange(viewPort),
                        context: client.code2ProtocolConverter.asInlineValueContext(context)
                    };
                    return client.sendRequest(vscode_languageserver_protocol_1.InlineValueRequest.type, requestParams, token).then((values) => {
                        if (token.isCancellationRequested) {
                            return null;
                        }
                        return client.protocol2CodeConverter.asInlineValues(values, token);
                    }, (error) => {
                        return client.handleFailedRequest(vscode_languageserver_protocol_1.InlineValueRequest.type, token, error, null);
                    });
                };
                const middleware = client.middleware;
                return middleware.provideInlineValues
                    ? middleware.provideInlineValues(document, viewPort, context, token, provideInlineValues)
                    : provideInlineValues(document, viewPort, context, token);
            }
        };
        return [this.registerProvider(selector, provider), { provider: provider, onDidChangeInlineValues: eventEmitter }];
    }
    registerProvider(selector, provider) {
        return vscode_1.languages.registerInlineValuesProvider(this._client.protocol2CodeConverter.asDocumentSelector(selector), provider);
    }
}
exports.InlineValueFeature = InlineValueFeature;
