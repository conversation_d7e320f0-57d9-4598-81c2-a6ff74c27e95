Copyright 2025

[MVDT : MVVS_v2.7.0]

Phase: RELEASED
Distribution: EXTERNAL

Notices Report Content
 * License Data
 * License Text

Components: 

antlr4-c3 1.1.12: https://github.com/mike-lischke/antlr4-c3 : MIT License
Quarkus - Dev tools - Project Core Extension Codestarts 3.2.7.Final : Apache
License 2.0
 UDJrb 0.0.3: http://github.com/BinaryMuse/UDJrb/ : Rocket
Proprietary License
Visual Studio Code 1.37.0: http://code.visualstudio.com : MIT License
Visual Studio Code 1.41.1: http://code.visualstudio.com : MIT License
Visual Studio Code 1.65.0: http://code.visualstudio.com : MIT License

Licenses: 

Apache License 2.0
(Quarkus - Dev tools - Project Core Extension Codestarts 3.2.7.Final)

Apache License
Version 2.0, January 2004
=========================


http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

"License" shall mean the terms and conditions for use, reproduction, and
distribution as defined by Sections 1 through 9 of this document.

"Licensor" shall mean the copyright owner or entity authorized by the
copyright owner that is granting the License.

"Legal Entity" shall mean the union of the acting entity and all other
entities that control, are controlled by, or are under common control with
that entity.  For the purposes of this definition, "control" means (i) the
power, direct or indirect, to cause the direction or management of such
entity, whether by contract or otherwise, or (ii) ownership of fifty percent
(50%) or more of the outstanding shares, or (iii) beneficial ownership of such
entity.

"You" (or "Your") shall mean an individual or Legal Entity exercising
permissions granted by this License.

"Source" form shall mean the preferred form for making modifications,
including but not limited to software source code, documentation source, and
configuration files.

"Object" form shall mean any form resulting from mechanical transformation or
translation of a Source form, including but not limited to compiled object
code, generated documentation, and conversions to other media types.

"Work" shall mean the work of authorship, whether in Source or Object form,
made available under the License, as indicated by a copyright notice that is
included in or attached to the work (an example is provided in the Appendix
below).

"Derivative Works" shall mean any work, whether in Source or Object form, that
is based on (or derived from) the Work and for which the editorial revisions,
annotations, elaborations, or other modifications represent, as a whole, an
original work of authorship. For the purposes of this License, Derivative
Works shall not include works that remain separable from, or merely link (or
bind by name) to the interfaces of, the Work and Derivative Works thereof.

"Contribution" shall mean any work of authorship, including the original
version of the Work and any modifications or additions to that Work or
Derivative Works thereof, that is intentionally submitted to Licensor for
inclusion in the Work by the copyright owner or by an individual or Legal
Entity authorized to submit on behalf of the copyright owner. For the purposes
of this definition, "submitted" means any form of electronic, verbal, or
written communication sent to the Licensor or its representatives, including
but not limited to communication on electronic mailing lists, source code
control systems, and issue tracking systems that are managed by, or on behalf
of, the Licensor for the purpose of discussing and improving the Work, but
excluding communication that is conspicuously marked or otherwise designated
in writing by the copyright owner as "Not a Contribution."

"Contributor" shall mean Licensor and any individual or Legal Entity on behalf
of whom a Contribution has been received by Licensor and subsequently
incorporated within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of this
License, each Contributor hereby grants to You a perpetual, worldwide,
non-exclusive, no-charge, royalty-free, irrevocable copyright license to
reproduce, prepare Derivative Works of, publicly display, publicly perform,
sublicense, and distribute the Work and such Derivative Works in Source or
Object form.

3. Grant of Patent License. Subject to the terms and conditions of this
License, each Contributor hereby grants to You a perpetual, worldwide,
non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this
section) patent license to make, have made, use, offer to sell, sell, import,
and otherwise transfer the Work, where such license applies only to those
patent claims licensable by such Contributor that are necessarily infringed by
their Contribution(s) alone or by combination of their Contribution(s) with
the Work to which such Contribution(s) was submitted. If You institute patent
litigation against any entity (including a cross-claim or counterclaim in a
lawsuit) alleging that the Work or a Contribution incorporated within the Work
constitutes direct or contributory patent infringement, then any patent
licenses granted to You under this License for that Work shall terminate as of
the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the Work or
Derivative Works thereof in any medium, with or without modifications, and in
Source or Object form, provided that You meet the following conditions:

  a. You must give any other recipients of the Work or Derivative Works a copy
  of this License; and

  b. You must cause any modified files to carry prominent notices stating that
  You changed the files; and

  c. You must retain, in the Source form of any Derivative Works that You
  distribute, all copyright, patent, trademark, and attribution notices from
  the Source form of the Work, excluding those notices that do not pertain to
  any part of the Derivative Works; and

  d. If the Work includes a "NOTICE" text file as part of its distribution,
  then any Derivative Works that You distribute must include a readable copy
  of the attribution notices contained within such NOTICE file, excluding
  those notices that do not pertain to any part of the Derivative Works, in at
  least one of the following places: within a NOTICE text file distributed as
  part of the Derivative Works; within the Source form or documentation, if
  provided along with the Derivative Works; or, within a display generated by
  the Derivative Works, if and wherever such third-party notices normally
  appear.  The contents of the NOTICE file are for informational purposes only
  and do not modify the License. You may add Your own attribution notices
  within Derivative Works that You distribute, alongside or as an addendum to
  the NOTICE text from the Work, provided that such additional attribution
  notices cannot be construed as modifying the License.

You may add Your own copyright statement to Your modifications and may provide
additional or different license terms and conditions for use, reproduction, or
distribution of Your modifications, or for any such Derivative Works as a
whole, provided Your use, reproduction, and distribution of the Work otherwise
complies with the conditions stated in this License.

5. Submission of Contributions. Unless You explicitly state otherwise, any
Contribution intentionally submitted for inclusion in the Work by You to the
Licensor shall be under the terms and conditions of this License, without any
additional terms or conditions. Notwithstanding the above, nothing herein
shall supersede or modify the terms of any separate license agreement you may
have executed with Licensor regarding such Contributions.

6. Trademarks. This License does not grant permission to use the trade names,
trademarks, service marks, or product names of the Licensor, except as
required for reasonable and customary use in describing the origin of the Work
and reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or agreed to in
writing, Licensor provides the Work (and each Contributor provides its
Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied, including, without limitation, any warranties
or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
PARTICULAR PURPOSE. You are solely responsible for determining the
appropriateness of using or redistributing the Work and assume any risks
associated with Your exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory, whether in
tort (including negligence), contract, or otherwise, unless required by
applicable law (such as deliberate and grossly negligent acts) or agreed to in
writing, shall any Contributor be liable to You for damages, including any
direct, indirect, special, incidental, or consequential damages of any
character arising as a result of this License or out of the use or inability
to use the Work (including but not limited to damages for loss of goodwill,
work stoppage, computer failure or malfunction, or any and all other
commercial damages or losses), even if such Contributor has been advised of
the possibility of such damages.

9. Accepting Warranty or Additional Liability. While redistributing the Work
or Derivative Works thereof, You may choose to offer, and charge a fee for,
acceptance of support, warranty, indemnity, or other liability obligations
and/or rights consistent with this License. However, in accepting such
obligations, You may act only on Your own behalf and on Your sole
responsibility, not on behalf of any other Contributor, and only if You agree
to indemnify, defend, and hold each Contributor harmless for any liability
incurred by, or claims asserted against, such Contributor by reason of your
accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work

To apply the Apache License to your work, attach the following boilerplate
notice, with the fields enclosed by brackets "[]" replaced with your own
identifying information. (Don't include the brackets!) The text should be
enclosed in the appropriate comment syntax for the file format. We also
recommend that a file or class name and description of purpose be included on
the same "printed page" as the copyright notice for easier identification
within third-party archives.

  Copyright [yyyy] [name of copyright owner] Licensed under the Apache
  License, Version 2.0 (the "License"); you may not use this file except in
  compliance with the License. You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law
  or agreed to in writing, software distributed under the License is
  distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the specific language
  governing permissions and limitations under the License.
 
 ---
 
 MIT
  License
 (Visual Studio Code 1.37.0)
 
 Permission is hereby granted,
  free of charge, to any person obtaining a copy of this software and
  associated documentation files (the \"Software\"), to deal in the Software
  without restriction, including without limitation the rights to use, copy,
  modify, merge, publish, distribute, sublicense, and/or sell copies of the
  Software, and to permit persons to whom the Software is furnished to do so,
  subject to the following conditions:", "", "The above copyright notice and
  this permission notice shall be included in all copies or substantial
  portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT
  WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
  WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
  LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
  CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
  SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE
 
 ---
 
MIT License
(antlr4-c3 1.1.12, Visual Studio Code 1.65.0)

The MIT License
===============

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
 
 ---
 
 MIT License
 (Visual Studio Code 1.41.1)
 
 MIT
License



Copyright (c) 2015 - present Microsoft Corporation



All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to
deal
 in the Software without restriction, including without limitation the
rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or
sell
 copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
 
 The above
copyright notice and this permission notice shall be included in all
 copies
or substantial portions of the Software.
 
 THE SOFTWARE IS PROVIDED "AS
IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT
LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT
HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE
 
 ---
 
Rocket Proprietary License
 (UDJrb 0.0.3)
 
 ROCKET SOFTWARE, INC.  END
USER LICENSE AGREEMENT (EULA)

End User agrees Rocket Software proprietary software products (“Products”) are
licensed to End User according to these Software License Terms (“Terms”)
between End User and the party accepting End User’s order for the Products
(“Licensor”), and govern End User’s rights and obligations in using the
Products. 

1. LICENSE
a. Definitions: Concurrent User means a User authorized to use a Product
concurrently with other Concurrent Users, limited by a specified maximum
number of Concurrent Users at any one time. Documentation means Product user
manuals and training materials provided to End User electronically or
physically. License means End User’s non-exclusive, non-transferable right to
use the object code of licensed Product, and related Documentation, for its
own internal business operations within the country in which the Product is
first installed (“Territory”), according to the Documentation, and subject to
license restrictions. End User means a legal entity executing a written order
document with Licensor. Measured License means a License for the time period,
number and type of Users, transactions, copies, seats, instructions per second
(MIPS) or other quantity or measure specified in writing by Licensor for each
Product. Named User means an individual identified User authorized to use a
Product. A new Named User may replace a prior Named User. User means a Named
User or Concurrent User authorized by End User to use a Product for End User’s
internal business purposes. 
b. Grant. Products are copyrighted by Licensor and are licensed, not sold.
Licensor grants to End User a Measured License to use a Product listed in an
order document executed by End User and Licensor, and related Documentation.
End User may make one copy of the Product for non-production backup purposes.
End User’s right to use Product as an Enterprise License or other type of
Measured License greater than a single use is specified in the order document.
End User’s actual use will not decrease (but may increase) the scope of the
License granted and the License fees due.  c. Restrictions. End User will not:
i. rent, lease, or sublicense Product, or use Product for service bureau,
hosting, or as an application service provider; ii. permit any third party to
access or use Product or Documentation, except for End User’s contractor using
Product for End User’s internal business operations, provided End User ensures
its contractor complies with the Terms, is not a competitor of Licensor or its
licensors, and End User is liable for their acts and omissions; iii. transfer
or use Product outside the Territory; iv. use Product except as specified in
the Documentation; v. translate, modify or make derivative works of Product or
Documentation; vi. reverse engineer, decompile or disassemble a Product,
except as permitted by law after End User has disclosed its intended
activities in writing to Licensor; vii. exceed the time period for the
License, or use a Product in excess of the Measured License which End User
purchased; viii. use Product in production if provided under a non-production
License; ix. alter Product’s copyright or other intellectual property rights
notices; x. infringe or misappropriate Licensor’s or its licensors’
Intellectual Property or Licenses.
d. Verification. End User will keep accurate records measuring its use of
Product according to its License. Licensor has the right to audit End User’s
records and operations to verify End User’s Product use according to the
License and the Terms. Audits will be scheduled at a mutually agreed date and
time during End User’s usual business hours. If Licensor determines that End
User’s Product use exceeds the License that End User purchased, End User will
immediately pay Licensor the current Fees for the additional Product use and
reimburse Licensor’s reasonable expenses to perform the audit. The limitation
of liability set forth in Section 4 does not apply to End User’s Product use
exceeding its License or misappropriation of Product.  e. Termination.
Licensor may terminate a License by written notice: i. immediately, if End
User infringes or misappropriates Licensor’s or its licensors’ Intellectual
Property or Licenses, or fails to comply with the License Terms; ii. as
specified in the Warranty Terms; and iii. if End User materially breaches the
Agreement, subject to any specified cure period. Upon termination End User
will immediately return or destroy all Product and, upon Licensor’s request,
provide written certification of such destruction. 

2. INTELLECTUAL PROPERTY OWNERSHIP Intellectual Property means all
intellectual property, including without limitation, inventions, patents,
copyrights, trademarks, service marks, trade names, trade secrets, know-how,
moral rights, licenses, and any other intangible proprietary or property
rights, registered or not, under statute and/or common law. Licensor (or its
licensors) own and retain licenses and all right, title and interest in all
Intellectual Property in Products and Documentation, and related developments,
data, designs, formulae, documents, drawings, plans, specifications and other
Product information, proprietary materials and all derivative works. To the
extent that any right , title or interest in or to any Product Intellectual
Property may not automatically vest in Licensor (or its licensors) by
operation of law, End User irrevocably transfers, assigns and conveys all
right, title, and interest therein to Licensor (or its licensors). At
Licensor’s request and expense End User will promptly take any action and
execute any documents necessary to vest full title in Licensor (or its
licensor). 

3. CONFIDENTIALITY Confidential Information means any material, data, or
information, in any form or media, that is proprietary or confidential to
Licensor or its licensors and is marked as confidential, or not marked but by
its nature or treatment should reasonably be considered to be confidential.
Whether or not disclosed orally or marked as confidential, Confidential
Information includes the Terms; Products, specifications, Documentation and
results of benchmark tests; Licensor’s or it’s licensors’ non-public data,
information, and Intellectual Property. Confidential Information does not
include information that is i. publicly available without breach of the Terms;
ii. reasonably shown to have been known by End User prior to disclosure or
independently developed by End User subsequent to disclosure without breach of
these Terms; or iii. obtained by End User from a third party without
confidentiality obligation. Products are not deemed to be placed in the public
domain. End User will promptly notify Licensor if it is compelled by a court
or legal process to disclose Confidential Information and will take any
reasonable action requested by Licensor to maintain the confidentiality of the
Confidential Information. End User will use Confidential Information solely to
perform its obligations under the Agreement. End User will take commercially
reasonable steps to safeguard Confidential Information, including no less than
the steps taken to protect its own Confidential Information. End User will not
disclose Confidential Information except to its employees or contractors bound
by written confidentiality obligations no less restrictive than these Terms.
End User will promptly notify Licensor in writing of unauthorized use or
disclosure of Confidential Information. End User, at its expense, will take
all reasonable action to recover Confidential Information and prevent further
unauthorized use or disclosure, including action for seizure and injunctive
relief. If End User fails to do so in a timely manner, Licensor or its
licensors may take reasonable action to do so at End User's expense, and End
User will reasonably cooperate.

4. LIMITATION OF LIABILITY     a. LICENSOR (OR ITS LICENSOR) WILL NOT BE
LIABLE FOR i. ANY CONSEQUENTIAL, INDIRECT, SPECIAL, PUNITIVE OR INCIDENTAL
DAMAGES, ii. ANY INTERRUPTION OF BUSINESS OR OPERATIONS, COST OF COVER,
GOODWILL, TOLL FRAUD, OR LOSS OF DATA, PROFITS, OR REVENUE, OR FAILURE OF A
REMEDY TO ACHIEVE ITS ESSENTIAL PURPOSE. 
b. EXCEPT FOR DEATH OR BODILY INJURY CLAIMS, TANGIBLE PROPERTY DAMAGE, WILLFUL
MISCONDUCT OR FRAUD, LICENSOR (OR ITS LICENSOR) WILL NOT BE LIABLE FOR ANY
DAMAGES THAT EXCEED THE AMOUNT END USER PAID TO LICENSOR IN THE PRECEDING 12
MONTHS FOR THE PRODUCT THAT GAVE RISE TO THE CLAIM. 
c. THE LIMITATIONS IN THIS SECTION 4 WILL APPLY TO ANY DAMAGES, HOWEVER
CAUSED, UNDER ANY THEORY OF LIABLITY, WHETHER FOR BREACH OF CONTRACT, TORT,
MISREPRESENTATION, NEGLIGENCE, THE USE OR PERFORMANCE OF A PRODUCT OR SERVICE,
OR OTHERWISE, AND REGARDLESS OF WHETHER THE DAMAGES WERE FORESEEABLE OR
UNFORSEEABLE. LICENSOR (OR ITS LICENSOR) WILL NOT BE LIABLE FOR ANY CLAIM
BROUGHT MORE THAN 12 MONTHS AFTER END USER BECAME AWARE OF THE ISSUE GIVING
RISE TO THE CLAIM. LICENSOR’S FAILURE TO EXERCISE A RIGHT OR REMEDY IS NOT A
WAIVER.
d. TO THE EXTENT PERMITTED UNDER LAW, THE LICENSOR’S (AND ITS LICENSORS’)
LIABILITY FOR THE BREACH OF ANY IMPLIED CONDITION, GUARANTEE, REPRESENTATION
OR WARRANTY WHICH CANNOT BE EXCLUDED IS (IF ANY LEGISLATION SO PERMITS)
LIMITED TO THE MAXIMUM EXTENT SUCH CONDITION, GUARANTEE, REPRESENTATION OR
WARRANTY CAN BE LIMITED UNDER APPLICABLE LAW.

5. INJUNCTIVE RELIEF  End User’s failure to comply with the License or
Confidentiality Terms would result in irreparable harm to Licensor or its
licensors that may be intangible but real and is incapable of complete remedy
by award of damages. End User agrees: a. Licensor or its licensors has the
right to an injunction or other court order and may proceed directly to court
to specifically enforce End User’s obligations; b. no finding of irreparable
harm or other condition to injunctive relief is required; c. an order may be
entered to compel End User to act or refrain from acting or threatened action;
and d. if an injunction is granted, End User will pay Licensor’s or its
licensors’ reasonable expenses and attorney fees, in addition to any other
relief granted. Licensor or its licensors has the right to pursue all remedies
at law and in equity for such a breach.

6. MISCELLANEOUS	 a. License Terms. The License Terms supersede all
other oral or written terms, proposals or representations regarding Product
Licenses. The Terms may only be modified by a written amendment signed by
Licensor and End User. If any Term is illegal, invalid, or unenforceable, the
other Terms remain in full force and effect and any Term that is intended to
survive termination will survive.  b. Assignment. End User may not assign any
of its obligations, rights or remedies, in whole or in part.  c. Governing
Law. The laws of Commonwealth of Massachusetts govern the Product License,
excluding conflict of law principles that would apply the law of any other
jurisdiction. The United Nations Convention on Contracts for the International
Sale of Goods and the Uniform Computer Information Transaction Act, as
adopted, do not apply. Each party irrevocably and unconditionally submits to
the exclusive jurisdiction of the courts of Commonwealth of Massachusetts.  d.
Compliance with Laws; Export. End User will comply with U.S., foreign, and
international laws and regulations, including without limitation the U.S.
Foreign Corrupt Practices Act, other anti-bribery or anti-corruption laws,
U.S. Export Administration and Treasury Department's Office of Foreign Assets
Control regulations, and other anti-boycott and import regulations.  End User
agrees: i. that the export, re-export, transfer, re-transfer, sale, supply,
access to, or use of Product or Documentation to or in a country other than
the country in which the Product or Documentation was first provided to End
User, or to, by, or for a different end user or end use, may require a U.S. or
other government license or other authorization; and ii. not to, directly or
indirectly, export, re-export, transfer, re-transfer, sell, supply, or allow
access to or use of Product or Documentation to, in, by, or for sanctioned,
embargoed, or prohibited countries, persons, or end uses under U.S. or other
applicable law (collectively, “Prohibited Uses“). End User is responsible for
screening for Prohibited Uses and obtaining any required licenses or other
authorizations and shall indemnify Licensor and its licensors for any
violation by End User of any  Export Controls and/or economic sanctions laws
and regulations. Licensor may terminate the License immediately if Licensor
determines, in its sole discretion, that End User has breached, intends to
breach, or insists upon breaching any of the provisions in this clause.


 
 ---
 

