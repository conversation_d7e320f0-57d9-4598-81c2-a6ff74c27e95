{"version": 3, "file": "onlineBuilder.js", "sourceRoot": "", "sources": ["../../../src/online/compile/onlineBuilder.ts"], "names": [], "mappings": ";;;AAAA,iCAAgC;AAChC,mDAAkE;AAElE,uDAA2D;AAC3D,wDAAwD;AAExD,mCAAgC;AAChC,+CAA+C;AAE/C,gDAAgD;AAEhD,MAAa,aAAc,SAAQ,iBAAO;IAGtC,YAAoB,WAAiC;QACjD,KAAK,EAAE,CAAC;QADQ,gBAAW,GAAX,WAAW,CAAsB;QAEjD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,wCAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACtF,CAAC;IAES,OAAO,CAAC,IAAuB,EAAE,KAAe,EAAE,MAAe;QACvE,IAAI,UAAU,GAAG,CAAC,IAAI,KAAK,iBAAO,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAC,iBAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACrH,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAEM,OAAO,CAAC,UAAoC;QAC/C,IAAI,OAAO,GAAG,kBAAM,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC9F,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAG,CAAC,mBAAmB,CAAC,CAAC;YACxD,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAG,KAAK,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAG,CAAC,kBAAkB,CAAC,CAAA;YACtD,OAAO,SAAS,CAAC;QACrB,CAAC;aAAI,CAAC;YACF,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAEO,QAAQ,CAAC,WAAqB;QAClC,MAAM,UAAU,GAAG,kBAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,IAAG,UAAU,KAAK,SAAS,EAAC,CAAC;YACzB,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;CACJ;AA9CD,sCA8CC;AAGD,SAAS,aAAa,CAAC,MAAc;IACjC,IAAI,UAAU,CAAC;IACf,MAAM,MAAM,GAAG,EAAE,CAAC,oBAAoB,EAAE,CAAC;IACzC,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,CAAC;QAClC,UAAU,GAAG;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAE;YACX,OAAO,EAAE;gBACL,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,MAAM,CAAC,iBAAiB;aACtC;SACJ,CAAC;IACN,CAAC;SAAM,CAAC;QACJ,UAAU,GAAG;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAE;YACX,OAAO,EAAE;gBACL,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,MAAM,CAAC,iBAAiB;gBAClC,SAAS,EAAE,MAAM,CAAC,iBAAiB;aACtC;SACJ,CAAC;IACN,CAAC;IAED,OAAO,UAAU,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAc,EAAE,IAAc;IACxD,IAAI,UAAU,CAAC;IACf,MAAM,MAAM,GAAG,IAAI,KAAK,iBAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpE,MAAM,MAAM,GAAG,MAAM,CAAC,iBAAiB,IAAI,UAAU,CAAC;IACtD,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,CAAC;QAClC,UAAU,GAAG;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAE;YACX,OAAO,EAAE;gBACL,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC7B,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,EAAE;gBAC/C,SAAS,EAAE,MAAM,CAAC,iBAAiB,IAAI,EAAE;aAC5C;SACJ,CAAC;IACN,CAAC;SAAM,CAAC;QACJ,UAAU,GAAG;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAE;YACX,OAAO,EAAE;gBACL,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC7B,SAAS,EAAE,MAAM,CAAC,iBAAiB,IAAI,EAAE;aAC5C;SACJ,CAAC;IACN,CAAC;IAED,OAAO,UAAU,CAAC;AAEtB,CAAC"}