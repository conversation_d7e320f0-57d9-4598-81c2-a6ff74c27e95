"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.U2Account = exports.U2Server = exports.U2File = exports.U2Dir = exports.U2Item = exports.U2ItemProvider = void 0;
const vscode = require("vscode");
const path = require("path");
const fsprovider_1 = require("./fsprovider");
const env_1 = require("../../common/env");
const mvClient = require("../../mvClient/client");
const vscode_languageclient_1 = require("vscode-languageclient");
const driver = require("../../driver");
const olc = require("../../config/onlineConfig");
const extension_1 = require("../../extension");
class U2ItemProvider {
    constructor(_fsProvider, _treeData) {
        this._fsProvider = _fsProvider;
        this._treeData = _treeData;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    refresh() {
        this._onDidChangeTreeData.fire(undefined);
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (element) {
            return this.getItemsInSubDir(element.uri);
        }
        else {
            return this.getItemsInSubDir(vscode.Uri.parse(env_1.FS_SCHEMA + ':/'));
        }
    }
    getItemsInSubDir(uri) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            let uvItems = new Array();
            const entry = this._fsProvider.lookup(uri);
            // MVVS-1329 use path.sep to get the separator for the current workspace directory
            if (uri.fsPath === path.sep) {
                const servers = olc.getServers();
                for (const server of servers) {
                    this._fsProvider.clear();
                    yield this.addServer(uri, server);
                    this._push(uri, server, uvItems);
                }
            }
            else if (entry instanceof fsprovider_1.Account) {
                if (entry.entries.size === 0) {
                    // If server not connected, then return directly.
                    if (mvClient.isConnected()) {
                        // Logon to account
                        (_a = mvClient.getAccount()) === null || _a === void 0 ? void 0 : _a.setName(entry.name);
                        yield this._add(uri, entry, entry.name);
                        entry.entries.forEach(e => {
                            this._push(uri, e, uvItems);
                        });
                    }
                }
            }
            else if (entry instanceof fsprovider_1.Directory) {
                if (entry.entries.size === 0) {
                    // If server not connected, then return directly.
                    const account = mvClient.getAccount();
                    if (account) {
                        if (entry instanceof fsprovider_1.Server) {
                            if (entry.address === account.address) {
                                const accountName = account.getName();
                                const ret = yield mvClient.logon(accountName);
                                if (ret == "") {
                                    if (mvClient.isConnected()) {
                                        yield this._add(uri, entry, accountName);
                                    }
                                }
                            }
                        }
                        else {
                            // Account in mvClient maybe not the current account, so we need to logon to current account first.
                            // Logon to account in uri, then get files list
                            const parts = uri.fsPath.split("\\");
                            if (parts.length < 3) {
                                return [];
                            }
                            const accountName = parts[parts.length - 2];
                            if (accountName !== account.getName()) {
                                yield mvClient.logon(accountName);
                                mvClient.setAccountName(accountName);
                            }
                            if (mvClient.isConnected()) {
                                yield this._add(uri, entry, accountName);
                            }
                        }
                    }
                }
                entry.entries.forEach(e => {
                    this._push(uri, e, uvItems);
                });
            }
            else if (entry instanceof fsprovider_1.File) {
                this._push(uri, entry, uvItems);
            }
            return uvItems;
        });
    }
    _push(uri, entry, u2items) {
        if (entry instanceof fsprovider_1.Server) {
            const fileUri = vscode.Uri.joinPath(uri, entry.address);
            let server = new U2Server(entry.name, entry.address, fileUri);
            // update server icon based on current connection status
            const connectedServerLabel = extension_1.online.getConnectedServerLabel();
            if (mvClient.isConnected() && connectedServerLabel !== undefined && connectedServerLabel === server.label) {
                server.iconPath = new vscode.ThemeIcon("pass-filled");
            }
            u2items.push(server);
        }
        else if (entry instanceof fsprovider_1.Account) {
            const accountUri = vscode.Uri.joinPath(uri, entry.name);
            u2items.push(new U2Account(entry.name, accountUri));
        }
        else if (entry instanceof fsprovider_1.File) {
            const fileUri = vscode.Uri.joinPath(uri, entry.name);
            u2items.push(new U2File(entry.name, fileUri));
        }
        else if (entry instanceof fsprovider_1.Directory) {
            const fileUri = vscode.Uri.joinPath(uri, entry.name);
            u2items.push(new U2Dir(entry.name, fileUri));
        }
    }
    _add(uri, entry, accountName) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            var serverLabel = extension_1.online.getConnectedServerLabel();
            if (serverLabel === undefined)
                return;
            var pattern = olc.getFilterPatternFor(uri.path, serverLabel);
            if (entry instanceof fsprovider_1.Server) {
                yield this._loadAccounts(uri, pattern);
            }
            else if (((_a = mvClient.getAccount()) === null || _a === void 0 ? void 0 : _a.getName()) === entry.name) {
                yield this._loadDirs(uri, accountName, pattern);
            }
            else {
                yield this._loadBasicFiles(uri, accountName, pattern);
            }
        });
    }
    addServer(uri, entry) {
        return __awaiter(this, void 0, void 0, function* () {
            this._createRootDir(entry);
        });
    }
    _createRootDir(entry) {
        const rootPath = env_1.FS_SCHEMA + ':/' + entry.address;
        const rootUri = vscode.Uri.parse(rootPath);
        this._fsProvider.createRootDirectory(rootUri, entry);
        const command = vscode_languageclient_1.Command.create("setOnlineRootPath", "setOnlineRootPath", rootPath);
        driver.send(command);
    }
    _loadDirs(uri, accountName, filterPattern) {
        return __awaiter(this, void 0, void 0, function* () {
            const command = vscode_languageclient_1.Command.create("getDirs", "getDirs", accountName, uri.path, filterPattern);
            yield driver.send(command).then(dirs => {
                if (dirs instanceof Array) {
                    dirs.forEach((dir) => {
                        const dirUri = vscode.Uri.joinPath(uri, dir);
                        this._fsProvider.createDirectory(dirUri);
                    });
                }
                else {
                    let options = { modal: true };
                    vscode.window.showWarningMessage("Failed to load directories in account \'" + accountName + "\', please refresh the explorer and try again.", options);
                }
            });
        });
    }
    _loadAccounts(uri, accountFilterPattern) {
        return __awaiter(this, void 0, void 0, function* () {
            let accounts = yield mvClient.getAllAccounts(accountFilterPattern);
            if (accounts && accounts.length > 0) {
                // Put all accounts information to current server node
                for (const accountName of accounts) {
                    // create uri
                    let accountUri = vscode.Uri.joinPath(uri, accountName);
                    this._fsProvider.addAccount(accountUri);
                }
            }
        });
    }
    _loadBasicFiles(uri, accountName, filterPattern) {
        return __awaiter(this, void 0, void 0, function* () {
            let dir = path.posix.basename(uri.path);
            const command = vscode_languageclient_1.Command.create("getFileList", "getFileList", accountName, dir, filterPattern);
            yield driver.send(command).then(programs => {
                programs.forEach((program) => {
                    const programUri = vscode.Uri.joinPath(uri, program);
                    this._fsProvider.writeFile(programUri, Buffer.from(""), { create: true, overwrite: true });
                    // await this._loadFileContent(uri, dir, program);
                });
            });
        });
    }
}
exports.U2ItemProvider = U2ItemProvider;
class U2Item extends vscode.TreeItem {
    constructor(label, uri, collapsibleState, command) {
        super(label, collapsibleState);
        this.label = label;
        this.uri = uri;
        this.collapsibleState = collapsibleState;
        this.command = command;
        this.tooltip = `${this.uri.fsPath}`;
        this.description = '';
        this.contextValue = 'U2Item';
    }
}
exports.U2Item = U2Item;
class U2Dir extends U2Item {
    constructor(label, uri) {
        super(label, uri, vscode.TreeItemCollapsibleState.Collapsed);
        this.label = label;
        this.uri = uri;
        this.contextValue = 'U2Dir';
    }
}
exports.U2Dir = U2Dir;
class U2File extends U2Item {
    constructor(label, uri) {
        super(label, uri, vscode.TreeItemCollapsibleState.None, {
            command: 'extension.openU2File',
            title: '',
            arguments: [uri]
        });
        this.label = label;
        this.uri = uri;
        this.contextValue = 'U2File';
    }
}
exports.U2File = U2File;
class U2Server extends U2Item {
    constructor(label, address, uri) {
        super(label, uri, vscode.TreeItemCollapsibleState.Collapsed);
        this.label = label;
        this.address = address;
        this.uri = uri;
        this.iconPath = new vscode.ThemeIcon("database");
        this.contextValue = "U2Server";
        this.tooltip = `${this.address}`;
    }
    connecting() {
        this.description = 'Connecting...';
    }
    connected() {
        this.description = 'Connected';
    }
    disconnected() {
        this.description = '';
    }
}
exports.U2Server = U2Server;
class U2Account extends U2Item {
    constructor(label, uri) {
        super(label, uri, vscode.TreeItemCollapsibleState.Collapsed, {
            command: 'extension.logon',
            title: '',
            arguments: [uri]
        });
        this.label = label;
        this.uri = uri;
        this.contextValue = "U2Account";
        this.description = '';
    }
}
exports.U2Account = U2Account;
//# sourceMappingURL=u2tree.js.map