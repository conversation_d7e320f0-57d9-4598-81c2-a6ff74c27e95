"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogConfig = exports.JSON_SUFFIX = exports.YML_SUFFIX = exports.XML_SUFFIX = exports.PROPERTIES_SUFFIX = void 0;
const editor_1 = require("../common/editor");
const fs = require("fs");
const path = require("path");
const extension_1 = require("../extension");
exports.PROPERTIES_SUFFIX = "log4j2.properties";
exports.XML_SUFFIX = "log4j2.xml";
exports.YML_SUFFIX = "log4j2.yml";
exports.JSON_SUFFIX = "log4j2.json";
class LogConfig {
    constructor(cacheType) {
        this.logConfigFile = this.copyLog4j2Config(cacheType);
    }
    getLaunchParameter() {
        return "-Dlog4j2.configurationFile=file:\\\\\\" + this.logConfigFile;
    }
    copyLog4j2Config(cacheType) {
        const propertiesFile = this.getLogConfigFile(exports.PROPERTIES_SUFFIX);
        const xmlFile = this.getLogConfigFile(exports.XML_SUFFIX);
        const ymlFile = this.getLogConfigFile(exports.YML_SUFFIX);
        const jsonFile = this.getLogConfigFile(exports.JSON_SUFFIX);
        if (fs.existsSync(propertiesFile)) {
            return propertiesFile;
        }
        if (fs.existsSync(xmlFile)) {
            return xmlFile;
        }
        if (fs.existsSync(ymlFile)) {
            return ymlFile;
        }
        if (fs.existsSync(jsonFile)) {
            return jsonFile;
        }
        const sourcePath = path.join(extension_1.extensionPath, "config", exports.PROPERTIES_SUFFIX);
        const content = fs.readFileSync(sourcePath, 'utf-8');
        const newContent = content.replace('${LOG_HOME}', cacheType);
        fs.writeFileSync(propertiesFile, newContent);
        return propertiesFile;
    }
    getLogConfigFile(log4j2configFileName) {
        const rmvPath = (0, editor_1.getWorkspacePath)();
        if (rmvPath !== undefined) {
            return path.join(rmvPath, "config", log4j2configFileName).replace(/\\/g, "/");
        }
        return "Not exsit";
    }
}
exports.LogConfig = LogConfig;
//# sourceMappingURL=logConfig.js.map