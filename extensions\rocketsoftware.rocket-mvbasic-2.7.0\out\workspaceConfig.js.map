{"version": 3, "file": "workspaceConfig.js", "sourceRoot": "", "sources": ["../src/workspaceConfig.ts"], "names": [], "mappings": ";;AAQA,oDAEC;AAOD,wCA+BC;AAMD,4DA0BC;AAMD,kDAoBC;AA1GD,iCAAiC;AACjC,sCAAsD;AACtD,oDAAoD;AACpD;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,kBAA+B;IAChE,cAAc,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;AAC9C,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,MAAe,EAAE,kBAA+B;;IAC3E,kCAAkC;IAClC,IAAI,SAAS,GAAG,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,MAAA,MAAM,CAAC,MAAM,CAAC,gBAAgB,0CAAE,QAAQ,CAAC,GAAG,CAAC;IACnF,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,eAAS,EAAE,CAAC;QAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAClD,SAAS,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,CAAC,CAAC,0CAAE,GAAG,CAAC;IAClC,CAAC;IAED,wEAAwE;IACxE,IAAI,SAAS,EAAE,CAAC;QACZ,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,aAAa,EAAE,CAAC;YAChB,MAAM,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,GAAG,iBAAW,GAAG,GAAG,EAAE,aAAa,CAAC,CAAC;YACtG,MAAM,aAAa,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YAE7D,2DAA2D;YAC3D,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;gBAC3B,MAAM,cAAc,GAAG,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,aAAa,CAAC;qBAC/C,MAAM,CAAC,GAAG,GAAG,iBAAW,GAAG,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC;qBACjG,IAAI,CACD,GAAG,EAAE;oBACD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,QAAQ,iBAAW,EAAE,CAAC,CAAC;gBACpH,CAAC,EACD,CAAC,GAAG,EAAE,EAAE;oBACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,GAAG,GAAG,CAAC,CAAC;gBAChF,CAAC,CACJ,CAAC;YACV,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC;AAGD;;GAEG;AACH,SAAgB,wBAAwB,CAAC,SAAiB;IACtD,MAAM,yBAAyB,GAAG,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC;IACnE,MAAM,QAAQ,GAAG,KAAK,GAAG,SAAS,GAAG,gBAAgB,CAAC;IACtD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACzD,IAAI,YAAY,GAA8B,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;IAC9E,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,sHAAsH;IACtH,mDAAmD;IACnD,IAAI,YAAY,CAAC,yBAAyB,CAAC,IAAI,SAAS,EAAE,CAAC;QACvD,MAAM,OAAO,GAA+B,EAAE,CAAC;QAC/C,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC7B,4BAA4B;YAC5B,IAAI,GAAG,KAAK,yBAAyB,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;QACD,YAAY,GAAG,OAAO,CAAC;QACvB,QAAQ,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,SAAS,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;QAC5E,YAAY,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;QACjC,QAAQ,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACnB,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC3F,CAAC;AACL,CAAC;AAGD;;GAEG;AACH,SAAgB,mBAAmB,CAAC,SAAiB;IACjD,MAAM,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC;IACzD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACzD,MAAM,OAAO,GAAgC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACxE,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS,EAAE,CAAC;QACvC,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;QAC/B,QAAQ,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,EAAE,CAAC;QAC/B,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;QAC5B,OAAO,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACxB,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,QAAQ,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACnB,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACjF,CAAC;AACL,CAAC"}