{"$schema": "http://json-schema.org/draft-07/schema", "definitions": {"_mvFile": {"type": "object", "properties": {"account": {"type": "string", "description": "Name of the account"}, "fileName": {"type": "string", "description": "Name of the file in an account"}}, "dependencies": {"account": ["fileName"]}}}, "type": "object", "properties": {"db": {"type": "object", "description": "Propeties to connect multi-value database server.", "properties": {"host": {"type": "string", "description": "Address/Host name of multi-value database server."}, "userName": {"type": "string", "description": "User name to login to database server."}, "password": {"type": "string", "description": "If password value is empty, the client will ask it while connecting to the server."}, "account": {"type": "string", "description": "Account path."}, "dataSource": {"type": "string", "description": "UNIVERSE or UNIDATA.", "oneOf": [{"const": "UNIVERSE"}, {"const": "UNIDATA"}]}, "port": {"type": "integer", "description": "Database server connection port number."}, "rpcName": {"type": "string", "description": "The name of a custom Remote Procedure Call (RPC) service"}}, "required": ["host", "userName", "password", "account", "dataSource"]}, "catalog": {"type": "object", "description": "Configurations for catalog programs", "properties": {"isSearchAllDirs": {"type": "boolean", "description": "Whether to scan all the files under workspace or only the files under the current open subpath as catalog, default is no (only the files under the current subpath are scanned as catalog)", "default": false}, "readServerInterval": {"type": "integer", "description": "Time interval for scanning the catalog of MV server, in seconds, default 5", "minimum": 1, "default": 5}, "programDirs": {"type": "array", "description": "Configure which paths can become scan paths for catalog", "items": {"$ref": "#/definitions/_mvFile"}}, "programMapping": {"type": "array", "description": "Configure the mapping relationship between catalog and program", "items": {"allOf": [{"type": "object", "properties": {"catalogName": {"type": "string", "description": "Name of catalog to mapper in basic file"}, "program": {"type": "string", "description": "Name of the source file in the dir-type file"}}, "required": ["catalogName", "program"]}, {"$ref": "#/definitions/_mvFile"}]}}}}, "includeMapping": {"type": "array", "description": "Configurations for include programs", "items": {"allOf": [{"$ref": "#/definitions/_mvFile", "required": ["account", "fileName"]}, {"type": "object", "properties": {"includeFile": {"type": "string", "description": "Set the include file to be mapped."}}, "required": ["includeFile"]}]}}, "accounts": {"type": "array", "description": "Configure local mapping account", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the account"}, "path": {"type": "string", "description": "Local path of the account"}}, "required": ["name", "path"]}}}}