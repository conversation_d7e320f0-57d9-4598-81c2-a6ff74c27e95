<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="vscode-speech-language-pack-zh-tw" Version="0.5.0" Publisher="ms-vscode" />
			<DisplayName>Chinese (Traditional, Taiwan) language support for VS Code Speech</DisplayName>
			<Description xml:space="preserve">Chinese (Traditional, Taiwan) language support for speech-to-text and other voice capabilities in VS Code.</Description>
			<Tags>Accessibility,a11y,STT,ai,co-pilot,Chat,Voice,Transcription,Microsoft,multi-root ready,__web_extension</Tags>
			<Categories>Other</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.87.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="ms-vscode.vscode-speech" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="ui,web" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="" />
				
				
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/Microsoft/vscode-speech-assets.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/Microsoft/vscode-speech-assets.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/Microsoft/vscode-speech-assets.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/Microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Aworkbench-voice" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/microsoft/vscode/wiki/VS-Code-Speech" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				
				
			</Properties>
			<License>extension/LICENSE.txt</License>
			<Icon>extension/logo.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/readme.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/logo.png" Addressable="true" />
		</Assets>
	</PackageManifest>