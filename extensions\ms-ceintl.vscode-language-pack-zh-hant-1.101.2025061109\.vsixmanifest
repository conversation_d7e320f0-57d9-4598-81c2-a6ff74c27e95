<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="vscode-language-pack-zh-hant" Version="1.101.2025061109" Publisher="MS-CEINTL" />
			<DisplayName>Chinese (Traditional) Language Pack for Visual Studio Code</DisplayName>
			<Description xml:space="preserve">Language pack extension for Chinese (Traditional)</Description>
			<Tags>中文(繁體),lp-zh-tw,__lp_vscode,__lp-zh-tw_vscode,__lp_ms-vscode.js-debug,__lp-zh-tw_ms-vscode.js-debug,__lp_vscode.bat,__lp-zh-tw_vscode.bat,__lp_vscode.builtin-notebook-renderers,__lp-zh-tw_vscode.builtin-notebook-renderers,__lp_vscode.clojure,__lp-zh-tw_vscode.clojure,__lp_vscode.coffeescript,__lp-zh-tw_vscode.coffeescript,__lp_vscode.configuration-editing,__lp-zh-tw_vscode.configuration-editing,__lp_vscode.cpp,__lp-zh-tw_vscode.cpp,__lp_vscode.csharp,__lp-zh-tw_vscode.csharp,__lp_vscode.css-language-features,__lp-zh-tw_vscode.css-language-features,__lp_vscode.css,__lp-zh-tw_vscode.css,__lp_vscode.dart,__lp-zh-tw_vscode.dart,__lp_vscode.debug-auto-launch,__lp-zh-tw_vscode.debug-auto-launch,__lp_vscode.debug-server-ready,__lp-zh-tw_vscode.debug-server-ready,__lp_vscode.diff,__lp-zh-tw_vscode.diff,__lp_vscode.docker,__lp-zh-tw_vscode.docker,__lp_vscode.emmet,__lp-zh-tw_vscode.emmet,__lp_vscode.extension-editing,__lp-zh-tw_vscode.extension-editing,__lp_vscode.fsharp,__lp-zh-tw_vscode.fsharp,__lp_vscode.git-base,__lp-zh-tw_vscode.git-base,__lp_vscode.git,__lp-zh-tw_vscode.git,__lp_vscode.github-authentication,__lp-zh-tw_vscode.github-authentication,__lp_vscode.github,__lp-zh-tw_vscode.github,__lp_vscode.go,__lp-zh-tw_vscode.go,__lp_vscode.groovy,__lp-zh-tw_vscode.groovy,__lp_vscode.grunt,__lp-zh-tw_vscode.grunt,__lp_vscode.gulp,__lp-zh-tw_vscode.gulp,__lp_vscode.handlebars,__lp-zh-tw_vscode.handlebars,__lp_vscode.hlsl,__lp-zh-tw_vscode.hlsl,__lp_vscode.html-language-features,__lp-zh-tw_vscode.html-language-features,__lp_vscode.html,__lp-zh-tw_vscode.html,__lp_vscode.ini,__lp-zh-tw_vscode.ini,__lp_vscode.ipynb,__lp-zh-tw_vscode.ipynb,__lp_vscode.jake,__lp-zh-tw_vscode.jake,__lp_vscode.java,__lp-zh-tw_vscode.java,__lp_vscode.javascript,__lp-zh-tw_vscode.javascript,__lp_vscode.json-language-features,__lp-zh-tw_vscode.json-language-features,__lp_vscode.json,__lp-zh-tw_vscode.json,__lp_vscode.julia,__lp-zh-tw_vscode.julia,__lp_vscode.latex,__lp-zh-tw_vscode.latex,__lp_vscode.less,__lp-zh-tw_vscode.less,__lp_vscode.log,__lp-zh-tw_vscode.log,__lp_vscode.lua,__lp-zh-tw_vscode.lua,__lp_vscode.make,__lp-zh-tw_vscode.make,__lp_vscode.markdown-language-features,__lp-zh-tw_vscode.markdown-language-features,__lp_vscode.markdown-math,__lp-zh-tw_vscode.markdown-math,__lp_vscode.markdown,__lp-zh-tw_vscode.markdown,__lp_vscode.media-preview,__lp-zh-tw_vscode.media-preview,__lp_vscode.merge-conflict,__lp-zh-tw_vscode.merge-conflict,__lp_vscode.microsoft-authentication,__lp-zh-tw_vscode.microsoft-authentication,__lp_vscode.npm,__lp-zh-tw_vscode.npm,__lp_vscode.objective-c,__lp-zh-tw_vscode.objective-c,__lp_vscode.perl,__lp-zh-tw_vscode.perl,__lp_vscode.php-language-features,__lp-zh-tw_vscode.php-language-features,__lp_vscode.php,__lp-zh-tw_vscode.php,__lp_vscode.powershell,__lp-zh-tw_vscode.powershell,__lp_vscode.prompt,__lp-zh-tw_vscode.prompt,__lp_vscode.pug,__lp-zh-tw_vscode.pug,__lp_vscode.python,__lp-zh-tw_vscode.python,__lp_vscode.r,__lp-zh-tw_vscode.r,__lp_vscode.razor,__lp-zh-tw_vscode.razor,__lp_vscode.references-view,__lp-zh-tw_vscode.references-view,__lp_vscode.restructuredtext,__lp-zh-tw_vscode.restructuredtext,__lp_vscode.ruby,__lp-zh-tw_vscode.ruby,__lp_vscode.rust,__lp-zh-tw_vscode.rust,__lp_vscode.scss,__lp-zh-tw_vscode.scss,__lp_vscode.search-result,__lp-zh-tw_vscode.search-result,__lp_vscode.shaderlab,__lp-zh-tw_vscode.shaderlab,__lp_vscode.shellscript,__lp-zh-tw_vscode.shellscript,__lp_vscode.simple-browser,__lp-zh-tw_vscode.simple-browser,__lp_vscode.sql,__lp-zh-tw_vscode.sql,__lp_vscode.swift,__lp-zh-tw_vscode.swift,__lp_vscode.terminal-suggest,__lp-zh-tw_vscode.terminal-suggest,__lp_vscode.theme-abyss,__lp-zh-tw_vscode.theme-abyss,__lp_vscode.theme-defaults,__lp-zh-tw_vscode.theme-defaults,__lp_vscode.theme-kimbie-dark,__lp-zh-tw_vscode.theme-kimbie-dark,__lp_vscode.theme-monokai-dimmed,__lp-zh-tw_vscode.theme-monokai-dimmed,__lp_vscode.theme-monokai,__lp-zh-tw_vscode.theme-monokai,__lp_vscode.theme-quietlight,__lp-zh-tw_vscode.theme-quietlight,__lp_vscode.theme-red,__lp-zh-tw_vscode.theme-red,__lp_vscode.theme-solarized-dark,__lp-zh-tw_vscode.theme-solarized-dark,__lp_vscode.theme-solarized-light,__lp-zh-tw_vscode.theme-solarized-light,__lp_vscode.theme-tomorrow-night-blue,__lp-zh-tw_vscode.theme-tomorrow-night-blue,__lp_vscode.tunnel-forwarding,__lp-zh-tw_vscode.tunnel-forwarding,__lp_vscode.typescript-language-features,__lp-zh-tw_vscode.typescript-language-features,__lp_vscode.typescript,__lp-zh-tw_vscode.typescript,__lp_vscode.vb,__lp-zh-tw_vscode.vb,__lp_vscode.vscode-theme-seti,__lp-zh-tw_vscode.vscode-theme-seti,__lp_vscode.xml,__lp-zh-tw_vscode.xml,__lp_vscode.yaml,__lp-zh-tw_vscode.yaml</Tags>
			<Categories>Language Packs</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.101.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="ui,workspace" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="中文(繁體)" />
				<Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="" />
				
				
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/Microsoft/vscode-loc.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/Microsoft/vscode-loc.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/Microsoft/vscode-loc.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/Microsoft/vscode-loc/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/Microsoft/vscode-loc#readme" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				
				
			</Properties>
			<License>extension/LICENSE.md</License>
			<Icon>extension/languagepack.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/readme.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/changelog.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/languagepack.png" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Code.Translation.ZH-TW" Path="extension/translations/main.i18n.json" Addressable="true" />
		</Assets>
	</PackageManifest>