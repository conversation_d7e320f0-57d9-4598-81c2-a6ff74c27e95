spec-type=rnnt1
model-config-path=./model_onnx_quant.config
token-path=./tokens.list
matrix-kind=0
beam-width=7
recombine-kind=0
score-norm-kind=0
beam-sort-kind=1
insertion-boost=NaN
end-padding-dim=0
feature-dim=80
feature-stride=80
ms-per-frame=40
segmentation-mode=2
option-check=false
alignment-offset-frames=2

# word timing
locale-id=1028
lexicon=prongen/lsr1028.lxa
lts=prongen/grph1028.lxa
phn=prongen/l1028.phn
tn=prongen/tn1028.bin
phone-path=ciphone.list

# post processing
locale-path=tw_spellingrules.txt
output-locale=zh-TW
profanity-path=zh-tw_ProfanityList.enc
punctuation-path=zh-tw_explicitPuncRules.txt

# confidence classifier
cc-sentence=classifier_utterance
cc-sent-dim-cnt=5
cc-word=classifier_word
cc-word-dim-cnt=6
# lid 
lid-rewind-frames=0
lid-mode=1
lid-enabled=0
lid-threshold=0.99

# VAD
vad-model-path=svad.quantized.onnx
vad-mode=segmentation
vad-threshold=0.2
vad-initializers-index=0
vad-rewind-frames-count=75
vad-segmentation-threshold=0.1
vad-ms-per-frame=10
license-version=1
