"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Msg = void 0;
class Msg {
}
exports.Msg = Msg;
// Language Server
Msg.LS_START_FAILED = "Failed to start Rocket MV BASIC extension.";
Msg.LS_START_SUCCESS = "Rocket MV BASIC extension started.";
Msg.LS_NOT_START = "Rocket MV BASIC extension has not started. Please activate it first.";
Msg.LS_STOPPED = "Rocket MV BASIC extension has stopped.";
// Extension
Msg.EXT_NO_WORKSPACE = "No folder or workspace opened.\nPlease open a folder or a workspace first.";
Msg.EXT_ACTIVATED_ALREADY = "Rocket MV BASIC extension already activated.";
// MV server
Msg.MV_CONNECT_SUCCESS = "Connection to the U2 server was successful.";
Msg.MV_CONNECT_FAIL = "Connection to the U2 server failed.";
Msg.MV_CONNECT_NO_CONFIG = "Connection to the U2 server failed.\nPlease fill out the configuration file first.";
Msg.MV_DISCONNECT_SUCCESS = "Disconnected from the MV server.";
Msg.MV_DISCONNECT_FAIL = "Disconnection from the MV server failed.";
Msg.MV_SWITCH_DATA_SOURCE = "Rocket MV BASIC extension will be reloaded to change the data source. Continue?";
Msg.MV_NO_WORKSPACE = "Please open an account workspace before using this command.";
Msg.MV_NEED_ADDRESS = "Please input the U2 server address / host name.";
Msg.MV_NEED_ACCOUNT = "Please input the U2 server account name.";
Msg.MV_NEED_USER_NAME = "Please input username.";
Msg.MV_NEED_WORD = "Please input password.";
Msg.MV_NEED_PORT = "Please input port number.";
Msg.MV_NO_ACCOUNT_FOLDER = "Error: please open a U2 account level folder first.";
// Open Document
Msg.CONFIG_NOT_EXISTS = "Configuration file doesn't exist. Please activate Rocket MV BASIC first then try again.";
// JDK
Msg.JAVA_ENV_NOT_SET = "Java 11 or Open JDK 11 is required. Please setup the Java 11, or Open JDK 11 environment first, or specify the JDK 11 directory in settings.";
Msg.JAVA_UNEXPECT = "Java 11 or Open JDK 11 runtime environment is irregular. Please check your Java environment and settings.";
Msg.JAVA_LOW_VERSION = "Java 11 or Open JDK 11 is required. Current version is not supported. Please update the version and try again.";
//Debugger
Msg.DEBUG_WITHOUT_WORKSPACE = "Without workspace, debugger is not supported.";
Msg.DEBUG_WITHOUT_CONNECT = "Please connect to a U2 server first.";
Msg.DEBUG_DISCONNECT = "A BASIC program is under debugging, if disconnect from U2 server, the debug session will be lost. Continue?";
Msg.WRONG_WORKSPACE = "Please connect the db in the correct workspace folder.";
Msg.DEBUG_WITHOUT_EDITOR = "No active BASIC program file is open. Please open or configure a BASIC program file then try again.";
Msg.DEBUG_WRONG_FILE_TYPE = '${editor.document.uri.fsPath} is not valid Basic file, please choose a basic file.';
Msg.DEBUG_NOT_UNDER_WORKSPACE = "The program is not under the active workspace folder.";
Msg.DEBUG_NOT_OPEN_FILE = "Please open a file for debug.";
Msg.DEBUG_WRONG_LANGUAGE_ID = "Failed to debug current file. This may be due to incorrect file type or incorrect language type. Please check the current file or change the language type to Rocket MV BASIC.";
Msg.DEBUG_COMPILE_CONFIG = "Compilation will read the flavor from basic.mvbasic.json file.";
//Compile
Msg.ACCOUNT_NOT_CONSISTENT = "Account mismatch. Account <${predict.account}> for amended file doesn't match the connected mv account <${connect.account}>.";
Msg.WORKSPACE_NOT_CONSISTENT = "Please confirm whether the connected account <${account}> belongs to the workspace folder of the file to be compiled";
Msg.FILE_NOT_BEEN_LOAD = "The files to be compiled do not exist or has not been loaded.";
Msg.ACCOUNT_NOT_CONNECT = "Please connect to an account first to build BASIC program files.";
//# sourceMappingURL=msg.js.map