{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"package": {"add.eventListener.breakpoint": "切換事件接聽程式中斷點", "add.xhr.breakpoint": "新增 XHR/擷取中斷點", "attach.node.process": "連結到節點處理序", "base.cascadeTerminateToConfigurations.label": "當此偵錯工作階段終止時，也會一併停止的偵錯工作階段清單。", "base.enableDWARF.label": "切換偵錯工具是否會嘗試從可能耗用資源密集的 WebAssembly 讀取 DWARF 偵錯符號。需要 'ms-vscode.wasm-dwarf-debugging' 延伸模組才能運作。", "breakpoint.xhr.any": "任何 XHR/擷取", "breakpoint.xhr.contains": "當 URL 包含下列項目時中斷: ", "browser.address.description": "已偵錯瀏覽器所接聽的 IP 位址或主機名稱。", "browser.attach.port.description": "要用於對瀏覽器進行遠端偵錯的連接埠，會在啟動瀏覽器時指定為 `--remote-debugging-port`。", "browser.baseUrl.description": "用於解析路徑 baseUrl 的基底 URL。將 URL 對應至磁碟上的檔案時，baseURL 會受到修剪。預設為啟動 URL 網域。", "browser.browserAttachLocation.description": "強制瀏覽器附加至一個位置。在遠端工作區中 (例如，透過 SSH 或 WSL)，這可用來附加至遠端電腦上的瀏覽器，而非在本機附加。", "browser.browserLaunchLocation.description": "強制瀏覽器在一個位置啟動。在遠端工作區中 (例如，透過 ssh 或 WSL)，這可用來在遠端電腦上開啟瀏覽器，而非在本機開啟瀏覽器。", "browser.cleanUp.description": "偵錯工作階段完成之後要執行的清除。只關閉正在偵錯的索引標籤與關閉整個瀏覽器的比較。", "browser.cwd.description": "執行階段可執行檔的選用工作目錄。", "browser.disableNetworkCache.description": "控制是否跳過每個要求的網路快取", "browser.env.description": "瀏覽器環境索引鍵/值組的選用字典。", "browser.file.description": "要在瀏覽器中開啟的本機 html 檔案", "browser.includeDefaultArgs.description": "啟動中是否將包含預設的瀏覽器啟動引數 (以停用可能會讓偵錯更加困難的功能)。", "browser.includeLaunchArgs.description": "進階: 是否在瀏覽器上設定任何預設的啟動/偵錯引數。偵錯工具會假設瀏覽器將使用管道偵錯，例如使用 `--remote-debugging-pipe` 提供的管道偵錯。", "browser.inspectUri.description": "用於重寫 inspectUri 的格式: 這是在 `{curlyBraces}` 中插入索引鍵的範本字串。可用的索引鍵包括:\r\n - `url.*` 是目前執行的應用程式經剖析過後的位址。例如，`{url.port}`、`{url.hostname}`\r\n - `port` 是 Chrome 正在接聽的偵錯連接埠。\r\n - `browserInspectUri` 是啟動之瀏覽器上的偵測器 URI。\r\n - `browserInspectUriPath` 是啟動之瀏覽器上的偵測器 URI 路徑部分 (例如: \"/devtools/browser/e9ec0098-306e-472a-8133-5e42488929c2\")。\r\n - `wsProtocol` 是提示的 WebSocket 通訊協定。若來源 URL 為 `https`，此項目會設定為 `wss`; 不是 `https` 時，則會設定為 `ws`。\r\n", "browser.launch.port.description": "瀏覽器接聽的連接埠。預設為 \"0\"，會透過管道對瀏覽器進行偵錯，這種方式一般是較安全的建議選項，除非您需要透過其他工具連結至瀏覽器。", "browser.pathMapping.description": "URL/路徑對本機資料夾的對應，可將瀏覽器中的指令碼解析至磁碟上的指令碼", "browser.perScriptSourcemaps.description": "是否使用包含來源檔案之主檔名的唯一 sourcemap 個別載入指令碼。這可以設定為在處理大量小型指令碼時，將 sourcemap 處理最佳化。若設定為「自動」，我們將在適當情況下偵測已知案例。", "browser.profileStartup.description": "如果為 true，將在處理序啟動後立即開始分析", "browser.restart": "是否要在瀏覽器連線中斷時重新連線", "browser.revealPage": "焦點索引標籤", "browser.runtimeArgs.description": "傳遞至執行階段可執行檔的選用引數。", "browser.runtimeExecutable.description": "瀏覽器可執行檔的 'canary'、'stable'、'custom' 或路徑。自訂表示自訂包裝函式、自訂組建或 CHROME_PATH 環境變數。", "browser.runtimeExecutable.edge.description": "'canary'、'stable'、'dev'、'custom' 或瀏覽器可執行檔的路徑。Custom 表示自訂包裝函式、自訂組建或 EDGE_PATH 環境變數。", "browser.server.description": "設定要啟動的網頁伺服器。會採用與 'node' 啟動工作相同的組態。", "browser.skipFiles.description": "偵錯時要略過的檔案或資料夾名稱的陣列或路徑 glob。允許星號模式和否定，例如，`[\"**/node_modules/**\"、\"!**/node_modules/my-module/**\"]`", "browser.smartStep.description": "自動逐步執行 sourcemap 檔案中未對應的行。例如，TypeScript 在編譯 async/await 或其他功能時自動產生的程式碼。", "browser.sourceMapPathOverrides.description": "一組對應，用於將 sourcemap 中顯示的來源檔案位置重寫為其磁碟上的位置。如需詳細資料，請參閱讀我檔案。", "browser.sourceMapRenames.description": "是否要使用來源對應中的 \"names\" 對應。這需要要求來源內容，因此速度可能會因為特定偵錯工具而變慢。", "browser.sourceMaps.description": "使用 JavaScript 來源對應 (若存在)。", "browser.targetSelection": "是否附加到符合 URL 篩選的所有目標 (\"automatic\")，或要求您挑選一個 (\"pick\")。", "browser.timeout.description": "重試此毫秒數以連線至瀏覽器。預設為 10000 毫秒。", "browser.url.description": "將會搜尋具有此明確 URL 的索引標籤，並在找到時加以附加", "browser.urlFilter.description": "將會搜尋具有此 URL 的頁面，若有找到，會附加至該頁面。可有 * 萬用字元。", "browser.userDataDir.description": "根據預設，瀏覽器會以暫存資料夾中的單獨使用者設定檔啟動。使用此選項加以覆寫。設定為 False 來以您的預設使用者設定檔啟動。如果執行個體已從 `userDataDir` 執行，無法啟動新的瀏覽器。", "browser.vueComponentPaths": "用以尋找 '*.vue' 元件的檔案 Glob 樣式清單。根據預設會搜尋整個工作區。因為在 Vue CLI 4 中執行額外的查閱需要 Vue 的 sourcemap，所以必須設定此清單。只要將此設定成空白陣列，就能停用此特殊處理。", "browser.webRoot.description": "這會指定網頁伺服器根目錄的工作區絕對路徑。可用來將 `/app.js` 這類路徑解析成磁碟上的檔案。亦為 pathMapping 的 \"/\" 速記法", "chrome.attach.description": "附加至已經進入偵錯模式的 Chrome 執行個體", "chrome.attach.label": "Chrome: 連結", "chrome.label": "Web 應用程式 (Chrome)", "chrome.launch.description": "啟動 Chrome 以偵錯 URL", "chrome.launch.label": "Chrome: 啟動", "commands.callersAdd.label": "排除呼叫者", "commands.callersAdd.paletteLabel": "排除呼叫者在目前的位置暫停", "commands.callersGoToCaller.label": "前往呼叫者位置", "commands.callersGoToTarget.label": "前往目標位置", "commands.callersRemove.label": "移除排除的呼叫者", "commands.callersRemoveAll.label": "移除所有排除的呼叫者", "commands.disableSourceMapStepping.label": "停用來源對應逐步執行", "commands.enableSourceMapStepping.label": "啟用來源對應逐步執行", "commands.networkClear.label": "清除網路記錄", "commands.networkCopyURI.label": "複製要求 URL", "commands.networkOpenBody.label": "開啟回應本文", "commands.networkOpenBodyInHexEditor.label": "在十六進位編輯器中開啟回應本文", "commands.networkReplayXHR.label": "重新執行要求", "commands.networkViewRequest.label": "以 cURL 形式檢視要求", "configuration.autoAttachMode": "設定當 `#debug.node.autoAttach#` 為 on 時，會自動連結及偵錯哪一項處理序。無論此設定為何，使用 `--inspect` 旗標啟動的節點處理序一律會加以連結。", "configuration.autoAttachMode.always": "自動附加至在終端機中啟動的每個 Node.js 處理序。", "configuration.autoAttachMode.disabled": "自動附加已停用且不顯示於狀態列。", "configuration.autoAttachMode.explicit": "只有在指定 `--inspect` 時才自動附加。", "configuration.autoAttachMode.smart": "執行不在 node_modules 資料夾中的指令碼時自動附加。", "configuration.autoAttachSmartPatterns": "設定 Glob 模式以決定要在「智慧型」`#debug.javascript.autoAttachFilter#` 模式中附加的時機。已使用通用測試和程式碼執行器的名稱清單取代 `$KNOWN_TOOLS$`。[在 VS Code 文件閱讀更多內容](https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_auto-attach-smart-patterns)。", "configuration.automaticallyTunnelRemoteServer": "偵測遠端 Web 應用程式時，設定是否自動打開遠端伺服器到本機電腦的通道。", "configuration.breakOnConditionalError": "條件式中斷點擲回錯誤時是否停止。", "configuration.debugByLinkOptions": "在偵錯終端機內按一下偵錯開啟連結時所使用的選項。可設為 \"false\"，停用此行為。", "configuration.defaultRuntimeExecutables": "用於啟動設定的預設 `runtimeExecutable` (若未指定)。其可用於對 Node.js 或瀏覽器安裝，設定自訂路徑。", "configuration.enableNetworkView": "為支援其目標啟用實驗性網路檢視。", "configuration.npmScriptLensLocation": "\"Run\" 與 \"Debug\" 程式碼濾鏡應在您 npm 指令碼中顯示的位置。可以是「全部」、指令碼、指令碼區段的「頂端」或「永不」。", "configuration.pickAndAttachOptions": "透過 `Debug: Attach to Node.js Process` 命令偵錯處理序時所用的預設選項", "configuration.resourceRequestOptions": "在偵錯工具中載入資源 (例如來源對應) 時所使用的要求選項。舉例來說，若您的來源對應需要驗證或使用自我簽署憑證，就可能需要設定此項。選項可用於使用 [`got`](https://github.com/sindresorhus/got) 程式庫來建立要求。\r\n\r\n停用憑證驗證是一種常見案例，可透過傳遞 `{ \"https\": { \"rejectUnauthorized\": false } }` 來完成。", "configuration.terminalOptions": "JavaScript 偵錯終端機和 npm 指令碼的預設啟動選項。", "configuration.unmapMissingSources": "設定無法讀取原始檔案的 sourcemap 檔案是否將自動取消對應。如果為 false (預設)，系統會顯示提示。", "createDiagnostics.label": "診斷中斷點問題", "customDescriptionGenerator.description": "自訂偵錯工具為物件 (例如區域變數等...) 所顯示的文字描述。範例:\r\n      1. this.toString() // 會呼叫 toString 列印所有物件\r\n      2. this.customDescription ? this.customDescription() : defaultValue // 使用 customDescription 方法 (如果有的話)，如果沒有則傳回 defaultValue\r\n      3. 函式 (def) { return this.customDescription ? this.customDescription() : def } // 使用 customDescription 方法 (如果有的話)，如果沒有則傳回 defaultValue\r\n      ", "customPropertiesGenerator.description": "自訂偵錯工具中，為物件所顯示的屬性 (區域變數等等)。範例:\r\n    1. { ...this, extraProperty: '12345' } // 將 extraProperty 12345 新增至所有物件\r\n    2. this.customProperties ? this.customProperties() : this // 使用 customProperties 方法 (若可用); 若無法使用該方法，則請使用這裡的屬性 (預設屬性)\r\n    3. 函式 () { return this.customProperties ? this.customProperties() : this } // 使用 customDescription 方法 (若可用); 若無法使用該方法，則傳回預設屬性\r\n\r\n    已淘汰: 這是此功能暫時的執行方式，會持續到我們有時間以下列描述的方式進行實作為止: https://github.com/microsoft/vscode/issues/102181 (英文)", "debug.npm.edit": "編輯 package.json", "debug.npm.noScripts": "未在您的 package.json 找到 npm 指令碼", "debug.npm.noWorkspaceFolder": "您需要開啟工作區資料夾，才能偵錯 npm 指令碼。", "debug.npm.parseError": "無法讀取 {0}: {1}", "debug.npm.script": "偵錯 npm 指令碼", "debug.terminal.attach": "附加到 Node.js 終端處理序", "debug.terminal.label": "JavaScript 偵錯終端", "debug.terminal.program.description": "要在啟動終端機執行的指令。如未提供，終端機將不會在開啟時啟動程式。", "debug.terminal.snippet.label": "在偵錯終端機執行 \"npm start\"", "debug.terminal.toggleAuto": "切換終端 Node.js 自動附加", "debug.terminal.welcome": "[JavaScript 偵錯終端機](command:extension.js-debug.createDebuggerTerminal)\r\n\r\n您可以使用 JavaScript 偵錯終端機為命令列上執行的 Node.js 處理序進行偵錯。", "debug.terminal.welcomeWithLink": "[JavaScript 偵錯終端機](command:extension.js-debug.createDebuggerTerminal)\r\n\r\n您可以使用 JavaScript 偵錯終端機為命令列上執行的 Node.js 處理序進行偵錯。\r\n\r\n[偵錯 URL](command:extension.js-debug.debugLink)", "debug.unverifiedBreakpoints": "無法設定部分中斷點。如果您遇到問題，您可以 [疑難排解啟動設定](command:extension.js-debug.createDiagnostics)。", "debugLink.label": "開啟連結", "edge.address.description": "對 Web 檢視進行偵錯時，該 Web 檢視所接聽的 IP 位址或主機名稱。如果未設定，則會自動探索。", "edge.attach.description": "附加至已經進入偵錯模式的 Microsoft Edge 執行個體", "edge.attach.label": "Edge: 連結", "edge.label": "Web 應用程式 (Edge)", "edge.launch.description": "啟動 Microsoft Edge 來偵錯 URL", "edge.launch.label": "Edge: 啟動", "edge.port.description": "對 Web 檢視進行偵錯時，該 Web 檢視偵錯工具所接聽的連接埠。如果未設定，則會自動探索。", "edge.useWebView.attach.description": "物件，包含 UWP 代管 Webview2 之偵錯管道的 'pipeName'。這是建立管道 \"\\\\.\\pipe\\LOCAL\\MyTestSharedMemory\" 時的 \"MyTestSharedMemory\"", "edge.useWebView.launch.description": "為 'true' 時，偵錯工具會將執行階段可執行檔視為包含 WebView 的主機應用程式，以便您偵錯 WebView 指令碼內容。", "edit.xhr.breakpoint": "編輯 XHR/擷取中斷點", "enableContentValidation.description": "切換是否要驗證磁碟上的檔案內容符合執行時間中載入的內容。這適用於多種情況，而且在某些情況下是必要的，但舉例來說，如果您有指令碼的伺服器端轉換，就可能會造成問題。", "errors.timeout": "{0}: 經過 {1} 毫秒後逾時", "extension.description": "用於偵錯 Node.js 程式和 Chrome 的延伸模組。", "extensionHost.label": "VS Code 延伸模組開發", "extensionHost.launch.config.name": "啟動延伸模組", "extensionHost.launch.debugWebWorkerHost": "設定是否應嘗試附加到 Web 背景工作延伸主機。", "extensionHost.launch.debugWebviews": "設定是否要嘗試附加至已啟動 VS Code 執行個體中的 Web 檢視。這僅適用於桌面 VS Code。", "extensionHost.launch.env.description": "傳遞至延伸模組主機的環境變數。", "extensionHost.launch.rendererDebugOptions": "附加到轉譯器處理序時所使用的 Chrome 啟動選項，包含 `debugWebviews` 或 `debugWebWorkerHost`。", "extensionHost.launch.runtimeExecutable.description": "到達 VS 程式碼的絕對路徑。", "extensionHost.launch.stopOnEntry.description": "啟動後自動停止延伸模組主機。", "extensionHost.launch.testConfiguration": "[測試 CLI](https://code.visualstudio.com/api/working-with-extensions/testing-extension#quick-setup-the-test-cli) 的測試組態檔路徑", "extensionHost.launch.testConfigurationLabel": "要從檔案執行的單一設定。如果未指定，可能會要求您挑選。", "extensionHost.snippet.launch.description": "在偵錯模式下啟動 VS 程式碼延伸模組", "extensionHost.snippet.launch.label": "VS Code 延伸模組開發", "getDiagnosticLogs.label": "儲存診斷 JS 偵錯記錄", "longPredictionWarning.disable": "不再顯示", "longPredictionWarning.message": "設定中斷點需要一段時間。您可以透過更新 launch.json 中的 'outFiles' 來加快速度。", "longPredictionWarning.noFolder": "未開啟工作區資料夾。", "longPredictionWarning.open": "開啟 launch.json", "node.address.description": "要偵錯之處理序的 TCP/IP 位址。預設為 'localhost'。", "node.attach.attachExistingChildren.description": "是否要嘗試附加至已衍生的子處理序。", "node.attach.attachSpawnedProcesses.description": "是否要在附加的處理序中設定環境變數，來追蹤繁衍的子系。", "node.attach.config.name": "附加", "node.attach.continueOnAttach": "如果為 true，我們會自動繼續啟動的程式並等待 `--inspect-brk`", "node.attach.processId.description": "要連結的處理序之識別碼。", "node.attach.restart.description": "當連線中斷時，嘗試重新連線到該程式。若設定為 'true'，將不間斷地每秒嘗試一次。您可以在物件中指定 'delay' 與 'maxAttempts'，以自訂間隔及嘗試次數上限。", "node.attachSimplePort.description": "如有設定，則會透過指定的連接埠附加到處理序。Node.js 程式通常已不需要此設定，而且無法對子處理序進行偵錯，但在較特殊的情況下 (例如啟動 Deno 和 Docker 時)，可能會有幫助。若設為 0，則會選擇隨機的連接埠，並自動將 --inspect-brk 新增到啟動引數。", "node.console.title": "節點偵錯主控台", "node.disableOptimisticBPs.description": "在為該檔案載入 sourcemap 之前，請勿在任何檔案中設定中斷點。", "node.enableTurboSourcemaps.description": "設定是否要針對來源對應探索使用更快速的新機制", "node.experimentalNetworking.description": "在 Node.js 中啟用實驗性檢查。當設定為 [自動] 時，支援它的 Node.js 版本將啟用此功能。它可以設定為 [開啟] 或 [關閉]，以明確啟用或停用它。", "node.killBehavior.description": "設定當工作階段停止時，如何終止偵錯處理序。可設定為:\r\n\r\n- 強制 (預設): 強制卸除處理序樹狀結構。在 posix 上請傳送 SIGKILL，在 Windows 上請傳送 `taskkill.exe /F`。\r\n- 正常: 適當地卸除處理序樹狀結構。以此方式關機之後，行為不當的處理序有可能仍會持續執行。在 posix 上請傳送 SIGTERM，在 Windows 上則請傳送不指定 `/F` (強制) 旗標的 `taskkill.exe`。\r\n- 無: 不發生任何終止。", "node.label": "Node.js", "node.launch.args.description": "傳遞至程式的命令列引數。\r\n\r\n可以是字串陣列或單一字串。當程式在終端機中啟動時，將此屬性設定為單一字串將導致命令介面的引數不會逸出。", "node.launch.autoAttachChildProcesses.description": "自動附加偵錯工具至新的子處理序。", "node.launch.config.name": "啟動", "node.launch.console.description": "啟動偵錯目標的位置。", "node.launch.console.externalTerminal.description": "可透過使用者設定進行外部終端機的設定。", "node.launch.console.integratedTerminal.description": "VS Code 的整合式終端機", "node.launch.console.internalConsole.description": "VS Code 偵錯主控台 (無法從程式讀取輸入)", "node.launch.cwd.description": "正在接受偵錯之程式的工作目錄絕對路徑。如果您已設定 localRoot，則 cwd 會比對此值，否則會退回到您的 workspaceFolder", "node.launch.env.description": "傳遞至程式的環境變數。值 'null' 會從環境移除該變數。", "node.launch.envFile.description": "包含環境變數定義的檔案絕對路徑。", "node.launch.logging": "記錄設定", "node.launch.logging.cdp": "Chrome DevTools Protocol 訊息的記錄檔路徑", "node.launch.logging.dap": "偵錯配接器通訊協定訊息的記錄檔路徑", "node.launch.outputCapture.description": "擷取輸出訊息的來源: 如果設定為 `console`，則為預設偵錯 API; 如果設定為 `std`，則為 stdout/stderr 資料流。", "node.launch.program.description": "程式的絕對路徑。透過查看 package.json 及開啟的檔案猜測產生的值。編輯此屬性。", "node.launch.restart.description": "如果程式以非零的結束代碼結束，請嘗試重新啟動程式。", "node.launch.runtimeArgs.description": "傳遞到執行階段可執行檔的選擇性引數。", "node.launch.runtimeExecutable.description": "要使用的執行階段。可以是 PATH 上可用執行階段的絕對路徑或名稱。若省略，即假設為 `node`。", "node.launch.runtimeSourcemapPausePatterns": "手動插入進入點中斷點的模式清單。這對於以下情形相當實用，在啟動之前使用不存在或無法偵測到的 sourcemap，讓偵錯工具得以設定中斷點，例如[使用無伺服器架構](https://github.com/microsoft/vscode-js-debug/issues/492)。", "node.launch.runtimeVersion.description": "'node' 版本的執行階段使用中。需要 `nvm`。", "node.launch.useWSL.deprecation": "'useWSL' 已淘汰且其支援將受到捨棄。請改為使用 'Remote - WSL' 延伸模組。", "node.launch.useWSL.description": "使用 Windows 子系統 Linux 版。", "node.localRoot.description": "包含程式的本機目錄路徑。", "node.pauseForSourceMap.description": "是否要等待各個連入指令碼的來源對應載入。這會有效能額外負荷，且只要未停用 `rootPath`，就能在磁碟上執行時安全停用。", "node.port.description": "偵錯要連結的連接埠。預設值為 9229。", "node.processattach.config.name": "附加至處理序", "node.profileStartup.description": "如果為 true，將在處理序啟動後立即開始分析", "node.remote.host.header.description": "連接到偵測器的 WebSocket 時要使用的明確主機標頭。如果未指定，主機標頭將設定為 'localhost'。當偵測器在只接受特定主機標頭的 Proxy 後執行時，此功能顯得十分有用。", "node.remoteRoot.description": "包含程式的遠端目錄絕對路徑。", "node.resolveSourceMapLocations.description": "位置 (資料夾和 URL) 的 minimatch 模式清單，其中來源對應可用於解析本機檔案。您可以利用此清單來避免外部來源對應程式碼中出現錯誤分行。在模式名稱開頭加上 \"!\" 可予以排除。可設定為空白陣列或 null 來避免限制。", "node.showAsyncStacks.description": "顯示導致目前呼叫堆疊的非同步呼叫。", "node.snippet.attach.description": "附加至Node處理程序", "node.snippet.attach.label": "Node.js: 附加", "node.snippet.attachProcess.description": "開啟處理序選擇器以選取要連結的目標節點處理序", "node.snippet.attachProcess.label": "Node.js: 連結到處理序", "node.snippet.electron.description": "對 Electron 主要處理序偵錯", "node.snippet.electron.label": "Node.js: Electron 主要", "node.snippet.gulp.description": "對 Gulp 工作偵錯 (確認您的專案中已安裝本機 Gulp)", "node.snippet.gulp.label": "Node.js: <PERSON><PERSON> 工作", "node.snippet.launch.description": "在偵錯模式中啟動節點程式", "node.snippet.launch.label": "Node.js: 啟動程式", "node.snippet.mocha.description": "對 mocha 測試偵錯", "node.snippet.mocha.label": "Node.js: <PERSON><PERSON> 測試", "node.snippet.nodemon.description": "使用 nodemon 在來源變更時重新啟動偵錯工作階段", "node.snippet.nodemon.label": "Node.js: Nodemon 設定", "node.snippet.npm.description": "透過 npm `debug` 指令碼啟動節點程式", "node.snippet.npm.label": "Node.js: 透過 npm 啟動", "node.snippet.remoteattach.description": "附加到遠端節點程式的偵錯連接埠", "node.snippet.remoteattach.label": "Node.js: 附加到遠端程式", "node.snippet.yo.description": "偵錯 Yeoman 產生器 (透過執行專案資料夾中的 `npm link` 進行安裝)", "node.snippet.yo.label": "Node.js: <PERSON><PERSON> 產生器", "node.sourceMapPathOverrides.description": "依據 sourcemap 指示重新寫入一組來源檔案位置對應至磁碟上的位置。", "node.sourceMaps.description": "使用 JavaScript 來源對應 (如果有)。", "node.stopOnEntry.description": "啟動後自動停止程式。", "node.timeout.description": "重試連線到 Node.js 的毫秒數。預設值為 10000 毫秒。", "node.versionHint.description": "允許您明確指定正在執行的節點版本，以在無法自動偵測版本時，用以停用或啟用特定行為。", "node.websocket.address.description": "要附加至的確切 WebSocket 位址。若未指定，則會從位址和連接埠進行探索。", "openEdgeDevTools.label": "開啟瀏覽器 Devtools", "outFiles.description": "若已啟用來源對應，則這些 glob 模式會指定產生的 JavaScript 檔案。若模式的開頭為 `!` ，即會排除檔案。若未指定，則產生的程式碼應與其來源位於相同的目錄中。", "pretty.print.script": "偵錯的美化顯示", "profile.start": "取得效能設定檔", "profile.stop": "停止效能設定檔", "remove.eventListener.breakpoint.all": "移除所有事件接聽程式中斷點", "remove.xhr.breakpoint": "移除 XHR/擷取中斷點", "remove.xhr.breakpoint.all": "移除所有 XHR/擷取中斷點", "requestCDPProxy.label": "針對偵錯工作階段要求 CDP Proxy", "skipFiles.description": "偵錯時要跳過的檔案 Glob 模式陣列。該模式 `<node_internals>/**` 會比對所有內部 Node.js 模組。", "smartStep.description": "自動逐步執行產生的程式碼 (無法對應回原始來源)。", "start.with.stop.on.entry": "啟動偵錯並停止在進入點", "startWithStopOnEntry.label": "啟動偵錯並停止在進入點", "timeouts.generalDescription": "幾個偵錯工具作業的逾時。", "timeouts.generalDescription.markdown": "幾個偵錯工具作業的逾時。", "timeouts.hoverEvaluation.description": "中止暫留符號之值評估前的時間。如果設為 0，暫留評估永遠不會逾時。", "timeouts.sourceMaps.description": "與來源對應作業相關的逾時。", "timeouts.sourceMaps.sourceMapCumulativePause.description": "每個工作階段在基本時間 (sourceMapMinPause) 用盡之後，可額外用於等候來源對應處理的時間 (毫秒)", "timeouts.sourceMaps.sourceMapMinPause.description": "剖析指令碼時，花費在等候每個來源對應處理的基本時間 (毫秒)", "toggle.skipping.this.file": "切換跳過此檔案", "trace.boolean.description": "追蹤可設為 'true' 來將診斷記錄寫入磁碟。", "trace.description": "設定產生的診斷輸出。", "trace.logFile.description": "設定要寫入磁碟記錄的位置。", "trace.stdio.description": "是否從啟動的應用程式或是瀏覽器傳回追蹤資料。", "workspaceTrust.description": "需要信任才能在此工作區中對程式碼進行偵錯。"}, "bundle": {"A profiling session is already running, would you like to stop it and start a new session?": "分析工作階段已在執行。要停止該工作階段，然後再啟動新的工作階段嗎?", "Add XHR Breakpoint": "新增 XHR 中斷點", "Add new URL...": "新增新的 URL...", "Adjust glob pattern(s) in the 'outFiles' attribute so that they cover the generated JavaScript.": "調整 'outFiles' 屬性中的 Glob 模式，讓模式涵蓋所產生的 JavaScript。", "Always": "永遠", "Always in this Workspace": "一律在此工作區中", "An error occurred taking a profile from the target.": "從目標擷取設定檔時發生錯誤。", "Animation Frame Fired": "已觸發動畫畫面格", "Any XHR or fetch": "任何 XHR 或擷取", "Assertion failed": "判斷提示失敗", "Attach to process: '{0}' doesn't look like a process id.": "附加至處理序: '{0}' 看起來不像處理序識別碼。", "Attach to process: cannot enable debug mode for process '{0}' ({1}).": "附加至處理序: 無法啟用處理序 '{0}' ({1}) 的偵錯模式。", "Attribute 'runtimeVersion' requires Node.js version manager 'nvm-windows' or 'nvs'.": "屬性 'runtimeVersion' 需要 Node.js 版本管理員 'nvm-windows' 或 'nvs'。", "Attribute 'runtimeVersion' requires Node.js version manager 'nvs', 'nvm' or 'fnm' to be installed.": "屬性 'runtimeVersion' 需要安裝 Node.js 版本管理員 'nvs'、'nvm' 或 'fnm' 。", "Attribute 'runtimeVersion' with a flavor/architecture requires 'nvs' to be installed.": "具有變體/結構的屬性 'runtimeVersion' 需要安裝 'nvs'。", "Bidder Bidding Phase Start": "競標者競標階段開始", "Bidder Reporting Phase Start": "競標者報告階段開始", "Block": "封鎖", "Break when URL Contains": "當 URL 包含下列項目時中斷: ", "Breaks on all throw errors, even if they're caught later.": "中斷所有擲回錯誤，即使是之後攔截的也一樣。", "Breaks only on errors or promise rejections that are not handled.": "只在未處理的錯誤或承諾拒絕時中斷。", "Browser connection failed, will retry: {0}": "瀏覽器連線失敗，將重試: {0}", "CPU Profile": "CPU 設定檔", "CPU profile saved as \"{0}\" in your workspace folder": "CPU 設定檔已在工作區資料夾中另存為 \"{0}\"", "CSP violation \"{0}\"": "CSP 違規 \"{0}\"", "Can't find Node.js binary \"{0}\": {1}. Make sure Node.js is installed and in your PATH, or set the \"runtimeExecutable\" in your launch.json": "找不到 Node.js 二進位檔 \"{0}\": {1}。請確認 Node.js 已安裝，且位於您的 PATH 中，或在您的 launch.json 中設定 \"runtimeExecutable\"", "Can't load environment variables from file ({0}).": "無法從檔案 ({0}) 載入環境變數。", "Cancel Animation Frame": "取消動畫框架", "Cannot connect to the target at {0}: {1}": "無法連線到位於 {0} 的目標: {1}", "Cannot find `{0}` installed in {1}": "找不到安裝在 {1} 中的 '{0}'", "Cannot find a program to debug": "找不到要偵錯的程式", "Cannot find test configuration with label `{0}`, got: {1}": "找不到標籤為 '{0}' 的測試設定，已取得: {1}", "Cannot launch debug target in terminal ({0}).": "在終端機 ({0}) 中無法啟動偵錯目標。", "Cannot restart asynchronous frame": "無法重新啟動非同步框架", "Cannot set an empty value": "無法設定空白值", "Catch Block": "Catch 區塊", "Caught Exceptions": "攔截到例外狀況", "Close AudioContext": "關閉 AudioContext", "Closure": "關閉", "Closure ({0})": "關閉 ({0})", "Console profile started": "主機設定檔已啟動", "Could not connect to any UWP Webview pipe. Make sure your webview is hosted in debug mode, and that the `pipeName` in your `launch.json` is correct.": "無法連線到任何 UWP Web 檢視管道。請確定您的 Web 檢視是以偵錯模式託管，且 'launch.json' 中的 'pipeName' 正確無誤。", "Could not find a location for the variable": "找不到變數的位置", "Could not query the provided object": "無法查詢提供的物件", "Could not read source map for {0}: {1}": "無法讀取 {0} 的原始檔對應: {1}", "Could not read {0}: {1}": "無法讀取 {0}: {1}", "Create AudioContext": "建立 AudioContext", "Create canvas context": "建立畫布內容", "Debug Anyway": "仍要偵錯", "Debug URL": "偵錯 URL", "Details": "詳細資料", "Don't show again": "不要再顯示", "Duration": "持續時間", "Duration of Profile": "設定檔持續時間", "Edit XHR Breakpoint": "編輯 XHR 中斷點", "Edit package.json": "編輯 package.json", "Enables Node.js [auto attach]({0}) debugging in \"{1}\" mode/{Locked='[auto attach]({0})'}the 2nd placeholder is the setting value": "以 \"{1}\" 模式啟用 Node.js [auto attach]({0}) 偵錯", "Enter a URL or a pattern to match": "輸入要比對的 URL 或模式", "Eval": "Eval", "Frame could not be restarted": "無法重新啟動框架", "Generates a .cpuprofile file you can open in VS Code or the Edge/Chrome devtools": "產生可以在 VS Code 或 Edge/Chrome DevTools 中開啟的 .cpuprofile 檔案", "Generates a .heapprofile file you can open in VS Code or the Edge/Chrome devtools": "產生可以在 VS Code 或 Edge/Chrome DevTools 中開啟的 .heapprofile 檔案", "Generates a .heapsnapshot file you can open in VS Code or the Edge/Chrome devtools": "產生可以在 VS Code 或 Edge/Chrome DevTools 中開啟的 .heapsnapshot 檔案", "Global": "全域", "Globals": "全域", "Got it!": "了解!", "Heap Profile": "堆積設定檔", "Heap Snapshot": "堆積快照集", "How long to run the profile": "設定檔的執行時間長度", "Ignore": "略過", "Installation complete! The extension will be used after you restart your debug session.": "安裝完成! 重新啟動偵錯工作階段後，將會使用延伸模組。", "Installing the DWARF debugger...": "正在安裝 DWARF 偵錯工具...", "Invalid expression": "無效的運算式", "Invalid hit condition \"{0}\". Expected an expression like \"> 42\" or \"== 2\".": "到達條件 \"{0}\" 無效。需要 \"> 42\" 或 \"== 2\" 之類的運算式。", "It looks like a browser is already running from {0}. Please close it before trying to debug, otherwise VS Code may not be able to connect to it.": "瀏覽器似乎已從 {0} 執行。請先將其關閉再嘗試進行偵錯，否則 VS Code 可能無法與其連線。", "It looks like your debug session has already ended. Try debugging again, then run the \"Debug: Diagnose Breakpoint Problems\" command.": "您的偵錯工作階段似乎已結束。再次嘗試進行偵錯工具，然後執行 \"Debug: Diagnose Breakpoint Problems\" 命令。", "It's taking a while to configure your breakpoints. You can speed this up by updating the 'outFiles' in your launch.json.": "設定中斷點需要一段時間。您可以透過更新 launch.json 中的 'outFiles' 來加快速度。", "JavaScript Debug Terminal": "JavaScript 偵錯終端", "JavaScript debug adapter": "JavaScript 偵錯配接器", "Launch Chrome against localhost": "對 localhost 啟動 Chrome", "Launch Edge against localhost": "對 localhost 啟動 Microsoft Edge", "Launch Program": "啟動程式", "Launch configuration created based on 'package.json'.": "根據 'package.json' 啟動建立的組態。", "Launch configuration for '{0}' project created.": "為建立的 '{0}' 專案啟動組態。", "Local": "本機", "Locals": "區域變數", "Lost connection to debugee, reconnecting in {0}ms\r\n": "與偵錯項目的連線已中斷。將於 {0} 毫秒內重新連線\r\n", "Manual": "手動", "Missing source information. Did you set \"originalUrl\" or \"source\"?": "缺少來源資訊。您是設定 「originalUrl」 或 「source」 嗎？", "Module": "模組", "Networking not available.": "網路功能無法使用。", "Never": "永不", "No": "否", "No npm scripts found in the workspace folder.": "在工作區資料夾中找不到 npm 指令碼。", "No npm scripts found in your package.json": "未在您的 package.json 找到 npm 指令碼", "No package.json files found in your workspace.": "在您的工作區中找不到 package.json 檔案。", "No workspace folder open.": "未開啟工作區資料夾。", "Node Attributes": "節點屬性", "Node.js version '{0}' not installed using version manager {1}.": "未使用版本管理員 {1} 安裝 Node.js '{0}' 版。", "Not Now": "現在不要", "Only objects can be queried": "只能查詢物件", "Open launch.json": "開啟 launch.json", "Output has been truncated to the first {0} characters. Run `{1}` to copy the full output.": "輸出已截斷為前 {0} 個字元。執行 '{1}' 以複製完整輸出。", "Parameters": "參數", "Paused": "已暫停", "Paused before Out Of Memory exception": "已在發生記憶體不足例外狀況之前暫停", "Paused on Content Security Policy violation instrumentation breakpoint, directive \"{0}\"": "已在內容安全性原則違規檢測中斷點時暫停，指示詞 \"{0}\"", "Paused on DOM breakpoint": "已在 DOM 中斷點時暫停", "Paused on WebGL Error instrumentation breakpoint, error \"{0}\"": "已在 WebGL 錯誤檢測中斷點時暫停，錯誤 \"{0}\"", "Paused on XMLHttpRequest or fetch": "已在 XMLHttpRequest 或擷取時暫停", "Paused on assert": "已在判斷提示時暫停", "Paused on breakpoint": "在中斷點時暫停", "Paused on debug() call": "已在呼叫 debug() 時暫停", "Paused on debugger statement": "已於偵錯工具陳述式暫停", "Paused on event listener": "已在事件接聽程式時暫停", "Paused on event listener breakpoint \"{0}\", triggered on \"{1}\"": "已在 \"{1}\" 觸發的事件接聽程式中斷點 \"{0}\" 時暫停", "Paused on exception": "已在發生例外狀況時暫停", "Paused on frame entry": "已於框架項目暫停", "Paused on instrumentation breakpoint": "已在檢測中斷點時暫停", "Paused on instrumentation breakpoint \"{0}\"": "已在檢測中斷點 \"{0}\" 時暫停", "Paused on {0}": "暫停於 {0}", "Pick Breakpoint": "挑選中斷點", "Pick the node.js process to attach to": "挑選附加目標 node.js 處理序", "Please enter a number": "請輸入數字", "Please enter a number greater than 1": "請輸入大於 1 的數字", "Please stop the running profile before starting a new one.": "請先停止正在執行的設定檔，然後再啟動新的設定檔。", "Process picker failed ({0})": "處理序選擇器失敗 ({0})", "Profile duration in seconds, e.g \"5\"": "設定檔持續時間 (秒)，例如 \"5\"", "Profiling": "分析", "Profiling with breakpoints enabled can change the performance of your code. It can be useful to validate your findings with the \"duration\" or \"manual\" termination conditions.": "分析如有啟用中斷點，可能會造成程式碼的效能變更。您可以使用「持續時間」或「手動」終止條件來驗證您的觀察。", "Read More": "深入了解", "Request Animation Frame": "要求動畫畫面格", "Resume AudioContext": "繼續 AudioContext", "Return value": "傳回值", "Run Current File": "執行目前的檔案", "Run Node.js tool": "執行 Node.js 工具", "Run Script: {0}": "執行指令碼: {0}", "Run for a specific amount of time": "執行特定的時間長度", "Run until a specific breakpoint is hit": "執行到到達特定中斷點時", "Run until manually stopped": "執行到手動停止時", "Runs a Node.js command-line installed in the workspace node_modules.": "執行安裝於工作區 node_modules 的 Node.js 命令列。", "Saving": "正在儲存", "Script": "指令碼", "Script Blocked by Content Security Policy": "內容安全性原則禁止的指令碼", "Script First Statement": "指令碼中的第一個陳述式", "Select a tab": "選取索引標籤", "Select a tool to run": "選取要執行的工具", "Select current working directory for new terminal": "為新的終端機選擇目前的工作目錄", "Select test configuration to run": "選取要執行的測試設定", "Select the page where you want to open the devtools": "選取要開啟 Devtools 的頁面", "Select the session you want to inspect:": "選取您想要檢查的工作階段:", "Seller Reporting Phase Start": "賣方報告階段開始", "Seller Scoring Phase Start": "賣方評分階段開始", "Set innerHTML": "設定 innerHTML", "Skipped by skipFiles": "已由 skipFiles 略過", "Skipped by smartStep": "已由 smartStep 略過", "Some breakpoints might not work in your version of Node.js. We recommend upgrading for the latest bug, performance, and security fixes. Details: https://aka.ms/AAcsvqm": "某些中斷點在您的 Node.js 版本中可能無法運作。建議您升級，以取得最新的錯誤、效能及安全性修正。詳細資料: https://aka.ms/AAcsvqm", "Source not a source map": "原始檔不是原始檔對應", "Source not found": "找不到來源", "Stack frame not found": "找不到堆疊框架", "Starting profile...": "正在啟動設定檔...", "Stopping profile...": "正在停止設定檔...", "Suspend AudioContext": "暫停 AudioContext", "Syntax error setting breakpoint with condition {0} on line {1}: {2}": "使用條件 {0} 在第 {1}: {2} 行設定中斷點時發生語法錯誤", "Target page not found. You may need to update your \"urlFilter\" to match the page you want to debug.": "找不到目標頁面。您必須更新 \"urlFilter\"，才能找到您要偵錯的頁面。", "The Node version in \"{0}\" is outdated (version {1}), we require at least Node 8.x.": "\"{0}\" 中的 Node 版本已過期 ({1} 版)。最低需要 Node 8.x。", "The URL provided is invalid": "提供的 URL 無效", "The browser process exited with code {0} before connecting to the debug server. Make sure the `runtimeExecutable` is configured correctly and that it can run without errors.": "瀏覽器處理序在連線到偵錯伺服器之前已結束，代碼 {0}。請確定已正確設定 `runtimeExecutable`，而且可以執行而不發生錯誤。", "The configured `cwd` {0} does not exist.": "設定的 'cwd' {0} 不存在。", "The configured `cwd` {0} is not a folder.": "設定的 'cwd' {0} 不是資料夾。", "This is a missing file path referenced by a sourcemap. Would you like to debug the compiled version instead?": "此為 sourcemap 所參考的遺失檔案路徑。您要改為對已編譯的版本進行偵錯嗎?", "Thread is not paused": "執行緒未暫停", "Thread is not paused on exception": "執行緒未在發生例外狀況時暫停", "Thread not found": "找不到執行緒", "Type of profile": "設定檔類型", "URL contains \"{0}\"": "URL 包含 \"{0}\"", "UWP webview debugging is not available on your platform.": "您的平台無法使用 UWP Web 檢視偵錯。", "Unable to attach to browser": "無法連結到瀏覽器", "Unable to evaluate": "無法評估", "Unable to evaluate on async stack frame": "無法評估非同步堆疊框架", "Unable to find an installation of the browser on your system. Try installing it, or providing an absolute path to the browser in the \"runtimeExecutable\" in your launch.json.": "在您的系統上找不到瀏覽器的安裝。請嘗試安裝該項目，或在您 launch.json 的 \"runtimeExecutable\" 中提供瀏覽器的絕對路徑。", "Unable to find {0} version {1}. Available auto-discovered versions are: {2}. You can set the \"runtimeExecutable\" in your launch.json to one of these, or provide an absolute path to the browser executable.": "找不到 {0} {1} 版。提供之自動探索到的版本: {2}。您可以將您 launch.json 中的 \"runtimeExecutable\" 設定為其中之一，或是提供瀏覽器可執行檔的絕對路徑。", "Unable to launch browser: \"{0}\"": "無法啟動瀏覽器: \"{0}\"", "Unable to pause": "無法暫停", "Unable to pretty print": "無法美化圖片", "Unable to resume": "無法繼續", "Unable to retrieve source content": "無法擷取原始檔內容", "Unable to set variable value": "無法設定變數值", "Unable to step in": "無法逐步執行", "Unable to step next": "無法繼續下一行", "Unable to step out": "無法跳離", "Unbound breakpoint": "未繫結中斷點", "Uncaught Exceptions": "未攔截到例外狀況", "Unknown error": "未知的錯誤", "VS Code can provide better debugging experience for WebAssembly via \"DWARF Debugging\" extension. Would you like to install it?/\"DWARF Debugging\" is the extension name and should not be localized.": "VS Code 可透過「DWARF 偵錯」延伸模組為 WebAssembly 提供更好的偵錯體驗。要安裝嗎?", "Variable not found": "找不到變數", "Variables not available in async stacks": "變數在非同步堆疊中無法使用", "WARNING: Processing source-maps of {0} took longer than {1} ms so we continued execution without waiting for all the breakpoints for the script to be set.": "警告: 處理 {0} 來源對應的時間超過 {1} 毫秒，因此繼續執行，但不會等候指令碼的所有中斷點完成設定。", "We can't launch a browser in debug mode from here. If you want to debug this webpage, open this workspace from VS Code on your desktop.": "在偵錯模式中時，無法由此啟動瀏覽器。若要對此網頁執行偵錯，請從您桌面的 VS Code 中開啟此工作區。", "We can't launch a browser in debug mode from here. Open this workspace in VS Code on your desktop to enable debugging.": "在偵錯模式中時，無法由此啟動瀏覽器。請從您桌面的 VS Code 中開啟此工作區，以啟用偵錯。", "WebGL Error Fired": "已觸發 WebGL 錯誤", "WebGL Warning Fired": "已觸發 WebGL 警告", "With Block": "With 區塊", "Would you like to save a configuration in your launch.json for easy access later?": "您要將設定儲存在您的 launch.json 中，以便日後存取嗎?", "XHR/Fetch URLs": "XHR/擷取 URL", "Yes": "是", "You may install the `{}` module via npm for enhanced WebAssembly debugging": "您可以透過 npm 安裝 '{}' 模組以進行增強式 WebAssembly 偵錯", "You need to open a workspace folder to debug npm scripts.": "您需要開啟工作區資料夾，才能偵錯 npm 指令碼。", "You're running an outdated version of Node.js. We recommend upgrading for the latest bug, performance, and security fixes.": "您執行的 Node.js 版本已過期。我們建議升級以修正最新的錯誤、效能和安全性問題。", "an old debug session": "舊的偵錯工作階段", "breakpoint.provisionalBreakpoint": "breakpoint.provisionalBreakpoint", "path does not exist": "路徑不存在", "process id: {0} ({1})": "處理序 id: {0} ({1})", "process id: {0}, debug port: {1} ({2})": "處理序 id: {0}, 偵錯埠: {1} ({2})", "setInterval fired": "已觸發 setInterval", "setTimeout fired": "已觸發 setTimeout", "the configured userDataDir": "設定的 userDataDir", "{0} (couldn't describe: {1})": "{0} (無法描述: {1})", "{0} Click to Stop Profiling": "{0} 按一下可停止分析", "{0} Click to Stop Profiling ({1} sessions)": "{0} 按一下可停止分析 ({1} 個工作階段)", "{0} Click to Stop Profiling ({1})": "{0} 按一下可停止分析 ({1})"}}}