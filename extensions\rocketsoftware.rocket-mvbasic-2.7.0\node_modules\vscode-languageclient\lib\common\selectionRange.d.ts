import { Disposable, TextDocument, ProviderResult, Position as VPosition, SelectionRange as VSelectionRange, SelectionRangeProvider } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, SelectionRangeOptions, SelectionRangeRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export interface ProvideSelectionRangeSignature {
    (this: void, document: TextDocument, positions: readonly VPosition[], token: CancellationToken): ProviderResult<VSelectionRange[]>;
}
export interface SelectionRangeProviderMiddleware {
    provideSelectionRanges?: (this: void, document: TextDocument, positions: readonly VPosition[], token: CancellationToken, next: ProvideSelectionRangeSignature) => ProviderResult<VSelectionRange[]>;
}
export declare class SelectionRangeFeature extends TextDocumentLanguageFeature<boolean | SelectionRangeOptions, SelectionRangeRegistrationOptions, SelectionRangeProvider, SelectionRangeProviderMiddleware> {
    constructor(client: FeatureClient<SelectionRangeProviderMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: SelectionRangeRegistrationOptions): [Disposable, SelectionRangeProvider];
    private registerProvider;
}
