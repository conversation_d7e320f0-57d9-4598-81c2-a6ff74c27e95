{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Branch name": "分支名稱", "Choose a URL to clone from.": "選擇複製的來源 URL。", "No remote repositories found.": "找不到遠端存放庫。", "Provide repository URL": "提供存放庫 URL", "Provide repository URL or pick a repository source.": "提供存放庫 URL 或挑選存放庫來源。", "Repository name": "存放庫名稱", "Repository name (type to search)": "存放庫名稱 (要搜尋的類型)", "URL": "URL", "recently opened": "最近開啟的", "remote sources": "遠端來源", "{0} Error: {1}": "{0} 錯誤: {1}"}, "package": {"command.api.getRemoteSources": "取得遠端來源", "description": "Git 靜態貢獻和選擇器。", "displayName": "Git 基底"}}}