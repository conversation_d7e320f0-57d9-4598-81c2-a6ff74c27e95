# VS Code Debug Protocol

[![NPM Version](https://img.shields.io/npm/v/vscode-debugprotocol.svg)](https://npmjs.org/package/vscode-debugprotocol)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-debugprotocol.svg)](https://npmjs.org/package/vscode-debugprotocol)

This npm module contains declarations for the json-based Visual Studio Code debug protocol.

## History

As of August 2018 the change history of the Debug Adapter Protocol lives on the [Debug Adapter Protocol web site](https://microsoft.github.io/debug-adapter-protocol/changelog).


## License

[MIT](https://github.com/microsoft/vscode-debugadapter-node/blob/main/License.txt)
