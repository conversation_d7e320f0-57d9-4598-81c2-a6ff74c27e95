"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MvVs = void 0;
const builder_1 = require("./compile/builder");
const dirty_1 = require("./compile/dirty");
const extension_1 = require("./extension");
const onlineDirty_1 = require("./online/compile/onlineDirty");
class MvVs {
    constructor() {
        this._builder = new builder_1.Builder();
        this._dirty = new dirty_1.DirtyFileBox();
    }
    onlineLaunched() {
        this._builder = extension_1.online.builder;
        this._dirty = new onlineDirty_1.OnlineDirtyFileBox();
    }
    getBuilder() {
        return this._builder;
    }
    getDirty() {
        return this._dirty;
    }
}
exports.MvVs = MvVs;
//# sourceMappingURL=mvvs.js.map