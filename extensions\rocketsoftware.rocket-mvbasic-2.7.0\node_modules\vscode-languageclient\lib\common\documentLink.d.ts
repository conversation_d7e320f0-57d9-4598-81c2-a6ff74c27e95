import { TextDocument, Disposable, CancellationToken, ProviderResult, DocumentLinkProvider, DocumentLink as VDocumentLink } from 'vscode';
import { ClientCapabilities, DocumentLinkOptions, DocumentLinkRegistrationOptions, DocumentSelector, ServerCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideDocumentLinksSignature {
    (this: void, document: TextDocument, token: CancellationToken): ProviderResult<VDocumentLink[]>;
}
export interface ResolveDocumentLinkSignature {
    (this: void, link: VDocumentLink, token: CancellationToken): ProviderResult<VDocumentLink>;
}
export interface DocumentLinkMiddleware {
    provideDocumentLinks?: (this: void, document: TextDocument, token: CancellationToken, next: ProvideDocumentLinksSignature) => ProviderResult<VDocumentLink[]>;
    resolveDocumentLink?: (this: void, link: VDocumentLink, token: CancellationToken, next: ResolveDocumentLinkSignature) => ProviderResult<VDocumentLink>;
}
export declare class DocumentLinkFeature extends TextDocumentLanguageFeature<DocumentLinkOptions, DocumentLinkRegistrationOptions, DocumentLinkProvider, DocumentLinkMiddleware> {
    constructor(client: FeatureClient<DocumentLinkMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: DocumentLinkRegistrationOptions): [Disposable, DocumentLinkProvider];
}
