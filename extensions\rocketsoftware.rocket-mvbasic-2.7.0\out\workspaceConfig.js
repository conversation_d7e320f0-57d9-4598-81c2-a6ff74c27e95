"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultCloseCodeLens = defaultCloseCodeLens;
exports.updateCodeLens = updateCodeLens;
exports.defaultFilesAssociations = defaultFilesAssociations;
exports.defaultFilesExclude = defaultFilesExclude;
const vscode = require("vscode");
const env_1 = require("./common/env");
const dataSource = require("./mvClient/dataSource");
/**
 * @param workspaceFolderUri
 *        undefined: open single workspace
 *        vscode.Uri: open workspace folder
 */
function defaultCloseCodeLens(workspaceFolderUri) {
    updateCodeLens(false, workspaceFolderUri);
}
/**
 * Each fold need to can be single control
 * @param isOpen open: true, close: false
 * @param workspaceFolderUri workspace folder uri
 */
function updateCodeLens(isOpen, workspaceFolderUri) {
    var _a, _b;
    // Determine the folder URI to use
    let folderUri = workspaceFolderUri !== null && workspaceFolderUri !== void 0 ? workspaceFolderUri : (_a = vscode.window.activeTextEditor) === null || _a === void 0 ? void 0 : _a.document.uri;
    if (!folderUri || folderUri.scheme === env_1.FS_SCHEMA) {
        const folders = vscode.workspace.workspaceFolders;
        folderUri = (_b = folders === null || folders === void 0 ? void 0 : folders[0]) === null || _b === void 0 ? void 0 : _b.uri;
    }
    // If a valid folder URI is found, proceed with the configuration update
    if (folderUri) {
        const currentFolder = vscode.workspace.getWorkspaceFolder(folderUri);
        if (currentFolder) {
            const rocketMvBasicConfig = vscode.workspace.getConfiguration("[" + env_1.LANGUAGE_ID + "]", currentFolder);
            const codeLensValue = rocketMvBasicConfig["editor.codeLens"];
            // If the setting differs from the desired value, update it
            if (codeLensValue !== isOpen) {
                const codeLensConfig = { "editor.codeLens": isOpen };
                vscode.workspace.getConfiguration("", currentFolder)
                    .update("[" + env_1.LANGUAGE_ID + "]", codeLensConfig, vscode.ConfigurationTarget.WorkspaceFolder, true)
                    .then(() => {
                    vscode.window.showInformationMessage(`CodeLens has been ${isOpen ? 'enabled' : 'disabled'} for ${env_1.LANGUAGE_ID}`);
                }, (err) => {
                    vscode.window.showErrorMessage('Failed to update CodeLens setting: ' + err);
                });
            }
        }
    }
}
/**
 * This configuration for all workspace folds, not to a particular folder
 */
function defaultFilesAssociations(rmvSchema) {
    const jsonItemForHistoryVersion = "**/" + rmvSchema + "/config/**";
    const jsonItem = "**/" + rmvSchema + "/config/*.json";
    const files = vscode.workspace.getConfiguration("files");
    let associations = files.get("associations") || {};
    let isUpdate = false;
    //the history version using this config, due to log config is not a json config file which should not be open by jsonc
    //but now it is replaced by "**/.rmv/config/*.json"
    if (associations[jsonItemForHistoryVersion] != undefined) {
        const newObjs = {};
        for (const key in associations) {
            //delete '**/.rmv/config/**'
            if (key !== jsonItemForHistoryVersion) {
                newObjs[key] = associations[key];
            }
        }
        associations = newObjs;
        isUpdate = true;
    }
    if (associations[jsonItem] == undefined || associations[jsonItem] !== "jsonc") {
        associations[jsonItem] = "jsonc";
        isUpdate = true;
    }
    if (isUpdate == true) {
        files.update('associations', associations, vscode.ConfigurationTarget.Workspace, true);
    }
}
/**
 * This configuration will be add workspace effective for all workspace folds, not to a particular folder
 */
function defaultFilesExclude(rmvSchema) {
    const excludeItemKey = "**/" + rmvSchema + "/catalog/**";
    const files = vscode.workspace.getConfiguration("files");
    const exclude = files.get("exclude") || {};
    let isUpdate = false;
    if (exclude[excludeItemKey] == undefined) {
        exclude[excludeItemKey] = true;
        isUpdate = true;
    }
    if (exclude['*/_*'] == undefined) {
        const ds = dataSource.get();
        exclude['*/_*'] = false;
        if (ds.toUpperCase() === "UNIDATA") {
            exclude['*/_*'] = true;
        }
        isUpdate = true;
    }
    if (isUpdate == true) {
        files.update('exclude', exclude, vscode.ConfigurationTarget.Workspace, true);
    }
}
//# sourceMappingURL=workspaceConfig.js.map