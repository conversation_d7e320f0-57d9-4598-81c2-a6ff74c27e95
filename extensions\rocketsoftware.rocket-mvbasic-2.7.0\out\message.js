"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.get = get;
exports.getContent = getContent;
exports.getComponentName = getComponentName;
exports.getLevel = getLevel;
exports.init = init;
exports.show = show;
const vscode = require("vscode");
let messages = undefined;
/**
 * Get the message object through message id.
 * @param id The id of the message. Please refer [messages.json](../resources/messages.json).
 * @returns The message object if found, otherwise undefined.
 */
function get(id) {
    if (messages === undefined) {
        return undefined;
    }
    return messages.find(msg => msg.id === id);
}
/**
 * Get the message content through message id.
 * @param id The id of the message. Please refer [messages.json](../resources/messages.json).
 * @returns The message content if found, otherwise returns an error message.
 */
function getContent(id) {
    const msg = get(id);
    if (msg !== undefined) {
        return msg.content;
    }
    return "Error: invalid message id ${id}.";
}
function getComponentName(id) {
    const msg = get(id);
    if (msg === undefined) {
        return undefined;
    }
    const components = [
        "Other",
        "Language Server",
        "Extension",
        "MV Server",
        "Configuration",
        "Java",
        "Debug",
        "Compile",
        "OnlineEditing"
    ];
    if (id >= components.length || id < 0) {
        return undefined;
    }
    return components[id];
}
function getLevel(id) {
    const msg = get(id);
    if (msg === undefined) {
        return undefined;
    }
    const levels = [
        "Debug",
        "Info",
        "Warning",
        "Error",
        "Critical"
    ];
    if (id >= levels.length || id < 0) {
        return undefined;
    }
    return levels[id];
}
/**
 * Load message content from [messages.json](../resources/messages.json).
 * @param ctx The context of the extension.
 */
function init(ctx) {
    return __awaiter(this, void 0, void 0, function* () {
        if (messages !== undefined) {
            return;
        }
        const msgFileName = 'messages.json';
        const extensionRootUri = vscode.Uri.file(ctx.extensionPath);
        const msgFileUri = vscode.Uri.joinPath(extensionRootUri, 'resources', msgFileName);
        const status = yield vscode.workspace.fs.stat(msgFileUri);
        if (status === undefined) {
            return;
        }
        try {
            const messagesFileContent = yield vscode.workspace.fs.readFile(msgFileUri);
            let decoder = new TextDecoder('utf-8');
            messages = JSON.parse(decoder.decode(messagesFileContent));
        }
        catch (error) {
            console.error(`Error reading ${msgFileName}: `, error);
            return;
        }
    });
}
/**
 * Display the message in VS Code.
 * @param id Message id.
 * @returns na
 */
function show(id) {
    const msgObject = get(id);
    if (msgObject === undefined) {
        vscode.window.showErrorMessage("");
        return;
    }
    const level = msgObject.level;
    switch (level) {
        case 0:
            // TODO: should put into log file.
            break;
        case 1:
            vscode.window.showInformationMessage(msgObject.content);
            break;
        case 2:
            vscode.window.showWarningMessage(msgObject.content);
            break;
        case 3:
        case 4:
            // TODO: should put into log file.
            vscode.window.showErrorMessage(msgObject.content);
            break;
    }
}
//# sourceMappingURL=message.js.map