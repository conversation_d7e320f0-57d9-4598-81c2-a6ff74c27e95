{"name": "@vscode/debugprotocol", "description": "Npm module with declarations for the Visual Studio Code debug protocol", "version": "1.68.0", "author": "Microsoft Corporation", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-debugadapter-node.git"}, "bugs": {"url": "https://github.com/microsoft/vscode-debugadapter-node/issues"}, "main": "./lib/debugProtocol.js", "typings": "./lib/debugProtocol", "devDependencies": {"typescript": "^4.9.4"}, "scripts": {"prepack": "npm run compile", "compile": "tsc && node lib/generator", "watch": "tsc -w"}}