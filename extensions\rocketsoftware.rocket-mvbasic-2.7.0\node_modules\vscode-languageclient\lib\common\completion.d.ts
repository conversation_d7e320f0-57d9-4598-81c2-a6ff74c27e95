import { TextDocument, Disposable, Position as VPosition, CompletionContext as VCompletionContext, CancellationToken, ProviderResult, CompletionItem as VCompletionItem, CompletionList as VCompletionList, CompletionItemProvider } from 'vscode';
import { ClientCapabilities, CompletionOptions, CompletionRegistrationOptions, DocumentSelector, ServerCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideCompletionItemsSignature {
    (this: void, document: TextDocument, position: VPosition, context: VCompletionContext, token: CancellationToken): ProviderResult<VCompletionItem[] | VCompletionList>;
}
export interface ResolveCompletionItemSignature {
    (this: void, item: VCompletionItem, token: CancellationToken): ProviderResult<VCompletionItem>;
}
export interface CompletionMiddleware {
    provideCompletionItem?: (this: void, document: TextDocument, position: VPosition, context: VCompletionContext, token: CancellationToken, next: ProvideCompletionItemsSignature) => ProviderResult<VCompletionItem[] | VCompletionList>;
    resolveCompletionItem?: (this: void, item: VCompletionItem, token: CancellationToken, next: ResolveCompletionItemSignature) => ProviderResult<VCompletionItem>;
}
export declare class CompletionItemFeature extends TextDocumentLanguageFeature<CompletionOptions, CompletionRegistrationOptions, CompletionItemProvider, CompletionMiddleware> {
    private labelDetailsSupport;
    constructor(client: FeatureClient<CompletionMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: CompletionRegistrationOptions, id: string): [Disposable, CompletionItemProvider];
}
