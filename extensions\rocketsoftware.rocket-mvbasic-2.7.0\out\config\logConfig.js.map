{"version": 3, "file": "logConfig.js", "sourceRoot": "", "sources": ["../../src/config/logConfig.ts"], "names": [], "mappings": ";;;AAAA,6CAAoD;AACpD,yBAAyB;AACzB,6BAA6B;AAC7B,4CAA6C;AAEhC,QAAA,iBAAiB,GAAW,mBAAmB,CAAC;AAChD,QAAA,UAAU,GAAW,YAAY,CAAC;AAClC,QAAA,UAAU,GAAW,YAAY,CAAC;AAClC,QAAA,WAAW,GAAW,aAAa,CAAC;AAEjD,MAAa,SAAS;IAIlB,YAAY,SAAiB;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC1D,CAAC;IAEM,kBAAkB;QACrB,OAAO,wCAAwC,GAAG,IAAI,CAAC,aAAa,CAAC;IACzE,CAAC;IAEO,gBAAgB,CAAC,SAAiB;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,yBAAiB,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAU,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAU,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAW,CAAC,CAAC;QACpD,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YAChC,OAAO,cAAc,CAAC;QAC1B,CAAC;QACD,IAAI,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACnB,CAAC;QACD,IAAI,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACnB,CAAC;QACD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,yBAAa,EAAE,QAAQ,EAAE,yBAAiB,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACrD,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAE7C,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,gBAAgB,CAAC,oBAA4B;QACjD,MAAM,OAAO,GAAuB,IAAA,yBAAgB,GAAE,CAAC;QACvD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,oBAAoB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAClF,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;CAEJ;AA7CD,8BA6CC"}