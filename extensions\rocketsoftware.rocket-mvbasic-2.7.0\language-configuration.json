{
    "comments": {
        // symbol used for single line comment. Remove this entry if your language does not support line comments
        "lineComment": "*"
    },
    // symbols used as brackets
    "brackets": [
        ["{", "}"],
        ["[", "]"],
        ["<", ">"],
        ["(", ")"]
    ],
    // symbols that are auto closed when typing
    "autoClosingPairs": [
        {"open": "<", "close": ">"},
        {"open": "{", "close": "}"},
        {"open": "[", "close": "]"},
        {"open": "(", "close": ")"},
        {"open": "\"","close":  "\"", "notIn": ["string", "comment"]},
        {"open": "'", "close": "'", "notIn": ["string", "comment"]}
    ],
    // symbols that that can be used to surround a selection
    "surroundingPairs": [
        ["<", ">"],
        ["{", "}"],
        ["[", "]"],
        ["(", ")"],
        ["\"", "\""],
        ["'", "'"]
    ],
    "wordPattern": "[\\@\\$\\%\\.\\w]+",
    "indentationRules": {
        //"increaseIndentPattern": { "pattern": "(?!(^\\*|\\REM\\s|!).*)(?<![\\.\\$\\%\\@])\\b((THEN|ELSE|ON\\s+ERROR|LOOP|DO|BEGIN\\s+TRANSACTION|OTHERWISE)|(FOR|(?<!END\\s+)CASE)\\b(?![\\.\\$\\%\\@]).*)$", "flags": "i"},
        //"decreaseIndentPattern": { "pattern": "(?!(^\\*|\\REM\\s|!).*)(?<![\\.\\$\\%\\@])\\b(END|REPEAT|UNTIL|WHILE|NEXT|OTHERWISE$|(?<!BEGIN\\s+)CASE)\\b(?![\\.\\$\\%\\@]).*$", "flags": "i"},
    }
}
