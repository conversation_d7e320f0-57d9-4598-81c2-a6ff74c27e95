import { TextDocument, Disposable, OutputChannel, DiagnosticCollection, Diagnostic as VDiagnostic, Uri, CancellationToken, WorkspaceFolder as VWorkspaceFolder, FileCreateEvent, FileRenameEvent, FileDeleteEvent, FileWillCreateEvent, FileWillRenameEvent, FileWillDeleteEvent, CompletionItemProvider, HoverProvider, SignatureHelpProvider, DefinitionProvider, ReferenceProvider, DocumentHighlightProvider, CodeActionProvider, DocumentFormattingEditProvider, DocumentRangeFormattingEditProvider, OnTypeFormattingEditProvider, RenameProvider, DocumentSymbolProvider, DocumentLinkProvider, DeclarationProvider, FoldingRangeProvider, ImplementationProvider, DocumentColorProvider, SelectionRangeProvider, TypeDefinitionProvider, CallHierarchyProvider, LinkedEditingRangeProvider, TypeHierarchyProvider, WorkspaceSymbolProvider, ProviderResult, TextEdit as VTextEdit } from 'vscode';
import { Message, MessageSignature, ResponseError, RequestType0, RequestType, NotificationType0, NotificationType, ProtocolRequestType, ProtocolRequestType0, RequestHandler, RequestHandler0, GenericRequestHandler, ProtocolNotificationType, ProtocolNotificationType0, NotificationHandler, NotificationHandler0, GenericNotificationHandler, MessageReader, MessageWriter, Trace, Event, InitializeParams, InitializeResult, DocumentSelector, DidChangeTextDocumentNotification, FileEvent, ProgressType, ProgressToken, ShowDocumentRequest, ShowDocumentParams, ShowDocumentResult, CancellationStrategy, InitializeError, WorkDoneProgressBegin, WorkDoneProgressReport, WorkDoneProgressEnd, DidOpenTextDocumentNotification, WillSaveTextDocumentNotification, WillSaveTextDocumentWaitUntilRequest, DidSaveTextDocumentNotification, DidCloseTextDocumentNotification, DidCreateFilesNotification, DidRenameFilesNotification, DidDeleteFilesNotification, WillRenameFilesRequest, WillCreateFilesRequest, WillDeleteFilesRequest, CompletionRequest, HoverRequest, SignatureHelpRequest, DefinitionRequest, ReferencesRequest, DocumentHighlightRequest, CodeActionRequest, CodeLensRequest, DocumentFormattingRequest, DocumentRangeFormattingRequest, DocumentOnTypeFormattingRequest, RenameRequest, DocumentSymbolRequest, DocumentLinkRequest, DocumentColorRequest, DeclarationRequest, FoldingRangeRequest, ImplementationRequest, SelectionRangeRequest, TypeDefinitionRequest, CallHierarchyPrepareRequest, SemanticTokensRegistrationType, LinkedEditingRangeRequest, TypeHierarchyPrepareRequest, InlineValueRequest, InlayHintRequest, WorkspaceSymbolRequest, TextDocumentRegistrationOptions, FileOperationRegistrationOptions, DocumentDiagnosticRequest, NotebookDocumentSyncRegistrationType, NotebookDocumentSyncRegistrationOptions, MessageStrategy } from 'vscode-languageserver-protocol';
import * as c2p from './codeConverter';
import * as p2c from './protocolConverter';
import { DynamicFeature, FeatureClient, TextDocumentSendFeature, StaticFeature, TextDocumentProviderFeature, WorkspaceProviderFeature } from './features';
import { DiagnosticProviderMiddleware, DiagnosticProviderShape, $DiagnosticPullOptions } from './diagnostic';
import { NotebookDocumentMiddleware, $NotebookDocumentOptions, NotebookDocumentProviderShape } from './notebook';
import { ConfigurationMiddleware, $ConfigurationOptions, DidChangeConfigurationMiddleware } from './configuration';
import { DidChangeTextDocumentFeatureShape, DidCloseTextDocumentFeatureShape, DidOpenTextDocumentFeatureShape, DidSaveTextDocumentFeatureShape, TextDocumentSynchronizationMiddleware } from './textSynchronization';
import { CompletionMiddleware } from './completion';
import { HoverMiddleware } from './hover';
import { DefinitionMiddleware } from './definition';
import { SignatureHelpMiddleware } from './signatureHelp';
import { DocumentHighlightMiddleware } from './documentHighlight';
import { DocumentSymbolMiddleware } from './documentSymbol';
import { WorkspaceSymbolMiddleware } from './workspaceSymbol';
import { ReferencesMiddleware } from './reference';
import { TypeDefinitionMiddleware } from './typeDefinition';
import { ImplementationMiddleware } from './implementation';
import { ColorProviderMiddleware } from './colorProvider';
import { CodeActionMiddleware } from './codeAction';
import { CodeLensMiddleware, CodeLensProviderShape } from './codeLens';
import { FormattingMiddleware } from './formatting';
import { RenameMiddleware } from './rename';
import { DocumentLinkMiddleware } from './documentLink';
import { ExecuteCommandMiddleware } from './executeCommand';
import { FoldingRangeProviderMiddleware } from './foldingRange';
import { DeclarationMiddleware } from './declaration';
import { SelectionRangeProviderMiddleware } from './selectionRange';
import { CallHierarchyMiddleware } from './callHierarchy';
import { SemanticTokensMiddleware, SemanticTokensProviderShape } from './semanticTokens';
import { LinkedEditingRangeMiddleware } from './linkedEditingRange';
import { TypeHierarchyMiddleware } from './typeHierarchy';
import { InlineValueMiddleware, InlineValueProviderShape } from './inlineValue';
import { InlayHintsMiddleware, InlayHintsProviderShape } from './inlayHint';
import { WorkspaceFolderMiddleware } from './workspaceFolder';
import { FileOperationsMiddleware } from './fileOperations';
/**
 * Controls when the output channel is revealed.
 */
export declare enum RevealOutputChannelOn {
    Info = 1,
    Warn = 2,
    Error = 3,
    Never = 4
}
/**
 * A handler that is invoked when the initialization of the server failed.
 */
export declare type InitializationFailedHandler = 
/**
 * @param error The error returned from the server
 * @returns if true is returned the client tries to reinitialize the server.
 *  Implementors of a handler are responsible to not initialize the server
 *  infinitely. Return false if initialization should stop and an error
 *  should be reported.
 */
(error: ResponseError<InitializeError> | Error | any) => boolean;
/**
 * An action to be performed when the connection is producing errors.
 */
export declare enum ErrorAction {
    /**
     * Continue running the server.
     */
    Continue = 1,
    /**
     * Shutdown the server.
     */
    Shutdown = 2
}
export declare type ErrorHandlerResult = {
    /**
     * The action to take.
     */
    action: ErrorAction;
    /**
     * An optional message to be presented to the user.
     */
    message?: string;
    /**
     * If set to true the client assumes that the corresponding
     * error handler has presented an appropriate message to the
     * user and the message will only be log to the client's
     * output channel.
     */
    handled?: boolean;
};
/**
 * An action to be performed when the connection to a server got closed.
 */
export declare enum CloseAction {
    /**
     * Don't restart the server. The connection stays closed.
     */
    DoNotRestart = 1,
    /**
     * Restart the server.
     */
    Restart = 2
}
export declare type CloseHandlerResult = {
    /**
     * The action to take.
     */
    action: CloseAction;
    /**
     * An optional message to be presented to the user.
     */
    message?: string;
    /**
     * If set to true the client assumes that the corresponding
     * close handler has presented an appropriate message to the
     * user and the message will only be log to the client's
     * output channel.
     */
    handled?: boolean;
};
/**
 * A plugable error handler that is invoked when the connection is either
 * producing errors or got closed.
 */
export interface ErrorHandler {
    /**
     * An error has occurred while writing or reading from the connection.
     *
     * @param error - the error received
     * @param message - the message to be delivered to the server if know.
     * @param count - a count indicating how often an error is received. Will
     *  be reset if a message got successfully send or received.
     */
    error(error: Error, message: Message | undefined, count: number | undefined): ErrorHandlerResult | Promise<ErrorHandlerResult>;
    /**
     * The connection to the server got closed.
     */
    closed(): CloseHandlerResult | Promise<CloseHandlerResult>;
}
/**
 * Signals in which state the language client is in.
 */
export declare enum State {
    /**
     * The client is stopped or got never started.
     */
    Stopped = 1,
    /**
     * The client is starting but not ready yet.
     */
    Starting = 3,
    /**
     * The client is running and ready.
     */
    Running = 2
}
/**
 * An event signaling a state change.
 */
export interface StateChangeEvent {
    oldState: State;
    newState: State;
}
export declare enum SuspendMode {
    /**
     * Don't allow suspend mode.
     */
    off = "off",
    /**
     * Support suspend mode even if not all
     * registered providers have a corresponding
     * activation listener.
     */
    on = "on"
}
export declare type SuspendOptions = {
    /**
     * Whether suspend mode is supported. If suspend mode is allowed
     * the client will stop a running server when going into suspend mode.
     * If omitted defaults to SuspendMode.off;
     */
    mode?: SuspendMode;
    /**
     * A callback that is invoked before actually suspending
     * the server. If `false` is returned the client will not continue
     * suspending the server.
     */
    callback?: () => Promise<boolean>;
    /**
     * The interval in milliseconds used to check if the server
     * can be suspended. If the check passes three times in a row
     * (e.g. the server can be suspended for 3 * interval ms) the
     * server is suspended. Defaults to 60000ms, which is also the
     * minimum allowed value.
     */
    interval?: number;
};
export interface DidChangeWatchedFileSignature {
    (this: void, event: FileEvent): Promise<void>;
}
declare type _WorkspaceMiddleware = {
    didChangeWatchedFile?: (this: void, event: FileEvent, next: DidChangeWatchedFileSignature) => Promise<void>;
};
export declare type WorkspaceMiddleware = _WorkspaceMiddleware & ConfigurationMiddleware & DidChangeConfigurationMiddleware & WorkspaceFolderMiddleware & FileOperationsMiddleware;
interface _WindowMiddleware {
    showDocument?: (this: void, params: ShowDocumentParams, next: ShowDocumentRequest.HandlerSignature) => Promise<ShowDocumentResult>;
}
export declare type WindowMiddleware = _WindowMiddleware;
export interface HandleDiagnosticsSignature {
    (this: void, uri: Uri, diagnostics: VDiagnostic[]): void;
}
export interface HandleWorkDoneProgressSignature {
    (this: void, token: ProgressToken, params: WorkDoneProgressBegin | WorkDoneProgressReport | WorkDoneProgressEnd): void;
}
interface _Middleware {
    handleDiagnostics?: (this: void, uri: Uri, diagnostics: VDiagnostic[], next: HandleDiagnosticsSignature) => void;
    handleWorkDoneProgress?: (this: void, token: ProgressToken, params: WorkDoneProgressBegin | WorkDoneProgressReport | WorkDoneProgressEnd, next: HandleWorkDoneProgressSignature) => void;
    workspace?: WorkspaceMiddleware;
    window?: WindowMiddleware;
}
/**
 * The Middleware lets extensions intercept the request and notifications send and received
 * from the server
 */
export declare type Middleware = _Middleware & TextDocumentSynchronizationMiddleware & CompletionMiddleware & HoverMiddleware & DefinitionMiddleware & SignatureHelpMiddleware & DocumentHighlightMiddleware & DocumentSymbolMiddleware & WorkspaceSymbolMiddleware & ReferencesMiddleware & TypeDefinitionMiddleware & ImplementationMiddleware & ColorProviderMiddleware & CodeActionMiddleware & CodeLensMiddleware & FormattingMiddleware & RenameMiddleware & DocumentLinkMiddleware & ExecuteCommandMiddleware & FoldingRangeProviderMiddleware & DeclarationMiddleware & SelectionRangeProviderMiddleware & CallHierarchyMiddleware & SemanticTokensMiddleware & LinkedEditingRangeMiddleware & TypeHierarchyMiddleware & InlineValueMiddleware & InlayHintsMiddleware & NotebookDocumentMiddleware & DiagnosticProviderMiddleware;
export declare type LanguageClientOptions = {
    documentSelector?: DocumentSelector | string[];
    diagnosticCollectionName?: string;
    outputChannel?: OutputChannel;
    outputChannelName?: string;
    traceOutputChannel?: OutputChannel;
    revealOutputChannelOn?: RevealOutputChannelOn;
    /**
     * The encoding use to read stdout and stderr. Defaults
     * to 'utf8' if omitted.
     */
    stdioEncoding?: string;
    initializationOptions?: any | (() => any);
    initializationFailedHandler?: InitializationFailedHandler;
    progressOnInitialization?: boolean;
    errorHandler?: ErrorHandler;
    middleware?: Middleware;
    uriConverters?: {
        code2Protocol: c2p.URIConverter;
        protocol2Code: p2c.URIConverter;
    };
    workspaceFolder?: VWorkspaceFolder;
    connectionOptions?: {
        cancellationStrategy?: CancellationStrategy;
        messageStrategy?: MessageStrategy;
        maxRestartCount?: number;
    };
    markdown?: {
        isTrusted?: boolean;
        supportHtml?: boolean;
    };
} & $NotebookDocumentOptions & $DiagnosticPullOptions & $ConfigurationOptions;
export interface MessageTransports {
    reader: MessageReader;
    writer: MessageWriter;
    detached?: boolean;
}
export declare namespace MessageTransports {
    function is(value: any): value is MessageTransports;
}
export declare abstract class BaseLanguageClient implements FeatureClient<Middleware, LanguageClientOptions> {
    private _id;
    private _name;
    private _clientOptions;
    private _state;
    private _onStart;
    private _onStop;
    private _connection;
    private _idleInterval;
    private readonly _ignoredRegistrations;
    private readonly _listeners;
    private _disposed;
    private readonly _notificationHandlers;
    private readonly _notificationDisposables;
    private readonly _pendingNotificationHandlers;
    private readonly _requestHandlers;
    private readonly _requestDisposables;
    private readonly _pendingRequestHandlers;
    private readonly _progressHandlers;
    private readonly _pendingProgressHandlers;
    private readonly _progressDisposables;
    private _initializeResult;
    private _outputChannel;
    private _disposeOutputChannel;
    private _traceOutputChannel;
    private _capabilities;
    private _diagnostics;
    private _syncedDocuments;
    private _didChangeTextDocumentFeature;
    private readonly _pendingOpenNotifications;
    private readonly _pendingChangeSemaphore;
    private readonly _pendingChangeDelayer;
    private _fileEvents;
    private _fileEventDelayer;
    private _telemetryEmitter;
    private _stateChangeEmitter;
    private _trace;
    private _traceFormat;
    private _tracer;
    private readonly _c2p;
    private readonly _p2c;
    constructor(id: string, name: string, clientOptions: LanguageClientOptions);
    get name(): string;
    get middleware(): Middleware;
    get clientOptions(): LanguageClientOptions;
    get protocol2CodeConverter(): p2c.Converter;
    get code2ProtocolConverter(): c2p.Converter;
    get onTelemetry(): Event<any>;
    get onDidChangeState(): Event<StateChangeEvent>;
    get outputChannel(): OutputChannel;
    get traceOutputChannel(): OutputChannel;
    get diagnostics(): DiagnosticCollection | undefined;
    get state(): State;
    private get $state();
    private set $state(value);
    private getPublicState;
    get initializeResult(): InitializeResult | undefined;
    sendRequest<R, PR, E, RO>(type: ProtocolRequestType0<R, PR, E, RO>, token?: CancellationToken): Promise<R>;
    sendRequest<P, R, PR, E, RO>(type: ProtocolRequestType<P, R, PR, E, RO>, params: P, token?: CancellationToken): Promise<R>;
    sendRequest<R, E>(type: RequestType0<R, E>, token?: CancellationToken): Promise<R>;
    sendRequest<P, R, E>(type: RequestType<P, R, E>, params: P, token?: CancellationToken): Promise<R>;
    sendRequest<R>(method: string, token?: CancellationToken): Promise<R>;
    sendRequest<R>(method: string, param: any, token?: CancellationToken): Promise<R>;
    onRequest<R, PR, E, RO>(type: ProtocolRequestType0<R, PR, E, RO>, handler: RequestHandler0<R, E>): Disposable;
    onRequest<P, R, PR, E, RO>(type: ProtocolRequestType<P, R, PR, E, RO>, handler: RequestHandler<P, R, E>): Disposable;
    onRequest<R, E>(type: RequestType0<R, E>, handler: RequestHandler0<R, E>): Disposable;
    onRequest<P, R, E>(type: RequestType<P, R, E>, handler: RequestHandler<P, R, E>): Disposable;
    onRequest<R, E>(method: string, handler: GenericRequestHandler<R, E>): Disposable;
    sendNotification<RO>(type: ProtocolNotificationType0<RO>): Promise<void>;
    sendNotification<P, RO>(type: ProtocolNotificationType<P, RO>, params?: P): Promise<void>;
    sendNotification(type: NotificationType0): Promise<void>;
    sendNotification<P>(type: NotificationType<P>, params?: P): Promise<void>;
    sendNotification(method: string): Promise<void>;
    sendNotification(method: string, params: any): Promise<void>;
    onNotification<RO>(type: ProtocolNotificationType0<RO>, handler: NotificationHandler0): Disposable;
    onNotification<P, RO>(type: ProtocolNotificationType<P, RO>, handler: NotificationHandler<P>): Disposable;
    onNotification(type: NotificationType0, handler: NotificationHandler0): Disposable;
    onNotification<P>(type: NotificationType<P>, handler: NotificationHandler<P>): Disposable;
    onNotification(method: string, handler: GenericNotificationHandler): Disposable;
    sendProgress<P>(type: ProgressType<P>, token: string | number, value: P): Promise<void>;
    onProgress<P>(type: ProgressType<P>, token: string | number, handler: NotificationHandler<P>): Disposable;
    createDefaultErrorHandler(maxRestartCount?: number): ErrorHandler;
    setTrace(value: Trace): Promise<void>;
    private data2String;
    info(message: string, data?: any, showNotification?: boolean): void;
    warn(message: string, data?: any, showNotification?: boolean): void;
    error(message: string, data?: any, showNotification?: boolean | 'force'): void;
    private showNotificationMessage;
    private logTrace;
    private logObjectTrace;
    needsStart(): boolean;
    needsStop(): boolean;
    private activeConnection;
    isRunning(): boolean;
    start(): Promise<void>;
    private createOnStartPromise;
    private initialize;
    private doInitialize;
    private _clientGetRootPath;
    stop(timeout?: number): Promise<void>;
    dispose(timeout?: number): Promise<void>;
    private shutdown;
    private cleanUp;
    private cleanUpChannel;
    private notifyFileEvent;
    private sendPendingFullTextDocumentChanges;
    private triggerPendingChangeDelivery;
    private _diagnosticQueue;
    private _diagnosticQueueState;
    private handleDiagnostics;
    private triggerDiagnosticQueue;
    private workDiagnosticQueue;
    private setDiagnostics;
    protected getLocale(): string;
    protected abstract createMessageTransports(encoding: string): Promise<MessageTransports>;
    private $start;
    private createConnection;
    protected handleConnectionClosed(): Promise<void>;
    private handleConnectionError;
    private hookConfigurationChanged;
    private refreshTrace;
    private hookFileEvents;
    private readonly _features;
    private readonly _dynamicFeatures;
    registerFeatures(features: (StaticFeature | DynamicFeature<any>)[]): void;
    registerFeature(feature: StaticFeature | DynamicFeature<any>): void;
    getFeature(request: typeof DidOpenTextDocumentNotification.method): DidOpenTextDocumentFeatureShape;
    getFeature(request: typeof DidChangeTextDocumentNotification.method): DidChangeTextDocumentFeatureShape;
    getFeature(request: typeof WillSaveTextDocumentNotification.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentSendFeature<(textDocument: TextDocument) => Promise<void>>;
    getFeature(request: typeof WillSaveTextDocumentWaitUntilRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentSendFeature<(textDocument: TextDocument) => ProviderResult<VTextEdit[]>>;
    getFeature(request: typeof DidSaveTextDocumentNotification.method): DidSaveTextDocumentFeatureShape;
    getFeature(request: typeof DidCloseTextDocumentNotification.method): DidCloseTextDocumentFeatureShape;
    getFeature(request: typeof DidCreateFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileCreateEvent) => Promise<void>;
    };
    getFeature(request: typeof DidRenameFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileRenameEvent) => Promise<void>;
    };
    getFeature(request: typeof DidDeleteFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileDeleteEvent) => Promise<void>;
    };
    getFeature(request: typeof WillCreateFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillCreateEvent) => Promise<void>;
    };
    getFeature(request: typeof WillRenameFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillRenameEvent) => Promise<void>;
    };
    getFeature(request: typeof WillDeleteFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillDeleteEvent) => Promise<void>;
    };
    getFeature(request: typeof CompletionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CompletionItemProvider>;
    getFeature(request: typeof HoverRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<HoverProvider>;
    getFeature(request: typeof SignatureHelpRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SignatureHelpProvider>;
    getFeature(request: typeof DefinitionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DefinitionProvider>;
    getFeature(request: typeof ReferencesRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<ReferenceProvider>;
    getFeature(request: typeof DocumentHighlightRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentHighlightProvider>;
    getFeature(request: typeof CodeActionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CodeActionProvider>;
    getFeature(request: typeof CodeLensRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CodeLensProviderShape>;
    getFeature(request: typeof DocumentFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentFormattingEditProvider>;
    getFeature(request: typeof DocumentRangeFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentRangeFormattingEditProvider>;
    getFeature(request: typeof DocumentOnTypeFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<OnTypeFormattingEditProvider>;
    getFeature(request: typeof RenameRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<RenameProvider>;
    getFeature(request: typeof DocumentSymbolRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentSymbolProvider>;
    getFeature(request: typeof DocumentLinkRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentLinkProvider>;
    getFeature(request: typeof DocumentColorRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentColorProvider>;
    getFeature(request: typeof DeclarationRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DeclarationProvider>;
    getFeature(request: typeof FoldingRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<FoldingRangeProvider>;
    getFeature(request: typeof ImplementationRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<ImplementationProvider>;
    getFeature(request: typeof SelectionRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SelectionRangeProvider>;
    getFeature(request: typeof TypeDefinitionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<TypeDefinitionProvider>;
    getFeature(request: typeof CallHierarchyPrepareRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CallHierarchyProvider>;
    getFeature(request: typeof SemanticTokensRegistrationType.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SemanticTokensProviderShape>;
    getFeature(request: typeof LinkedEditingRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<LinkedEditingRangeProvider>;
    getFeature(request: typeof TypeHierarchyPrepareRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<TypeHierarchyProvider>;
    getFeature(request: typeof InlineValueRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<InlineValueProviderShape>;
    getFeature(request: typeof InlayHintRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<InlayHintsProviderShape>;
    getFeature(request: typeof WorkspaceSymbolRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & WorkspaceProviderFeature<WorkspaceSymbolProvider>;
    getFeature(request: typeof DocumentDiagnosticRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DiagnosticProviderShape> | undefined;
    getFeature(request: typeof NotebookDocumentSyncRegistrationType.method): DynamicFeature<NotebookDocumentSyncRegistrationOptions> & NotebookDocumentProviderShape | undefined;
    hasDedicatedTextSynchronizationFeature(textDocument: TextDocument): boolean;
    protected registerBuiltinFeatures(): void;
    registerProposedFeatures(): void;
    protected fillInitializeParams(params: InitializeParams): void;
    private computeClientCapabilities;
    private initializeFeatures;
    private handleRegistrationRequest;
    private handleUnregistrationRequest;
    private workspaceEditLock;
    private handleApplyWorkspaceEdit;
    private static RequestsToCancelOnContentModified;
    private static CancellableResolveCalls;
    handleFailedRequest<T>(type: MessageSignature, token: CancellationToken | undefined, error: any, defaultValue: T, showNotification?: boolean): T;
}
export declare namespace ProposedFeatures {
    function createAll(_client: FeatureClient<Middleware, LanguageClientOptions>): (StaticFeature | DynamicFeature<any>)[];
}
export {};
