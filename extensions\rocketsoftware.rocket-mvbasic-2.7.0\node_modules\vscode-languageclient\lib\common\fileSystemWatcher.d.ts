import { FileSystemWatcher as VFileSystemWatcher } from 'vscode';
import { ClientCapabilities, DidChangeWatchedFilesRegistrationOptions, DocumentSelector, FileEvent, RegistrationType, ServerCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, DynamicFeature, RegistrationData, FeatureState } from './features';
export declare class FileSystemWatcherFeature implements DynamicFeature<DidChangeWatchedFilesRegistrationOptions> {
    private readonly _client;
    private readonly _notifyFileEvent;
    private readonly _watchers;
    constructor(client: FeatureClient<object>, notifyFileEvent: (event: FileEvent) => void);
    getState(): FeatureState;
    get registrationType(): RegistrationType<DidChangeWatchedFilesRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(_capabilities: ServerCapabilities, _documentSelector: DocumentSelector): void;
    register(data: RegistrationData<DidChangeWatchedFilesRegistrationOptions>): void;
    registerRaw(id: string, fileSystemWatchers: VFileSystemWatcher[]): void;
    private hookListeners;
    unregister(id: string): void;
    dispose(): void;
}
