import * as vscode from 'vscode';
import * as proto from 'vscode-languageserver-protocol';
import { NotebookCellTextDocumentFilter } from 'vscode-languageserver-protocol';
import { DynamicFeature, FeatureClient, RegistrationData, FeatureState } from './features';
export declare type VNotebookDocumentChangeEvent = {
    /**
     * The notebook document
     */
    notebook: vscode.NotebookDocument;
    /**
     * The changed meta data if any.
     */
    metadata?: {
        [key: string]: any;
    };
    /**
     * Changes to cells.
     */
    cells?: {
        /**
        * Changes to the cell structure to add or
        * remove cells.
        */
        structure?: {
            /**
             * The change to the cell array.
             */
            array: {
                start: number;
                deleteCount: number;
                cells?: vscode.NotebookCell[];
            };
            /**
             * Additional opened cell text documents.
             */
            didOpen?: vscode.NotebookCell[];
            /**
             * Additional closed cell text documents.
             */
            didClose?: vscode.NotebookCell[];
        };
        /**
         * Changes to notebook cells properties like its
         * kind or metadata.
         */
        data?: vscode.NotebookCell[];
        /**
         * Changes to the text content of notebook cells.
         */
        textContent?: vscode.TextDocumentChangeEvent[];
    };
};
export declare type NotebookDocumentOptions = {
    filterCells?(notebookDocument: vscode.NotebookDocument, cells: vscode.NotebookCell[]): vscode.NotebookCell[];
};
export declare type $NotebookDocumentOptions = {
    notebookDocumentOptions?: NotebookDocumentOptions;
};
export declare type NotebookDocumentMiddleware = {
    notebooks?: {
        didOpen?: (this: void, notebookDocument: vscode.NotebookDocument, cells: vscode.NotebookCell[], next: (this: void, notebookDocument: vscode.NotebookDocument, cells: vscode.NotebookCell[]) => Promise<void>) => Promise<void>;
        didSave?: (this: void, notebookDocument: vscode.NotebookDocument, next: (this: void, notebookDocument: vscode.NotebookDocument) => Promise<void>) => Promise<void>;
        didChange?: (this: void, event: VNotebookDocumentChangeEvent, next: (this: void, event: VNotebookDocumentChangeEvent) => Promise<void>) => Promise<void>;
        didClose?: (this: void, notebookDocument: vscode.NotebookDocument, cells: vscode.NotebookCell[], next: (this: void, notebookDocument: vscode.NotebookDocument, cells: vscode.NotebookCell[]) => Promise<void>) => Promise<void>;
    };
};
export interface NotebookDocumentSyncFeatureShape {
    sendDidOpenNotebookDocument(notebookDocument: vscode.NotebookDocument): Promise<void>;
    sendDidSaveNotebookDocument(notebookDocument: vscode.NotebookDocument): Promise<void>;
    sendDidChangeNotebookDocument(event: VNotebookDocumentChangeEvent): Promise<void>;
    sendDidCloseNotebookDocument(notebookDocument: vscode.NotebookDocument): Promise<void>;
}
export declare type $NotebookCellTextDocumentFilter = NotebookCellTextDocumentFilter & {
    sync: true;
};
export declare type NotebookDocumentProviderShape = {
    getProvider(notebookCell: vscode.NotebookCell): NotebookDocumentSyncFeatureShape | undefined;
};
export declare class NotebookDocumentSyncFeature implements DynamicFeature<proto.NotebookDocumentSyncRegistrationOptions>, NotebookDocumentProviderShape {
    static readonly CellScheme: string;
    private readonly client;
    private readonly registrations;
    private dedicatedChannel;
    constructor(client: FeatureClient<NotebookDocumentMiddleware, $NotebookDocumentOptions>);
    getState(): FeatureState;
    readonly registrationType: proto.RegistrationType<proto.NotebookDocumentSyncRegistrationOptions>;
    fillClientCapabilities(capabilities: proto.ClientCapabilities): void;
    preInitialize(capabilities: proto.ServerCapabilities<any>): void;
    initialize(capabilities: proto.ServerCapabilities<any>): void;
    register(data: RegistrationData<proto.NotebookDocumentSyncRegistrationOptions>): void;
    unregister(id: string): void;
    dispose(): void;
    handles(textDocument: vscode.TextDocument): boolean;
    getProvider(notebookCell: vscode.NotebookCell): NotebookDocumentSyncFeatureShape | undefined;
    private findNotebookDocumentAndCell;
}
