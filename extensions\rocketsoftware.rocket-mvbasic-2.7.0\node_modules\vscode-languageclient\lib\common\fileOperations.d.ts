import * as code from 'vscode';
import * as proto from 'vscode-languageserver-protocol';
import { DynamicFeature, RegistrationData, NextSignature, FeatureState, FeatureClient } from './features';
/**
 * File operation middleware
 *
 * @since 3.16.0
 */
export interface FileOperationsMiddleware {
    didCreateFiles?: NextSignature<code.FileCreateEvent, Promise<void>>;
    willCreateFiles?: NextSignature<code.FileWillCreateEvent, Thenable<code.WorkspaceEdit | null | undefined>>;
    didRenameFiles?: NextSignature<code.FileRenameEvent, Promise<void>>;
    willRenameFiles?: NextSignature<code.FileWillRenameEvent, Thenable<code.WorkspaceEdit | null | undefined>>;
    didDeleteFiles?: NextSignature<code.FileDeleteEvent, Promise<void>>;
    willDeleteFiles?: NextSignature<code.FileWillDeleteEvent, Thenable<code.WorkspaceEdit | null | undefined>>;
}
interface FileOperationsWorkspaceMiddleware {
    workspace?: FileOperationsMiddleware;
}
interface Event<I> {
    readonly files: ReadonlyArray<I>;
}
declare abstract class FileOperationFeature<I, E extends Event<I>> implements DynamicFeature<proto.FileOperationRegistrationOptions> {
    protected readonly _client: FeatureClient<FileOperationsWorkspaceMiddleware>;
    private readonly _event;
    private readonly _registrationType;
    private readonly _clientCapability;
    private readonly _serverCapability;
    private _listener;
    private readonly _filters;
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>, event: code.Event<E>, registrationType: proto.RegistrationType<proto.FileOperationRegistrationOptions>, clientCapability: keyof proto.FileOperationClientCapabilities, serverCapability: keyof proto.FileOperationOptions);
    getState(): FeatureState;
    protected filterSize(): number;
    get registrationType(): proto.RegistrationType<proto.FileOperationRegistrationOptions>;
    fillClientCapabilities(capabilities: proto.ClientCapabilities): void;
    initialize(capabilities: proto.ServerCapabilities): void;
    register(data: RegistrationData<proto.FileOperationRegistrationOptions>): void;
    abstract send(data: E): Promise<void>;
    unregister(id: string): void;
    dispose(): void;
    protected getFileType(uri: code.Uri): Promise<code.FileType | undefined>;
    protected filter(event: E, prop: (i: I) => code.Uri): Promise<E>;
    protected static getFileType(uri: code.Uri): Promise<code.FileType | undefined>;
    private static asMinimatchOptions;
}
declare abstract class NotificationFileOperationFeature<I, E extends {
    readonly files: ReadonlyArray<I>;
}, P> extends FileOperationFeature<I, E> {
    private _notificationType;
    private _accessUri;
    private _createParams;
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>, event: code.Event<E>, notificationType: proto.ProtocolNotificationType<P, proto.FileOperationRegistrationOptions>, clientCapability: keyof proto.FileOperationClientCapabilities, serverCapability: keyof proto.FileOperationOptions, accessUri: (i: I) => code.Uri, createParams: (e: E) => P);
    send(originalEvent: E): Promise<void>;
    protected abstract doSend(event: E, next: (event: E) => Promise<void>): Promise<void>;
}
declare abstract class CachingNotificationFileOperationFeature<I, E extends {
    readonly files: ReadonlyArray<I>;
}, P> extends NotificationFileOperationFeature<I, E, P> {
    protected _willListener: code.Disposable | undefined;
    private readonly _fsPathFileTypes;
    protected getFileType(uri: code.Uri): Promise<code.FileType | undefined>;
    protected cacheFileTypes(event: E, prop: (i: I) => code.Uri): Promise<void>;
    protected clearFileTypeCache(): void;
    unregister(id: string): void;
    dispose(): void;
}
export declare class DidCreateFilesFeature extends NotificationFileOperationFeature<code.Uri, code.FileCreateEvent, proto.CreateFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    protected doSend(event: code.FileCreateEvent, next: (event: code.FileCreateEvent) => Promise<void>): Promise<void>;
}
export declare class DidRenameFilesFeature extends CachingNotificationFileOperationFeature<{
    oldUri: code.Uri;
    newUri: code.Uri;
}, code.FileRenameEvent, proto.RenameFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    register(data: RegistrationData<proto.FileOperationRegistrationOptions>): void;
    private willRename;
    protected doSend(event: code.FileRenameEvent, next: (event: code.FileRenameEvent) => Promise<void>): Promise<void>;
}
export declare class DidDeleteFilesFeature extends CachingNotificationFileOperationFeature<code.Uri, code.FileDeleteEvent, proto.DeleteFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    register(data: RegistrationData<proto.FileOperationRegistrationOptions>): void;
    private willDelete;
    protected doSend(event: code.FileCreateEvent, next: (event: code.FileCreateEvent) => Promise<void>): Promise<void>;
}
interface RequestEvent<I> {
    readonly token: code.CancellationToken;
    readonly files: ReadonlyArray<I>;
    waitUntil(thenable: Thenable<code.WorkspaceEdit>): void;
    waitUntil(thenable: Thenable<any>): void;
}
declare abstract class RequestFileOperationFeature<I, E extends RequestEvent<I>, P> extends FileOperationFeature<I, E> {
    private _requestType;
    private _accessUri;
    private _createParams;
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>, event: code.Event<E>, requestType: proto.ProtocolRequestType<P, proto.WorkspaceEdit | null, never, void, proto.FileOperationRegistrationOptions>, clientCapability: keyof proto.FileOperationClientCapabilities, serverCapability: keyof proto.FileOperationOptions, accessUri: (i: I) => code.Uri, createParams: (e: Event<I>) => P);
    send(originalEvent: E & RequestEvent<I>): Promise<void>;
    private waitUntil;
    protected abstract doSend(event: E, next: (event: RequestEvent<I>) => Thenable<code.WorkspaceEdit> | Thenable<any>): Thenable<code.WorkspaceEdit> | Thenable<any>;
}
export declare class WillCreateFilesFeature extends RequestFileOperationFeature<code.Uri, code.FileWillCreateEvent, proto.CreateFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    protected doSend(event: code.FileWillCreateEvent, next: (event: code.FileWillCreateEvent) => Thenable<code.WorkspaceEdit> | Thenable<any>): Thenable<code.WorkspaceEdit> | Thenable<any>;
}
export declare class WillRenameFilesFeature extends RequestFileOperationFeature<{
    oldUri: code.Uri;
    newUri: code.Uri;
}, code.FileWillRenameEvent, proto.RenameFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    protected doSend(event: code.FileWillRenameEvent, next: (event: code.FileWillRenameEvent) => Thenable<code.WorkspaceEdit> | Thenable<any>): Thenable<code.WorkspaceEdit> | Thenable<any>;
}
export declare class WillDeleteFilesFeature extends RequestFileOperationFeature<code.Uri, code.FileWillDeleteEvent, proto.DeleteFilesParams> {
    constructor(client: FeatureClient<FileOperationsWorkspaceMiddleware>);
    protected doSend(event: code.FileWillDeleteEvent, next: (event: code.FileWillDeleteEvent) => Thenable<code.WorkspaceEdit> | Thenable<any>): Thenable<code.WorkspaceEdit> | Thenable<any>;
}
export {};
