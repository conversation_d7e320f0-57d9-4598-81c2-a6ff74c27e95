import { <PERSON><PERSON><PERSON><PERSON>, MessageWriter, Logger, ConnectionStrategy, ConnectionOptions, ProtocolConnection } from '../common/api';
export * from 'vscode-jsonrpc/browser';
export * from '../common/api';
export declare function createProtocolConnection(reader: <PERSON><PERSON><PERSON><PERSON>, writer: MessageWriter, logger?: Logger, options?: ConnectionStrategy | ConnectionOptions): ProtocolConnection;
