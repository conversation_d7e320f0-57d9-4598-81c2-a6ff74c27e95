import { WorkspaceFolder as VWorkspaceFolder, WorkspaceFoldersChangeEvent as VWorkspaceFoldersChangeEvent } from 'vscode';
import { DynamicFeature, RegistrationData, FeatureClient, NextSignature, FeatureState } from './features';
import { ClientCapabilities, InitializeParams, ServerCapabilities, WorkspaceFoldersRequest, RegistrationType } from 'vscode-languageserver-protocol';
export declare function arrayDiff<T>(left: ReadonlyArray<T>, right: ReadonlyArray<T>): T[];
export declare type WorkspaceFolderMiddleware = {
    workspaceFolders?: WorkspaceFoldersRequest.MiddlewareSignature;
    didChangeWorkspaceFolders?: NextSignature<VWorkspaceFoldersChangeEvent, Promise<void>>;
};
declare type WorkspaceFolderWorkspaceMiddleware = {
    workspace?: WorkspaceFolderMiddleware;
};
export declare class WorkspaceFoldersFeature implements DynamicFeature<void> {
    private readonly _client;
    private readonly _listeners;
    private _initialFolders;
    constructor(client: FeatureClient<WorkspaceFolderWorkspaceMiddleware>);
    getState(): FeatureState;
    get registrationType(): RegistrationType<void>;
    fillInitializeParams(params: InitializeParams): void;
    protected initializeWithFolders(currentWorkspaceFolders: ReadonlyArray<VWorkspaceFolder> | undefined): void;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities): void;
    protected sendInitialEvent(currentWorkspaceFolders: ReadonlyArray<VWorkspaceFolder> | undefined): void;
    private doSendEvent;
    register(data: RegistrationData<undefined>): void;
    unregister(id: string): void;
    dispose(): void;
    private asProtocol;
}
export {};
