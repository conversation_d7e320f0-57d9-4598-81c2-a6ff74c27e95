{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"\n\nAre you sure you want to discard ALL changes in {0} files?": "\n您確定要捨棄 {0} 檔案中的所有變更嗎？", "\n\nAre you sure you want to discard changes in '{0}'?": "\n\n確定要捨棄 [{0}] 中的變更?", "\"{0}\" has fingerprint \"{1}\"": "「{0}」具有指紋「{1}」。", "$(info) Remote \"{0}\" has no tags.": "$(info) 遠端 \"{0}\" 沒有標籤。", "$(info) This repository has no stashes.": "$(info) 此存放庫沒有隱藏項目。", "$(info) This repository has no tags.": "$(info) 此存放庫沒有標籤。", "A branch named \"{0}\" already exists": "分支名稱 \"{0}\" 已經存在", "A git repository was found in the parent folders of the workspace or the open file(s). Would you like to open the repository?": "在工作區或開啟檔案的上層資料夾中找到一個 Git 存放庫。您要開啟存放庫嗎?", "Absolute paths not supported in \"git.scanRepositories\" setting.": "\"git.scanRepositories\" 設定中不支援絕對路徑。", "Add Remote": "新增遠端存放庫", "Add a new remote...": "新增遠端...", "Add remote from URL": "從 URL 新增遠端存放庫", "Add remote from {0}": "從 {0} 新增遠端存放庫", "Add to Workspace": "新增到工作區", "All Repositories": "所有存放庫", "Always": "永遠", "Always Pull": "一律提取", "Always Replace Local Tag(s)": "一律取代本機標籤", "Are you sure you want to DELETE the following untracked file: '{0}'?{1}": "確定要刪除下列未追蹤的檔案： '{0}'？{1}", "Are you sure you want to DELETE the {0} untracked files?{1}": "確定要刪除 {0} 個取消追蹤的檔案?{1}", "Are you sure you want to continue connecting?": "確定要繼續連線嗎?", "Are you sure you want to create an empty commit?": "確實要建立空白提交嗎？", "Are you sure you want to discard ALL changes in {0} files?\n\nThis is IRREVERSIBLE!\nYour current working set will be FOREVER LOST if you proceed.": "確定要捨棄 {0} 個檔案中的所有變更?\n\n此動作無法回復!\n若繼續，您目前的工作集將會永久遺失。", "Are you sure you want to discard changes in '{0}'?": "確定要捨棄 [{0}] 中的變更?", "Are you sure you want to drop ALL stashes? There are {0} stashes that will be subject to pruning, and MAY BE IMPOSSIBLE TO RECOVER.": "確定要卸除所有隱藏項目嗎? 有 {0} 個隱藏項目將需要剪除，可能無法復原。", "Are you sure you want to drop ALL stashes? There is 1 stash that will be subject to pruning, and MAY BE IMPOSSIBLE TO RECOVER.": "確定要卸除所有隱藏項目嗎? 有 1 個隱藏項目將需要剪除，可能無法復原。", "Are you sure you want to drop the stash: {0}?": "確定要卸除隱藏項目 {0} 嗎?", "Are you sure you want to restore '{0}'?": "確定要還原 [{0}]?", "Are you sure you want to restore ALL {0} files?": "確定要還原全部 {0} 個檔案?", "Are you sure you want to stage {0} files with merge conflicts?": "確定要暫存 {0} 個有合併衝突的檔案嗎?", "Are you sure you want to stage {0} with merge conflicts?": "確定要暫存有合併衝突的 {0} 嗎?", "Ask Me Later": "稍後詢問我", "Branch \"{0}\" already exists": "分支「{0}」已存在", "Branch name": "分支名稱", "Branch name needs to match regex: {0}": "分支名稱需要匹配 正規表達式: {0}", "Can't force push refs to remote. The tip of the remote-tracking branch has been updated since the last checkout. Try running \"Pull\" first to pull the latest changes from the remote branch first.": "無法強制將 refs 推送至遠端。自上次結帳後，已更新遠端追蹤分支的提示。請先嘗試執行 「Pull」，以先從遠端分支提取最新的變更。", "Can't push refs to remote. Try running \"Pull\" first to integrate your changes.": "無法將參考推送到遠端。請先嘗試執行「提取」以整合您的變更。", "Can't undo because HEAD doesn't point to any commit.": "因為 HEAD 未指向任何提交，所以無法復原。", "Changes": "變更", "Checking Out Branch/Tag...": "正在簽出分支/標籤...", "Checking Out Changes...": "正在簽出變更...", "Checkout Branch/Tag...": "簽出分支/標籤...", "Choose Folder...": "選擇資料夾...", "Choose a folder to clone {0} into": "選擇要複製 {0} 的資料夾", "Choose a repository": "請選擇儲存庫 ", "Choose which repository to clone": "選擇要複製的存放庫", "Choose which repository to publish": "選擇要發佈的存放庫", "Clear whitespace characters": "清除空白字元", "Clone from URL": "存放庫 URL", "Clone from {0}": "從 {0} 複製", "Cloning git repository \"{0}\"...": "正在複製 Git 儲存庫 \"{0}\"...", "Commit": "提交", "Commit & Push Changes": "提交與推送變更", "Commit & Sync Changes": "提交與同步變更", "Commit Anyway": "仍要提交", "Commit Changes": "提交變更", "Commit Changes on \"{0}\"": "在「{0}」上提交變更", "Commit Changes to New Branch": "提交新分支的變更", "Commit Hash": "提交雜湊", "Commit message": "提交訊息", "Commit operation was cancelled due to empty commit message.": "提交作業已取消，因為提交訊息為空白。", "Commit to New Branch & Push Changes": "提交新的分支與推送變更", "Commit to New Branch & Synchronize Changes": "提交至新的分支並同步處理變更", "Commit to a New Branch": "提交新的分支", "Commits without verification are not allowed, please enable them with the \"git.allowNoVerifyCommit\" setting.": "不允許未經驗證的提交，請使用 \"git.allowNoVerifyCommit\" 設定加以啟用。", "Committing & Pushing Changes...": "正在提交並推送變更...", "Committing & Synchronizing Changes...": "正在提交並同步處理變更...", "Committing Changes to New Branch...": "正在提交對新分支的變更...", "Committing Changes...": "正在提交變更...", "Committing to New Branch & Pushing Changes...": "正在提交新的分支與推送變更...", "Committing to New Branch & Synchronizing Changes...": "正在提交至新的分支並同步處理變更...", "Conflict: Added By Them": "衝突: 由他人新增", "Conflict: Added By Us": "衝突: 由我們新增", "Conflict: Both Added": "衝突: 兩者皆新增", "Conflict: Both Deleted": "衝突: 兩者皆刪除", "Conflict: Both Modified": "衝突: 兩者皆修改", "Conflict: Deleted By Them": "衝突: 由他人刪除", "Conflict: Deleted By Us": "衝突: 由我們刪除", "Continue Merge": "繼續合併", "Continue Rebase": "繼續重訂基底", "Continuing Merge...": "繼續合併...", "Continuing Rebase...": "繼續重訂基底...", "Copy Commit Hash": "複製提交雜湊", "Could not clone your repository as Git is not installed.": "因為未安裝 Git，所以無法複製您的存放庫。", "Create Empty Commit": "建立空白提交", "Current": "目前", "Current commit message only contains whitespace characters": "目前提交訊息只包含空白字元", "Delete All {0} Files": "刪除所有 {0} 檔案", "Delete Branch": "刪除分支", "Delete File": "刪除檔案", "Deleted": "已刪除", "Discard 1 Tracked File": "捨棄 1 個追蹤的檔案", "Discard All {0} Files": "捨棄所有 {0} 檔案", "Discard All {0} Tracked Files": "捨棄全部 {0} 個追蹤的檔案", "Discard File": "捨棄檔案", "Don't Pull": "不要提取", "Don't Show Again": "不要再顯示", "Download Git": "下載 git", "Email": "電子郵件", "Enables the following features: {0}": "啟用下列功能: {0}", "Failed to authenticate to git remote.": "無法從遠端向 git 驗證。", "Failed to authenticate to git remote:\n\n{0}": "無法從遠端向 git 驗證:\n\n{0}", "Failed to delete using the Recycle Bin. Do you want to permanently delete instead?": "無法使用資源回收筒刪除。您要改為永久刪除嗎？ ", "Failed to delete using the Trash. Do you want to permanently delete instead?": "無法使用垃圾筒刪除。您要改為永久刪除嗎？", "File \"{0}\" was deleted by them and modified by us.\n\nWhat would you like to do?": "檔案 \"{0}\" 已由對方刪除，但由我們修改。\n\n要執行什麼動作?", "File \"{0}\" was deleted by us and modified by them.\n\nWhat would you like to do?": "檔案 \"{0}\" 已由我們刪除，但由對方修改。\n\n要執行什麼動作?", "Force Checkout": "強制簽出", "Force push is not allowed, please enable it with the \"git.allowForcePush\" setting.": "不允許強制更新，請啟用 \"git.allowForcePush\" 設定。", "Git Blame Information": "Git Blame 資訊", "Git History": "Git 歷程記錄", "Git Local Changes (Index)": "Git 本機變更 (索引)", "Git Local Changes (Working Tree)": "Git 本機變更 (工作樹狀目錄)", "Git error": "Git 錯誤", "Git not found. Install it or configure it using the \"git.path\" setting.": "找不到 Git。安裝它或使用 \"git.path\" 設置。", "Git repositories were found in the parent folders of the workspace or the open file(s). Would you like to open the repositories?": "在工作區或開啟檔案的上層資料夾中找到 Git 存放庫。您要開啟存放庫嗎?", "Git: {0}": "Git: {0}", "HEAD version of \"{0}\" is not available.": "\"{0}\" 的 HEAD 版本無法使用。", "Hard wrap all lines": "對所有行硬式換行", "Hard wrap line": "硬式換行", "Ignored": "已忽略", "Incoming": "傳入", "Incoming Changes": "連入變更", "Incoming Changes (added)": "傳入變更 (已新增)", "Incoming Changes (deleted)": "連入變更 (刪除)", "Incoming Changes (modified)": "傳入的變更 (修改)", "Incoming Changes (renamed)": "傳入的變更 (重新命名)", "Index Added": "已新增索引", "Index Copied": "已複製索引", "Index Deleted": "已刪除索引", "Index Modified": "已修改索引", "Index Renamed": "已重新命名索引", "Initialize Repository": "初始化存放庫", "Intent to Add": "要新增的意圖", "Intent to Rename": "要重新命名的意圖", "Invalid branch name": "分支名稱無效", "It looks like the current branch \"{0}\" might have been rebased. Are you sure you still want to pull into it?": "目前的分支 \"{0}\" 可能已重訂基底。確定仍要提取至其中嗎?", "It looks like the current branch might have been rebased. Are you sure you still want to pull into it?": "目前的分支可能已重訂基底。確定仍要提取至其中嗎?", "It's not possible to change the commit message in the middle of a rebase. Please complete the rebase operation and use interactive rebase instead.": "在重訂基底的過程中，無法變更提交訊息。請完成重訂基底作業，並改用互動式重訂基底。", "Keep Our Version": "保留我們的版本", "Keep Their Version": "保留它們的版本", "Learn More": "深入了解", "Make sure you configure your \"user.name\" and \"user.email\" in git.": "請確認您的 Git \"user.name\" 及 \"user.email\"。", "Manage Unsafe Repositories": "管理不安全的存放庫", "Merge Changes": "合併變更", "Message": "訊息", "Message (commit on \"{0}\")": "訊息 (在 \"{0}\" 上提交)", "Message ({0} to commit on \"{1}\")": "訊息 (要在 \"{0}\" 上提交 {1})", "Message ({0} to commit)": "訊息 (要提交的 {0})", "Migrate Changes": "移轉變更", "Modified": "已修改", "Move to Recycle Bin": "移至 [資源回收筒]", "Move to Trash": "移至 [垃圾桶]", "Never": "永不", "No": "否", "No rebase in progress.": "沒有進行中的重訂基底。", "Not Committed Yet": "尚未提交", "Not Committed Yet (Staged)": "尚未提交 (暫存)", "OK": "確定", "OK, Don't Ask Again": "確定，不要再詢問", "OK, Don't Show Again": "確定，不要再顯示", "Open": "開啟", "Open Commit": "開啟認可", "Open Comparison": "開啟比較", "Open File": "開啟檔案", "Open Git Log": "開啟 Git 記錄", "Open Merge": "開啟合併", "Open Repositories In Parent Folders": "開啟上層資料夾中的存放庫", "Open Repository": "開啟存放庫", "Open Settings": "開啟設定", "Open in New Window": "在新視窗中開啟", "Optionally provide a stash message": "可選擇提供隱藏的訊息", "Passphrase": "複雜密碼", "Pick a branch to pull from": "揀選要提取的來源分支", "Pick a provider to publish the branch \"{0}\" to:": "挑選要對其發佈分支 \"{0}\" 的提供者:", "Pick a remote to publish the branch \"{0}\" to:": "挑選要對其發佈分支 \"{0}\" 的遠端:", "Pick a remote to pull the branch from": "挑選要將分支提取出的遠端", "Pick a remote to remove": "挑選要移除的遠端存放庫", "Pick a repository to mark as safe and open": "挑選標記為安全的存放庫並開啟", "Pick a repository to open": "挑選要開啟的存放庫", "Pick a repository to reopen": "挑選要重新開啟的存放庫", "Pick a stash to apply": "選擇所要套用的隱藏項目", "Pick a stash to drop": "挑選要卸除的隱藏項目", "Pick a stash to pop": "選擇要取回的隱藏項目", "Pick a stash to view": "挑選要檢視的隱藏項目", "Pick workspace folder to initialize git repo in": "選擇工作區資料夾以初始化 git 儲存庫", "Please check out a branch to push to a remote.": "請簽出分支以推送到遠端。", "Please clean your repository working tree before checkout.": "請先清除您的存放庫工作樹狀再簽出。", "Please provide a commit message": "請提供提交訊息", "Please provide a message to annotate the tag": "請提供訊息以標註標籤", "Please provide a new branch name": "請提供新的分支名稱", "Please provide a remote name": "請提供遠端存放庫名稱", "Please provide a tag name": "請提供標籤名稱", "Please provide the commit hash": "請提供提交雜湊", "Publish Branch": "發布分支", "Publish Branch \"{0}\"/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "發佈 Branch \"{0}\"", "Publish Branch/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "發布 Branch", "Publish to {0}": "發布至 {0}", "Publish to...": "發布至...", "Publishing Branch \"{0}\".../{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "正在發佈 Branch \"{0}\"...", "Publishing Branch.../{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "正在發布 Branch...", "Pull": "提取", "Pull {0} and push {1} commits between {2}/{3}": "從 {2}/{3} 間提取 {0} 個提交，並推送 {1} 個提交", "Pull {0} commits from {1}/{2}": "從 {1}/{2} 提取 {0} 個提交", "Push {0} commits to {1}/{2}": "將 {0} 個提交推送到 {1}/{2}", "Rebasing": "正在重訂基底", "Regenerate Branch Name": "重新產生分支名稱", "Remote \"{0}\" already exists.": "遠端存放庫 \"{0}\" 已存在。", "Remote branch at {0}": "位於 {0} 的遠端分支", "Remote name": "遠端存放庫名稱", "Remote name format invalid": "遠端存放庫名稱格式無效", "Remote tag at {0}": "遠端標籤位於 {0}", "Reopen Closed Repositories": "重新開啟已關閉的存放庫", "Replace Local Tag(s)": "取代本機標籤", "Restore All {0} Files": "還原所有 {0} 檔案", "Restore File": "還原檔案", "Save All & Commit Changes": "全部儲存並提交變更", "Save All & Stash": "全部儲存並隱藏", "Select a branch or tag to checkout": "選取要簽出的分支或標籤", "Select a branch or tag to merge from": "選取要合併的分支或標籤來源", "Select a branch to checkout in detached mode": "選取要在中斷連結模式簽出的分支", "Select a branch to delete": "選擇分支進行刪除", "Select a branch to rebase onto": "選取要重訂為基底的分支", "Select a ref to create the branch from": "選取用來建立分支的來源參考", "Select a remote branch to delete": "選取要刪除的遠端分支", "Select a remote tag to delete": "選取要刪除的遠端標籤", "Select a remote to delete a tag from": "選取要從中刪除標籤的遠端", "Select a remote to fetch": "選取要擷取的遠端", "Select a tag to delete": "選取要刪除的標籤", "Select as Repository Destination": "選取為存放庫目的地", "Show Changes": "顯示變更", "Show Command Output": "顯示命令輸出", "Staged Changes": "暫存的變更", "Stash & Checkout": "隱藏並簽出", "Stash Anyway": "仍要隱藏", "Stash message": "隱藏的訊息", "Stashed Changes": "隱藏的變更", "Successfully pushed.": "推送成功。", "Synchronize Changes": "同步處理變更", "Synchronizing Changes...": "正在同步處理變更...", "Syncing. Cancelling may cause serious damages to the repository": "正在同步。取消可能會對存放庫造成嚴重的損害", "Tag at {0}": "位於 {0} 的標籤", "Tag name": "標籤名稱", "The \"{0}\" repository has {1} submodules which won't be opened automatically. You can still open each one individually by opening a file within.": "\"{0}\" 儲存庫有 {1} 個無法自動開啟的子模組。您仍可在其中開啟檔案來個別開啟子模組。", "The active branch cannot be deleted.": "無法刪除使用中的分支。", "The branch \"{0}\" has no remote branch. Would you like to publish this branch?": "分支 \"{0}\" 沒有任何遠端分支。您仍想發佈這個分支嗎?", "The branch \"{0}\" is not fully merged. Delete anyway?": "分支 \"{0}\" 尚未完整合併. 確定要刪除嗎?", "The changes are already present in the current branch.": "目前的分支中已存在變更。", "The current branch is not published to the remote. Would you like to publish it to access your changes elsewhere?": "最新分支未發佈至遠端。您要將其發佈以存取您在其他地方所做的變更嗎?", "The following file has unresolved diagnostics: '{0}'.\n\nHow would you like to proceed?": "下列檔案有無法解析的診斷： '{0}'。\n\n您要如何繼續？", "The following file has unsaved changes which won't be included in the commit if you proceed: {0}.\n\nWould you like to save it before committing?": "下列檔案有未儲存的變更，若您繼續，變更將不會包含在提交中: {0}。\n\n要先予以儲存再提交嗎?", "The following file has unsaved changes which won't be included in the stash if you proceed: {0}.\n\nWould you like to save it before stashing?": "下列檔案有未儲存的變更，若您繼續，變更將不會包含在隱藏項目中: {0}。\n\n要先儲存檔案再隱藏嗎?", "The git repositories in the current folder are potentially unsafe as the folders are owned by someone other than the current user.": "目前資料夾中的 Git 存放庫可能不安全，因為資料夾是由目前使用者外的人所擁有。", "The git repository at \"{0}\" has too many active changes, only a subset of Git features will be enabled.": "位於 \"{0}\" 的 Git 儲存庫有過多使用中的變更，只有部份 Git 功能會被啟用。", "The git repository in the current folder is potentially unsafe as the folder is owned by someone other than the current user.": "目前資料夾中的 Git 存放庫可能不安全，因為資料夾是由目前使用者外的人所擁有。", "The last commit was a merge commit. Are you sure you want to undo it?": "最後一個提交是合併提交。確定要復原嗎?", "The new branch will be \"{0}\"": "新分支會是 \"{0}\"", "The remote branch of the active branch cannot be deleted.": "無法刪除使用中分支的遠端分支。", "The repository does not have any changes.": "存放庫沒有任何變更。", "The repository does not have any commits. Please make an initial commit before creating a stash.": "存放庫沒有任何提交。請先進行初始提交，再建立隱藏。", "The repository does not have any staged changes.": "存放庫沒有任何暫存的變更。", "The repository does not have any untracked changes.": "存放庫沒有任何未追蹤的變更。", "The selection range does not contain any changes.": "選取範圍未包含任何變更。", "There are known issues with the installed Git \"{0}\". Please update to Git >= 2.27 for the git features to work correctly.": "安裝的 Git \"{0}\" 有已知問題。必須更新為 Git >= 2.27，Git 功能才能正常運作。", "There are merge conflicts while applying the stash. Please resolve them before committing your changes.": "套用隱藏時發生合併衝突。請先解決這些問題，再提交您的變更。", "There are merge conflicts. Please resolve them before committing your changes.": "有合併衝突。請先解決這些問題，再認可您的變更。", "There are no available repositories": "沒有儲存庫可供使用 ", "There are no changes to commit.": "沒有任何變更要提交。", "There are no changes to stash.": "沒有需要隱藏的變更。", "There are no staged changes to commit.\n\nWould you like to stage all your changes and commit them directly?": "沒有任何要提交的暫存變更。\n\n要暫存所有變更並直接提交嗎?", "There are no staged changes to stash.": "沒有需要隱藏的暫存變更。", "There are no stashes in the repository.": "存放庫中沒有隱藏項目。", "There are {0} files that have unresolved diagnostics.\n\nHow would you like to proceed?": "有 {0} 檔案具有無法解析的診斷。\n\n您要如何繼續？", "There are {0} unsaved files.\n\nWould you like to save them before committing?": "有 {0} 個未儲存檔案。\n\n您要在提交之前進行儲存嗎?", "There are {0} unsaved files.\n\nWould you like to save them before stashing?": "有 {0} 個未儲存的檔案。\n\n要先儲存檔案再隱藏嗎?", "There were merge conflicts while cherry picking the changes. Resolve the conflicts before committing them.": "揀選變更時發生合併衝突。請先解決衝突再認可。", "This action will pull and push commits from and to \"{0}/{1}\".": "此動作會從 \"{0}/{1}\" 提取和推送提交。", "This repository has no remotes configured to fetch from.": "您的儲存庫未設定要擷取的遠端來源。", "This will create a Git repository in \"{0}\". Are you sure you want to continue?": "這會建立一個 Git 儲存庫在 \"{0}\"。確定要繼續嗎?", "Too many changes were detected. Only the first {0} changes will be shown below.": "偵測到太多變更。下方僅顯示前 {0} 個變更。", "Type Changed": "類型已變更", "Unable to pull from remote repository due to conflicting tag(s): {0}. Would you like to resolve the conflict by replacing the local tag(s)?": "無法從遠端存放庫提取，因為標籤衝突: {0}。您要取代本機標籤來解決衝突嗎?", "Uncommitted Changes": "未提交的變更", "Undo merge commit": "復原合併提交", "Untracked": "已取消追蹤", "Untracked Changes": "未追蹤的變更", "Update Git": "更新 Git", "View Problems": "檢視問題", "Would you like to add \"{0}\" to .gitignore?": "要將 \"{0}\" 新增至 .gitignore 嗎?", "Would you like to open the cloned repository, or add it to the current workspace?": "要開啟以複製的儲存庫, 或將其新增到目前工作區？", "Would you like to open the cloned repository?": "要開啟複製的儲存庫嗎?", "Would you like to open the initialized repository, or add it to the current workspace?": "要開啟初始化的儲存庫，或將其新增到目前工作區？", "Would you like to open the initialized repository?": "要開啟初始化的儲存庫嗎?", "Would you like to publish this repository to continue working on it elsewhere?": "您要發佈此存放庫以在其他地方繼續使用嗎?", "Would you like {0} to [periodically run \"git fetch\"]({1})?": "是否要 {0} [定期執行 [git 擷取]]({1})?", "Yes": "是", "Yes, Don't Show Again": "是的，不要再顯示", "You": "您", "You are about to commit your changes without verification, this skips pre-commit hooks and can be undesirable.\n\nAre you sure to continue?": "您即將在不進行驗證的情況下提交變更，這麼做會跳過預先提交勾點，造成不適當的結果。\n\n確定要繼續嗎?", "You are about to force push your changes, this can be destructive and could inadvertently overwrite changes made by others.\n\nAre you sure to continue?": "您即將強制推送自己的變更，這可能具有破壞性，而且可能會不小心覆寫他人所做的變更。\n\n確定要繼續嗎?", "You are trying to commit to a protected branch and you might not have permission to push your commits to the remote.\n\nHow would you like to proceed?": "您正在嘗試提交受保護的分支，而且可能沒有將提交推到遠端的權限。\n\n要如何繼續?", "You seem to have git \"{0}\" installed. Code works best with git >= 2": "您似乎已有安裝 Git \"{0}\"。程式碼搭配 Git >= 2 的執行效果最佳", "Your local changes would be overwritten by checkout.": "您的本機變更會在簽出時被覆寫。", "Your repository has no remotes configured to publish to.": "您的儲存庫未設定要發布行的遠端目標。", "Your repository has no remotes configured to pull from.": "您的存放庫未設定要提取的來源遠端。", "Your repository has no remotes configured to push to.": "您的存放庫未設定要推送的目標遠端。", "Your repository has no remotes.": "您的存放庫沒有遠端存放庫。", "[main] Log level: {0}": "[主要] 記錄層級：{0}", "[main] Skipped found git in: \"{0}\"": "[主要] 已跳過在下列位置找到的 Git：\"{0}\"", "[main] Using git \"{0}\" from \"{1}\"": "[主要] 正在使用來自 \"{1}\" 的 Git \"{0}\"", "[main] Validating found git in: \"{0}\"": "[主要] 正在驗證在下列位置找到的 Git：\"{0}\"", "branches": "分支", "in {0}": "於 {0}", "no": "否", "now": "現在", "remote branches": "遠端分支", "tags": "標籤", "yes": "是", "{0} (Deleted)": "{0} (已刪除)", "{0} (Index)": "{0} (索引)", "{0} (Intent to add)": "{0} (要新增的意圖)", "{0} (Ours)": "{0} (我們的)", "{0} (Theirs)": "{0} (他們的)", "{0} (Type changed)": "{0} (類型已變更)", "{0} (Untracked)": "{0} (已取消追蹤)", "{0} (Working Tree)": "{0} (工作樹狀)", "{0} ({1})": "{0} ({1})", "{0} ({1}) ↔ {0} ({2})": "{0} ({1}) ↔ {0} ({2})", "{0} Checkout detached...": "{0} 簽出已中斷連結...", "{0} Commit": "{0} 提交", "{0} Commit & Push": "{0} 提交與推送", "{0} Commit & Sync": "{0} 提交與同步處理", "{0} Commit (Amend)": "{0} 認可 (修改)", "{0} Continue": "{0} 繼續", "{0} Create new branch from...": "{0} 從以下位置建立新分支...", "{0} Create new branch...": "{0} 建立新分支...", "{0} Fetch all remotes": "{0} 擷取所有遠端", "{0} Publish Branch/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "{0} 發佈 Branch", "{0} Sync Changes{1}{2}": "{0} 同步變更{1}{2}", "{0} characters over {1} in current line": "在目前行數有 {0} 個字元已超過 {1} 個", "{0} day": "{0} 天", "{0} day ago": "{0} 天前", "{0} days": "{0} 天", "{0} days ago": "{0} 天前", "{0} deletions{1}": "{0} 個刪除{1}", "{0} deletion{1}": "{0} 個刪除{1}", "{0} file changed": "{0} 個檔案已變更", "{0} files changed": "{0} 個檔案已變更", "{0} hour": "{0} 小時", "{0} hour ago": "{0} 小時前", "{0} hours": "{0} 小時", "{0} hours ago": "{0} 小時前", "{0} hr": "{0} 小時", "{0} hr ago": "{0} 小時前", "{0} hrs": "{0} 小時", "{0} hrs ago": "{0} 小時前", "{0} insertions{1}": "{0} 個插入{1}", "{0} insertion{1}": "{0} 個插入{1}", "{0} min": "{0} 分鐘", "{0} min ago": "{0} 分鐘前", "{0} mins": "{0} 分鐘", "{0} mins ago": "{0} 分鐘前", "{0} minute": "{0} 分鐘", "{0} minute ago": "{0} 分鐘前", "{0} minutes": "{0} 分鐘", "{0} minutes ago": "{0} 分鐘前", "{0} mo": "{0} 個月", "{0} mo ago": "{0} 個月前", "{0} month": "{0} 個月", "{0} month ago": "{0} 個月前", "{0} months": "{0} 個月", "{0} months ago": "{0} 個月前", "{0} mos": "{0} 個月", "{0} mos ago": "{0} 個月前", "{0} sec": "{0} 秒", "{0} sec ago": "{0} 秒前", "{0} second": "{0} 秒", "{0} second ago": "{0} 秒前", "{0} seconds": "{0} 秒", "{0} seconds ago": "{0} 秒前", "{0} secs": "{0} 秒", "{0} secs ago": "{0} 秒前", "{0} week": "{0} 週", "{0} week ago": "{0} 週前", "{0} weeks": "{0} 週", "{0} weeks ago": "{0} 週前", "{0} wk": "{0} 週", "{0} wk ago": "{0} 週前", "{0} wks": "{0} 週", "{0} wks ago": "{0} 週前", "{0} year": "{0} 年", "{0} year ago": "{0} 年前", "{0} years": "{0} 年", "{0} years ago": "{0} 年前", "{0} yr": "{0} 年", "{0} yr ago": "{0} 年前", "{0} yrs": "{0} 年", "{0} yrs ago": "{0} 年前", "{0} ↔ {1}": "{0} ↔ {1}"}, "package": {"colors.added": "已新增資源的顏色。", "colors.blameEditorDecoration": "改動記錄編輯器裝飾的色彩。", "colors.conflict": "帶有衝突資源的顏色。", "colors.deleted": "刪除資源的顏色", "colors.ignored": "忽略資源的顏色。", "colors.incomingAdded": "新增傳入資源的色彩。", "colors.incomingDeleted": "已刪除傳入資源的色彩。", "colors.incomingModified": "已修改傳入資源的色彩。", "colors.incomingRenamed": "重新命名傳入資源的色彩。", "colors.modified": "修改資源的顏色。", "colors.renamed": "重新命名或已複製資源的色彩。", "colors.stageDeleted": "已暫存資源遭刪除後的色彩。", "colors.stageModified": "已暫存資源遭修改後的色彩。", "colors.submodule": "子模組資源的顏色", "colors.untracked": "未追蹤資源的顏色。", "command.addRemote": "新增遠端存放庫...", "command.api.getRemoteSources": "取得遠端來源", "command.api.getRepositories": "取得存放庫", "command.api.getRepositoryState": "取得存放庫狀態", "command.blameToggleEditorDecoration": "切換 Git 開始 編輯器 裝飾", "command.blameToggleStatusBarItem": "切換 Git 開始狀態列專案", "command.branch": "建立分支...", "command.branchFrom": "從下列來源建立分支…", "command.checkout": "簽出至...", "command.checkoutDetached": "簽出至 (已中斷連結)...", "command.cherryPick": "揀選...", "command.cherryPickAbort": "中止揀選", "command.clean": "捨棄變更", "command.cleanAll": "捨棄所有變更", "command.cleanAllTracked": "捨棄所有追蹤修訂", "command.cleanAllUntracked": "捨棄所有未追蹤修訂", "command.clone": "複製", "command.cloneRecursive": "複製 (遞迴)", "command.close": "關閉儲存庫", "command.closeAllDiffEditors": "關閉所有 Diff 編輯器", "command.closeAllUnmodifiedEditors": "關閉所有未修改的編輯器", "command.closeOtherRepositories": "關閉其他存放庫", "command.commit": "提交", "command.commitAll": "全部提交", "command.commitAllAmend": "全部提交 (修改) ", "command.commitAllAmendNoVerify": "全部提交 (修改，未驗證)", "command.commitAllNoVerify": "全部提交 (未驗證)", "command.commitAllSigned": "全部提交 (已簽章)", "command.commitAllSignedNoVerify": "全部提交 (已簽章，未驗證)", "command.commitAmend": "認可 (修改)", "command.commitAmendNoVerify": "認可 (修改，未驗證)", "command.commitEmpty": "提交空白", "command.commitEmptyNoVerify": "提交空白 (未驗證)", "command.commitMessageAccept": "接受提交訊息", "command.commitMessageDiscard": "捨棄提交訊息", "command.commitNoVerify": "提交 (未驗證)", "command.commitSigned": "提交 (已簽章)", "command.commitSignedNoVerify": "提交 (已簽章，未驗證)", "command.commitStaged": "提交暫存", "command.commitStagedAmend": "提交暫存 (修改)", "command.commitStagedAmendNoVerify": "提交暫存 (修改，未驗證)", "command.commitStagedNoVerify": "提交暫存 (未驗證)", "command.commitStagedSigned": "提交暫存 (已簽章)", "command.commitStagedSignedNoVerify": "提交暫存 (已簽章，未驗證)", "command.continueInLocalClone": "在桌面上複製存放庫並開啟...", "command.continueInLocalClone.qualifiedName": "繼續在新的 [本機複製] 中工作", "command.createTag": "建立標籤...", "command.deleteBranch": "刪除分支...", "command.deleteRemoteBranch": "移除遠端分支...", "command.deleteRemoteTag": "刪除遠端標籤...", "command.deleteTag": "刪除標籤...", "command.fetch": "擷取", "command.fetchAll": "從所有遠端擷取", "command.fetchPrune": "擷取 (剪除)", "command.git.acceptMerge": "完成合併", "command.git.openMergeEditor": "在合併編輯器中解析", "command.git.runGitMerge": "計算與 Git 的衝突", "command.git.runGitMergeDiff3": "計算與 Git (Diff3) 的衝突", "command.graphCheckout": "簽出", "command.graphCheckoutDetached": "簽出 (已中斷連結)", "command.graphCherryPick": "揀選", "command.graphDeleteBranch": "刪除分支", "command.graphDeleteTag": "刪除標籤", "command.ignore": "新增到 .gitignore", "command.init": "初始化存放庫", "command.manageUnsafeRepositories": "管理不安全的存放庫", "command.merge": "合併...", "command.mergeAbort": "中止合併", "command.openAllChanges": "開啟所有變更", "command.openChange": "開啟變更", "command.openFile": "開啟檔案", "command.openHEADFile": "開啟檔案 (HEAD)", "command.openRepositoriesInParentFolders": "開啟上層資料夾中的存放庫", "command.openRepository": "開啟存放庫", "command.publish": "發布分支...", "command.pull": "提取", "command.pullFrom": "從...提取", "command.pullRebase": "提取 (重訂基底)", "command.push": "推送", "command.pushFollowTags": "推送 (跟隨標籤)", "command.pushFollowTagsForce": "推送 (跟隨標籤，強制)", "command.pushForce": "推送(強制更新)", "command.pushTags": "推送標籤", "command.pushTo": "推送至...", "command.pushToForce": "推送至...(強制更新)", "command.rebase": "重訂基底分支...", "command.rebaseAbort": "中止重訂基底", "command.refresh": "重新整理", "command.removeRemote": "移除遠端存放庫", "command.rename": "重新命名", "command.renameBranch": "重新命名分支...", "command.reopenClosedRepositories": "重新開啟已關閉的存放庫...", "command.restoreCommitTemplate": "還原提交範本", "command.revealFileInOS.linux": "開啟所屬資料夾", "command.revealFileInOS.mac": "在 Finder 中顯示", "command.revealFileInOS.windows": "在檔案總管中顯示", "command.revealInExplorer": "在 [總管檢視] 中顯示", "command.revertChange": "還原變更", "command.revertSelectedRanges": "還原選取的範圍", "command.showOutput": "顯示 Git 輸出", "command.stage": "暫存變更", "command.stageAll": "暫存所有變更 (Stage All Changes)", "command.stageAllMerge": "暫存所有合併變更", "command.stageAllTracked": "暫存所有追蹤修訂", "command.stageAllUntracked": "暫存所有未追蹤修訂", "command.stageBlock": "暫存區塊", "command.stageChange": "暫存變更", "command.stageSelectedRanges": "暫存選取的範圍", "command.stageSelection": "暫存選取", "command.stash": "隱藏", "command.stashApply": "套用隱藏項目...", "command.stashApplyEditor": "套用隱藏項目", "command.stashApplyLatest": "套用最新的隱藏項目", "command.stashDrop": "卸除隱藏項目...", "command.stashDropAll": "卸除所有隱藏項目...", "command.stashDropEditor": "卸除隱藏項目", "command.stashIncludeUntracked": "隱藏項目 (包含未被追蹤的項目)", "command.stashPop": "取回隱藏項目...", "command.stashPopEditor": "快顯隱藏項目", "command.stashPopLatest": "取回最近的隱藏項目", "command.stashStaged": "隱藏已暫存", "command.stashView": "檢視隱藏項目...", "command.sync": "同步處理", "command.syncRebase": "同步 (重定基底)", "command.timelineCompareWithSelected": "與選取項目比較", "command.timelineCopyCommitId": "複製提交識別碼", "command.timelineCopyCommitMessage": "複製提交訊息", "command.timelineOpenDiff": "開啟變更", "command.timelineSelectForCompare": "選取以進行比較", "command.undoCommit": "復原上個提交", "command.unstage": "取消暫存變更", "command.unstageAll": "取消所有暫存變更(Unstage All Changes)", "command.unstageChange": "取消暫存變更", "command.unstageSelectedRanges": "取消暫存選取的範圍", "command.viewChanges": "開啟變更", "command.viewCommit": "開啟認可", "command.viewStagedChanges": "開啟暫存變更", "command.viewUntrackedChanges": "開啟未追蹤的變更", "config.allowForcePush": "控制是否啟用強制推送 (不論是否有新的認可)。", "config.allowNoVerifyCommit": "控制是否允許未執行預先提交與提交訊息勾點的提交。", "config.alwaysShowStagedChangesResourceGroup": "一律顯示「暫存變更」的資源群組。", "config.alwaysSignOff": "控制所有提交的簽核旗標。", "config.autoRepositoryDetection": "設定何時自動偵測存放庫。", "config.autoRepositoryDetection.false": "停用自動儲存庫掃描。", "config.autoRepositoryDetection.openEditors": "掃描開啟檔案的父資料夾。", "config.autoRepositoryDetection.subFolders": "掃描目前已開啟資料夾的子資料夾。", "config.autoRepositoryDetection.true": "掃描目前已開啟資料夾的子資料夾與開啟檔案的父資料夾。", "config.autoStash": "先隱藏再提取任何變更，並在成功提取後，將其還原。", "config.autofetch": "設定為 true 時，會自動從目前 Git 存放庫的預設遠端擷取提交。設定為 `all` 將從所有遠端進行擷取。", "config.autofetchPeriod": "當啟用 `#git.autofetch#` 時，每個自動 git 擷取的間隔時間 (秒)。", "config.autorefresh": "是否啟用自動重新整理。", "config.blameEditorDecoration.enabled": "控制是否要使用編輯器裝飾，在編輯器中顯示改動記錄資訊。", "config.blameEditorDecoration.template": "改動記錄資訊編輯器裝飾的範本。支援的變數:\r\n\r\n* `hash`: 提交雜湊\r\n\r\n* `hashShort`: 根據 `#git.commitShortHashLength#` 的提交雜湊的前 N 個字元\r\n\r\n* `subject`: 提交訊息的第一行\r\n\r\n* `authorName`: 作者名稱\r\n\r\n* `authorEmail`: 作者電子郵件\r\n\r\n* `authorDate`: 作者日期\r\n\r\n* `authorDateAgo`: 從作者日期到現在的時間差距\r\n\r\n", "config.blameStatusBarItem.enabled": "控制是否要在狀態列中顯示改動記錄資訊。", "config.blameStatusBarItem.template": "改動記錄資訊狀態列項目的範本。支援的變數:\r\n\r\n* `hash`: 提交雜湊\r\n\r\n* `hashShort`: 根據 `#git.commitShortHashLength#` 的提交雜湊的前 N 個字元\r\n\r\n* `subject`: 提交訊息的第一行\r\n\r\n* `authorName`: 作者名稱\r\n\r\n* `authorEmail`: 作者電子郵件\r\n\r\n* `authorDate`: 作者日期\r\n\r\n* `authorDateAgo`: 從作者日期到現在的時間差距\r\n\r\n", "config.branchPrefix": "建立新分支時使用的首碼。", "config.branchProtection": "受保護分支的清單。根據預設，在將變更提交到受保護的分支之前，會先顯示提示。可使用 `#git.branchProtectionPrompt#` 設定控制提示。", "config.branchProtectionPrompt": "控制是否要在變更提交至受保護的分支之前先顯示提示。", "config.branchProtectionPrompt.alwaysCommit": "一律提交至受保護分支的變更。", "config.branchProtectionPrompt.alwaysCommitToNewBranch": "一律提交新分支的變更。", "config.branchProtectionPrompt.alwaysPrompt": "在變更提交至受保護的分支之前一律提示。", "config.branchRandomNameDictionary": "隨機產生的分支名稱所使用的字典清單。每個值代表用來產生分支名稱區段的字典。支援的字典: '形容詞'、'動物'、'色彩' 和 '數字'。", "config.branchRandomNameDictionary.adjectives": "隨機形容詞", "config.branchRandomNameDictionary.animals": "隨機動物名稱", "config.branchRandomNameDictionary.colors": "隨機色彩名稱", "config.branchRandomNameDictionary.numbers": "介於 100 到 999 之間的隨機數字", "config.branchRandomNameEnable": "控制建立新分支時是否產生隨機名稱。", "config.branchSortOrder": "控制分支的排序順序。", "config.branchValidationRegex": "用於驗證新分支名稱的正規表達式。", "config.branchWhitespaceChar": "要取代新分支名稱中的空格，以及分隔隨機產生之分支名稱區段的字元。", "config.checkoutType": "控制執行 [簽出至...] 時，要列出哪種類型的 Git 參考。", "config.checkoutType.local": "本機分支", "config.checkoutType.remote": "遠端分支", "config.checkoutType.tags": "標籤", "config.closeDiffOnOperation": "控制在隱藏、提交、捨棄、暫存或取消暫存變更時，是否應自動關閉 Diff 編輯器。", "config.commandsToLog": "GIT 命令列表 (例如: commit、push)，這些命令的 `stdout` 將被記錄到 [git 輸出](command:git.showOutput)。如果 GIT 命令設定了用戶端勾點，那麼用戶端勾點的 `stdout` 也將被記錄到 [git 輸出](command:git.showOutput)。", "config.commitShortHashLength": "控制提交短雜湊的長度。", "config.confirmEmptyCommits": "一律確認 'Git: Commit Empty' 命令的空白提交建立。", "config.confirmForcePush": "控制強制更新前是否要求確認。", "config.confirmNoVerifyCommit": "控制在提交但未經驗證之前是否要求確認。", "config.confirmSync": "請在同步 Git 存放庫之前先確認。", "config.countBadge": "控制 Git 計數徽章。", "config.countBadge.all": "計算所有變更的數目。", "config.countBadge.off": "關閉計數器。", "config.countBadge.tracked": "僅計算追蹤的變更數目。", "config.decorations.enabled": "控制 Git 是否會為總管和 [已開啟的編輯器] 檢視貢獻色彩和徽章。", "config.defaultBranchName": "初始化新的 Git 存放庫時，預設分支的名稱 (例如: main、trunk、development)。設為空白時，將會使用在 Git 中設定的預設分支名稱。**注意:** 需要 Git 版本 `2.28.0` 或更新版本。", "config.defaultCloneDirectory": "用於複製 Git 存放庫的預設位置。", "config.detectSubmodules": "控制是否自動偵測 Git 子模組。", "config.detectSubmodulesLimit": "控制 Git 子模組的偵測限制。", "config.diagnosticsCommitHook.enabled": "控制在提交前是否檢查未解決的診斷問題。", "config.diagnosticsCommitHook.sources": "控制在提交前要考慮的來源清單 (**項目**) 及最小嚴重性 (**值**)。**注意: ** 若要忽略來自特定來源的診斷，請將該來源新增至清單，並將最小嚴重性設定為「None」。", "config.discardAllScope": "控制 `Discard all changes` 命令會捨棄的變更。`all` 會捨棄所有變更。`tracked` 只會捨棄追蹤的檔案。`prompt` 會在每次動作執行時顯示提示對話方塊。", "config.discardUntrackedChangesToTrash": "控制捨棄未追蹤的變更是否會將檔案移至資源回收筒 (Windows)、垃圾桶 (macOS、Linux)，而不是永久刪除。**注意:** 此設定在連接到遠端或以貼齊套件在 Linux 中執行時沒有作用。", "config.enableCommitSigning": "允許使用 GPG、X.509 或 SSH 簽署提交。", "config.enableSmartCommit": "無暫存變更時提交所有變更。", "config.enableStatusBarSync": "控制是否在狀態列顯示 Git 同步命令。", "config.enabled": "是否啟用 Git。", "config.experimental.installGuide": "Git 安裝流程的實驗性改進。", "config.fetchOnPull": "啟用時，會在提取時擷取所有分支。否則僅擷取目前的分支。", "config.followTagsWhenSync": "執行 sync 命令時，推送所有附註標籤。", "config.ignoreLegacyWarning": "略過舊的 Git 警告。", "config.ignoreLimitWarning": "當儲存庫中有過多變更時，略過警告。", "config.ignoreMissingGitWarning": "忽略遺漏 Git 時的警告。", "config.ignoreRebaseWarning": "當分支在提取時可能已重訂基底時，忽略警告。", "config.ignoreSubmodules": "忽略檔案樹狀目錄中子模組的修改。", "config.ignoreWindowsGit27Warning": "當 Windows 上安裝了 Git 2.25 - 2.26 時，忽略警告。", "config.ignoredRepositories": "要忽略的 Git 儲存庫清單。", "config.inputValidation": "控制是否顯示認可訊息輸入驗證診斷。", "config.inputValidationLength": "控制顯示警告的提交訊息長度閾值。", "config.inputValidationSubjectLength": "控制用於顯示警告的提交訊息主旨長度閾值。將其取消設定可繼承 `#git.inputValidationLength#` 的值。", "config.mergeEditor": "針對目前發生衝突的檔案開啟合併編輯器。", "config.openAfterClone": "控制是否要在複製後自動開啟存放庫。", "config.openAfterClone.always": "永遠在目前視窗中開啟。", "config.openAfterClone.alwaysNewWindow": "永遠在新視窗中開啟。", "config.openAfterClone.prompt": "永遠提示採取動作。", "config.openAfterClone.whenNoFolderOpen": "只有在未開啟任何資料夾時，才在目前視窗中開啟。", "config.openDiffOnClick": "控制按一下變更時，是否應該開啟 Diff 編輯器。否則將開啟一般編輯器。", "config.openRepositoryInParentFolders": "控制是否應開啟工作區或開啟檔案上層資料夾中的存放庫。", "config.openRepositoryInParentFolders.always": "永遠在工作區或開啟檔案的上層資料夾中開啟存放庫。", "config.openRepositoryInParentFolders.never": "永不在工作區或開啟檔案上層資料夾中開啟存放庫。", "config.openRepositoryInParentFolders.prompt": "在工作區或開啟檔案上層資料夾中開啟存放庫前提示。", "config.optimisticUpdate": "控制是否要在執行 Git 命令之後，樂觀地更新原始檔控制檢視的狀態。", "config.path": "Git 可執行檔的路徑與檔案名稱，例如 `C:\\Program Files\\Git\\bin\\git.exe` (Windows)。此亦可為包含多個待查閱路徑的字串值陣列。", "config.postCommitCommand": "成功提交後執行 git 命令。", "config.postCommitCommand.none": "提交後不要執行任何命令。", "config.postCommitCommand.push": "成功提交後執行 'git push'。", "config.postCommitCommand.sync": "成功提交後執行 'git pull' 和 'git push'。", "config.promptToSaveFilesBeforeCommit": "控制Git是否應該在提交之前檢查未儲存的檔案。", "config.promptToSaveFilesBeforeCommit.always": "檢查任何未存檔的檔案。", "config.promptToSaveFilesBeforeCommit.never": "停用此檢查。", "config.promptToSaveFilesBeforeCommit.staged": "僅檢查未儲存的暫存檔案。", "config.promptToSaveFilesBeforeStash": "控制 Git 是否應該在隱藏變更之前檢查未儲存的檔案。", "config.promptToSaveFilesBeforeStash.always": "檢查任何未存檔的檔案。", "config.promptToSaveFilesBeforeStash.never": "停用此檢查。", "config.promptToSaveFilesBeforeStash.staged": "僅檢查未儲存的暫存檔案。", "config.pruneOnFetch": "擷取時剪除。", "config.publishBeforeContinueOn": "控制從 Git 存放庫使用 [繼續工作] 時，是否發佈未發佈的 Git 狀態。", "config.publishBeforeContinueOn.always": "從 Git 存放庫使用 [繼續工作] 時，一律發佈未發佈的 Git 狀態", "config.publishBeforeContinueOn.never": "從 Git 存放庫使用 [繼續工作] 時，永不發佈未發佈的 Git 狀態", "config.publishBeforeContinueOn.prompt": "從 Git 存放庫使用 [繼續工作] 時，提示發佈未發佈的 Git 狀態", "config.pullBeforeCheckout": "控制沒有傳出認可的分支在簽出前是否向前快轉。", "config.pullTags": "於提取時擷取所有標籤。", "config.rebaseWhenSync": "當執行同步命令時強制 Git 使用重訂基底。", "config.rememberPostCommitCommand": "記住提交後執行的最後一個 git 命令。", "config.replaceTagsWhenPull": "執行提取命令時，以遠端標籤自動取代本機標籤，以防發生衝突。", "config.repositoryScanIgnoredFolders": "若 `#git.autoRepositoryDetection#` 設為 `true` 或 `subFolders`，掃描 Git 儲存機制時忽略的資料夾清單。", "config.repositoryScanMaxDepth": "控制當 '#git.autoRepositoryDetection#' 設定為 'true' 或 'subFolders' 時，掃描 Git 存放庫的工作區資料夾時所使用的深度。可以設為 '-1'，表示沒有限制。", "config.requireGitUserConfig": "控制要在沒有組態時要求明確的 Git 使用者組態，還是允許 Git 進行猜測。", "config.scanRepositories": "要在其中搜尋 Git 存放庫的路徑清單。", "config.showActionButton": "控制是否可以在原始檔控制檢視中顯示動作按鈕。", "config.showActionButton.commit": "當本地分支有可提交的修改檔案時，顯示提交變更的動作按鈕。", "config.showActionButton.publish": "當本地分支沒有追蹤遠端分支時，顯示發佈的動作按鈕。", "config.showActionButton.sync": "當本地分支位於遠端分支前後時，顯示同步變更的動作按鈕。", "config.showCommitInput": "控制是否要在 Git 原始檔控制台中顯示提交輸入。", "config.showInlineOpenFileAction": "控制是否在Git變更列表中的檔名旁顯示“開啟檔案”的動作按鈕。", "config.showProgress": "控制 Git 動作是否應顯示進度。", "config.showPushSuccessNotification": "控制是否要在推送成功時顯示通知。", "config.showReferenceDetails": "控制是否在簽出、分支和標籤選擇器中顯示 Git 參考最後一次提交的詳細資料。", "config.similarityThreshold": "控制相似性索引的閾值 (相較於檔案大小的新增/刪除數量)，以將一對新增/刪除檔案中的變更視為重新命名。**注意:** 需要 Git 版本 `2.18.0` 或更新版本。", "config.smartCommitChanges": "控制智慧提交自動暫存的變更。", "config.smartCommitChanges.all": "自動暫存所有變更。", "config.smartCommitChanges.tracked": "僅自動暫存追蹤的變更。", "config.statusLimit": "控制如何限制可以從 Git 狀態命令剖析的變更數。可以設定為 0 表示無限制。", "config.suggestSmartCommit": "建議啟用智慧提交 (在沒有暫存變更時提交所有變更)。", "config.supportCancellation": "控制是否要在執行同步動作時顯示允許使用者取消作業的通知。", "config.terminalAuthentication": "控制是否要讓 VS Code 成為在整合式終端中繁衍之 Git 處理序的驗證處理常式。請注意: 您必須重新啟動終端，才能在此設定中挑選變更。", "config.terminalGitEditor": "控制是否要讓 VS Code 成為在整合式終端中繁衍之 Git 處理序的 Git 編輯器。請注意: 您必須重新啟動終端，才能在此設定中發現變更。", "config.timeline.date": "控制 [時間軸] 檢視中的項目要使用哪個日期。", "config.timeline.date.authored": "使用撰寫日期", "config.timeline.date.committed": "使用提交日期", "config.timeline.showAuthor": "控制是否要在 [時間軸] 檢視中顯示提交作者。", "config.timeline.showUncommitted": "控制是否要在 [時間表] 檢視中顯示未提交的變更。", "config.untrackedChanges": "控制未追蹤修訂的運作方式。", "config.untrackedChanges.hidden": "未追蹤修訂會隱藏並從數個動作中排除。", "config.untrackedChanges.mixed": "所有修訂 (追蹤和未追蹤) 會同時出現並以同等方式運作。", "config.untrackedChanges.separate": "未追蹤修訂個別出現在原始檔控制檢視中。這些修訂也會從數個動作中排除。", "config.useCommitInputAsStashMessage": "控制是否使用提交輸入方塊中的訊息作為預設隱藏訊息。", "config.useEditorAsCommitInput": "控制在提交輸入方塊中未提供訊息時，是否使用全文字編輯器來撰寫提交訊息。", "config.useForcePushIfIncludes": "控制強制推送是否使用較安全的 force-if-includes 變體。注意: 這個設定需要啟用 '#git.useForcePushWithLease#' 設定，以及 Git 版本 '2.30.0' 或更新版本。", "config.useForcePushWithLease": "控制強制推送是否使用較安全的 force-with-lease 方法。", "config.useIntegratedAskPass": "控制是否應覆寫 GIT_ASKPASS 以使用整合式版本。", "config.verboseCommit": "啟用 '#git.useEditorAsCommitInput#' 時，啟用詳細資訊輸出。", "description": "Git SCM 整合", "displayName": "Git", "submenu.branch": "分支", "submenu.changes": "變更", "submenu.commit": "提交", "submenu.commit.amend": "修改", "submenu.commit.signoff": "登出", "submenu.explorer": "Git", "submenu.pullpush": "提取、推送", "submenu.remotes": "遠端", "submenu.stash": "隱藏", "submenu.tags": "標籤", "view.workbench.cloneRepository": "您可以在本機複製存放庫。\r\n[複製存放庫](command:git.clone '在 Git 延伸模組啟用後複製存放庫')", "view.workbench.learnMore": "若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.closedRepositories": "找到先前已關閉的 Git 存放庫。\r\n[重新開啟已關閉的存放庫](command:git.reopenClosedRepositories)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.closedRepository": "找到先前已關閉的 Git 存放庫。\r\n[重新開啟已關閉的存放庫](command:git.reopenClosedRepositories)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.disabled": "如果您想要使用 Git 功能，請在 [設定](command:workbench.action.openSettings?%5B%22git.enabled%22%5D) 中啟用 Git。\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.empty": "如果要使用 Git 功能，您可以開啟包含 Git 存放庫的資料夾或從 URL 複製。\r\n[開啟資料夾](command:vscode.openFolder)\r\n[複製存放庫](command:git.cloneRecursive)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.emptyWorkspace": "目前開啟的工作區，沒有任何包含 Git 存放庫的資料夾。\r\n[新增資料夾至工作區](command:workbench.action.addRootFolder)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.folder": "目前開啟的資料夾沒有任何 Git 存放庫。您可以初始化存放庫，其將啟用 Git 支援的原始檔控制功能。\r\n[初始化存放庫](command:git.init?%5Btrue%5D)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。", "view.workbench.scm.missing": "安裝 Git，這是熱門的原始檔控制系統，以追蹤程式碼變更並與其他人共同作業。在我們的[Git指南](https://aka.ms/vscode-scm)中深入了解。", "view.workbench.scm.missing.linux": "原始檔控制必須安裝 Git。\r\n[下載適用於 Linux 的 Git](https://git-scm.com/download/linux)\r\n安裝之後，請 [重新載入](command:workbench.action.reloadWindow) (或 [疑難排解](command:git.showOutput))。可從 [ Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22) 安裝其他原始檔控制提供者。", "view.workbench.scm.missing.mac": "[下載適用於 macOS 的 Git](https://git-scm.com/download/mac)\r\n安裝之後，請[重新載入](command:workbench.action.reloadWindow) (或[疑難排解](command:git.showOutput))。可[從 Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22) 安裝其他原始檔控制提供者。", "view.workbench.scm.missing.windows": "[下載適用於 Windows 的 Git](https://git-scm.com/download/win)\r\n安裝之後，請[重新載入](command:workbench.action.reloadWindow) (或[疑難排解](command:git.showOutput))。可[從 Marketplace](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22) 安裝其他原始檔控制提供者。", "view.workbench.scm.repositoriesInParentFolders": "在工作區或開啟檔案的上層目錄中發現 Git 存放庫。\r\n[開啟存放庫](command:git.openRepositoriesInParentFolders)\r\n使用 [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) 設定來控制工作區的上層資料夾中的 Git 存放庫或開啟中的檔案是否開啟。若要深入了解，[請閱讀我們的文件](https://aka.ms/vscode-git-repository-in-parent-folders)。", "view.workbench.scm.repositoryInParentFolders": "在工作區或開啟檔案的上層目錄中發現 Git 存放庫。\r\n[開啟存放庫](command:git.openRepositoriesInParentFolders)\r\n使用 [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) 設定來控制工作區的上層資料夾中的 Git 存放庫或開啟中的檔案是否開啟。若要深入了解，[請閱讀我們的文件](https://aka.ms/vscode-git-repository-in-parent-folders)。", "view.workbench.scm.scanFolderForRepositories": "正在掃描 Git 存放庫的資料夾...", "view.workbench.scm.scanWorkspaceForRepositories": "正在掃描 Git 存放庫的工作區...", "view.workbench.scm.unsafeRepositories": "偵測到的 Git 存放庫可能不安全，因為資料夾是由目前使用者以外的人所擁有。\r\n[管理不安全的存放庫](command:git.manageUnsafeRepositories)\r\n若要深入了解不安全的存放庫，[請閱讀我們的文件](https://aka.ms/vscode-git-unsafe-repository)。", "view.workbench.scm.unsafeRepository": "偵測到的 Git 存放庫可能不安全，因為資料夾是由目前使用者以外的人所擁有。\r\n[管理不安全的存放庫](command:git.manageUnsafeRepositories)\r\n若要深入了解不安全的存放庫，請[閱讀我們的文件](https://aka.ms/vscode-git-unsafe-repository)。", "view.workbench.scm.workspace": "目前開啟的工作區，沒有任何包含 Git 存放庫的資料夾。您可以在資料夾上初始化存放庫，其將啟用 Git 支援的原始檔控制功能。\r\n[初始化存放庫](command:git.init)\r\n若要深入了解如何在 VS Code 中使用 Git 和原始檔控制，[請閱讀我們的文件](https://aka.ms/vscode-scm)。"}}}