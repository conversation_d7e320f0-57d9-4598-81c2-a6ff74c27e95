spec-type=rnnt1
model-config-path=model_onnx.config
token-path=tokens.list
matrix-kind=0
beam-width=3
recombine-kind=0
score-norm-kind=0
beam-sort-kind=1
insertion-boost=NaN
end-padding-dim=0
feature-dim=80
feature-stride=80
ms-per-frame=40
option-check=false
biasing-weight=1.1
biasing-lexical=1

# prongen configuration for word timing using CIPhone
locale-id=1033
lexicon=prongen/lsr1033.lxa
lts=prongen/che1033.lxa
phn=prongen/L1033.phn
tn=prongen/tn1033.bin
filler-phone=sil

# locale
phone-path=ciphone.lst
profanity-path=en-us_ProfanityList.enc
locale-path=en_spellingrules.txt
output-locale=en-US

# punctuation and capitalization
punctuation-path=punct/en-us_explicitPuncRules.txt
punctuation-model-path=punct/cappunc.onnx
punctuation-sp-vocab-path=punct/spm_8k.vocab
punctuation-sps-to-ids-path=punct/w2i.txt
punctuation-ids-to-classes-path=punct/i2w.0.txt
capitalization-ids-to-classes-path=punct/i2w.1.txt
capitalize-paths=en-US_bigram_capitalizationRules.enc,en-US_trigram_capitalizationRules.enc,en-US_unigram_capitalizationRules.enc

# VAD
vad-model-path=svad_init.quantized.onnx
vad-mode=segmentation
vad-threshold=0.6
vad-initializers-index=0
vad-rewind-frames-count=75
vad-segmentation-threshold=0.35
vad-ms-per-frame=10


# LID
lid-enabled=0
lid-threshold=0.90
lid-rewind-frames=0
lid-mode=1

# ITN configuration: itn-mode value can be: 0=disabled; 1=default, apply ITN on final result; 2=enable ITN for both final and intermediate result. 
# For intermediate result, ITN is invoked if a punctuation is appeared.
itn-mode=2
itn-models=itn/itn.bin
itn-tag-model=itn/itntag.quant.onnx
itn-tag-token-path=itn/input_tokens.list
itn-tag-entity-path=itn/output_tokens.list
itn-tag-bpe-vocab-path=itn/itntag.spm.vocab

# lattice
use-lattice=1
context-size=5
nbest-size=100

# phonetic alternates
prefix-tree-bin=6000_lexicon.prelt

license-version=1
