"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MVVSFileSystem = void 0;
const vscode = require("vscode");
const path = require("path");
const fs = require("fs");
const extConfig = require("../config/extConfig");
const editor_1 = require("../common/editor");
class MVVSFileSystem {
    constructor() {
        this._emitter = new vscode.EventEmitter();
        this.onDidChangeFile = this._emitter.event;
    }
    watch(uri, options) {
        throw new Error("Method not implemented.");
    }
    stat(uri) {
        const filePath = this.resolve(uri);
        if (!filePath) {
            throw vscode.FileSystemError.FileNotFound();
        }
        if (!fs.existsSync(filePath)) {
            throw vscode.FileSystemError.FileNotFound();
        }
        const status = fs.statSync(filePath);
        return {
            type: status.isFile() ? 1 : 2,
            ctime: status.ctime.getTime(),
            mtime: status.mtime.getTime(),
            size: status.size
        };
    }
    readDirectory(uri) {
        throw new Error("Method not implemented.");
    }
    createDirectory(uri) {
        throw new Error("Method not implemented.");
    }
    readFile(uri) {
        const filePath = this.resolve(uri);
        if (!filePath) {
            throw vscode.FileSystemError.FileNotFound();
        }
        const buffer = fs.readFileSync(filePath);
        const content = buffer.toString();
        return new Uint8Array(content.split('').map(function (char) { return char.charCodeAt(0); }));
    }
    writeFile(uri, content, options) {
        throw new Error("Method not implemented.");
    }
    delete(uri, options) {
        throw new Error("Method not implemented.");
    }
    rename(oldUri, newUri, options) {
        throw new Error("Method not implemented.");
    }
    resolve(vfsUri) {
        const root = this.getCacehPath();
        if (!root) {
            return undefined;
        }
        return path.join(root, vfsUri.path);
    }
    getCacehPath() {
        const workspace = (0, editor_1.getWorkspacePath)();
        if (!workspace) {
            return undefined;
        }
        const cacheDir = "cache";
        const dbConfig = this.getDbConfig();
        if (!dbConfig) {
            return undefined;
        }
        const host = dbConfig.host;
        const port = dbConfig.port;
        const fullPath = path.join(workspace, cacheDir, host, port.toString());
        return fullPath;
    }
    getDbConfig() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length == 0) {
            return undefined;
        }
        const workspace = workspaceFolders[0];
        const config = extConfig.read(workspace.uri.fsPath, extConfig.db, extConfig.offlineMode);
        if (!config) {
            return undefined;
        }
        return config.db;
    }
}
exports.MVVSFileSystem = MVVSFileSystem;
//# sourceMappingURL=mvvsfs.js.map