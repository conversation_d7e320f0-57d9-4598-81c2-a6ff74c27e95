﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">temp_packages</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">temp_packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="temp_packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)\microsoft.cognitiveservices.speech\1.44.0\build\Microsoft.CognitiveServices.Speech.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.cognitiveservices.speech\1.44.0\build\Microsoft.CognitiveServices.Speech.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.telemetry\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Telemetry.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.telemetry\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Telemetry.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.onnx.runtime\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.onnx.runtime\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.ONNX.Runtime.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.embedded.tts\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.embedded.tts\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Embedded.TTS.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.embedded.sr\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.cognitiveservices.speech.extension.embedded.sr\1.44.0\build\Microsoft.CognitiveServices.Speech.Extension.Embedded.SR.props')" />
  </ImportGroup>
</Project>