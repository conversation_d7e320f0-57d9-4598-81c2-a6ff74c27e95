{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Always": "永遠", "Auto Attach: Always": "自動附加: 永遠", "Auto Attach: Disabled": "自動附加: 已停用", "Auto Attach: Smart": "自動附加: 智慧型", "Auto Attach: With Flag": "自動附加: 使用旗標", "Auto attach is disabled and not shown in status bar": "自動附加已停用且不顯示於狀態列", "Auto attach to every Node.js process launched in the terminal": "自動附加到在終端機中啟動的每個 Node.js 處理序", "Auto attach when running scripts that aren't in a node_modules folder": "執行不在 node_modules 資料夾中的指令碼時自動附加", "Automatically attach to node.js processes in debug mode": "在偵錯模式下自動附加到 node.js 處理序", "Debug Auto Attach": "偵錯自動附加", "Disabled": "停用", "Only With Flag": "僅限旗標", "Only auto attach when the `--inspect` flag is given": "只有在指定 `--inspect` 時才自動附加", "Re-enable auto attach": "重新啟用自動附加", "Smart": "智慧型", "Temporarily disable auto attach in this session": "在此工作階段中暫時停用自動附加", "Toggle auto attach in this workspace": "在此工作區中切換自動附加", "Toggle auto attach on this machine": "在這部電腦上切換自動附加"}, "package": {"description": "當節點偵錯延伸模組非使用中時，自動附加功能的協助工具。", "displayName": "節點偵錯自動附加", "toggle.auto.attach": "切換自動附加"}}}