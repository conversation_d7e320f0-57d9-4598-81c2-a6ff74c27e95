{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Example": "範例", "Files by Extension": "依副檔名排列的檔案", "Files with Extension": "檔案副檔名", "Files with Multiple Extensions": "具多個副檔名的檔案", "Files with Path": "檔案路徑", "Files with Siblings by Name": "依名稱排列且同層級的檔案", "Folder by Name (Any Location)": "依名稱排列的資料夾 (任何位置)", "Folder by Name (Top Level)": "依名稱排列的資料夾 (最上層)", "Folders with Multiple Names (Top Level)": "具多個名稱的資料夾 (最上層)", "GitHub": "GitHub", "Map all files matching the absolute path glob pattern in their path to the language with the given identifier.": "將檔案路徑符合絕對路徑 Glob 模式的所有檔案，對應到具有指定識別碼的語言。", "Map all files matching the glob pattern in their filename to the language with the given identifier.": "將檔案名稱符合 Glob 模式的所有檔案，對應到具有指定識別碼的語言。", "Match a folder with a specific name in any location.": "在所有位置比對具特定名稱的資料夾。", "Match a top level folder with a specific name.": "比對具特定名稱的最上層資料夾。", "Match all files of a specific file extension.": "比對所有具特定副檔名的檔案。", "Match all files with any of the file extensions.": "比對所有具任何副檔名的檔案。", "Match files that have siblings with the same name but a different extension.": "比對名稱相同但副檔名不同的同層級檔案。", "Match multiple top level folders.": "比對多個最上層資料夾。", "The character used by the operating system to separate components in file paths. Is also aliased to '/'.": "作業系統用來分隔檔案路徑中元件的字元。也別名化為 '/'。", "The current opened file": "目前已開啟的檔案", "The current opened file relative to ${workspaceFolder}": "目前已開啟與 ${workspaceFolder} 相關的檔案", "The current opened file workspace folder name without any slashes (/)": "目前開啟的檔案工作區資料夾名稱，不含任何斜線 (/)", "The current opened file's basename": "目前已開啟檔案的 basename", "The current opened file's basename with no file extension": "目前已開啟檔案的 basename，不包含副檔名", "The current opened file's dirname": "目前已開啟檔案的 dirname", "The current opened file's dirname relative to ${workspaceFolder}": "相對於 ${workspaceFolder} 的目前已開啟檔案 dirname", "The current opened file's extension": "目前已開啟檔案的副檔名", "The current opened file's folder name": "目前已開啟檔案的資料夾名稱", "The current selected line number in the active file": "在使用中的檔案內目前已選取的行數", "The current selected text in the active file": "在使用中的檔案內目前已選取的文字", "The file extension of the editor (e.g. txt)": "編輯器的副檔名 (例如 txt)", "The file name of the editor without its directory or extension (e.g. myFile)": "編輯器的檔案名稱，不帶目錄或副檔名 (例如 myFile)", "The name of the default build task. If there is not a single default build task then a quick pick is shown to choose the build task.": "預設建置工作的名稱。如果沒有任何預設建置工作，則會顯示快選以選擇建置工作。", "The name of the folder opened in VS Code without any slashes (/)": "在 VS Code 內已開啟的資料夾名稱，不包含任何斜線 (/)", "The nth parent folder name of the editor": "編輯器的第 n 個父資料夾名稱", "The parent folder name of the editor (e.g. myFileFolder)": "編輯器的父資料夾名稱 (例如 myFileFolder)", "The path of the folder opened in VS Code": "在 VS Code 中已開啟的資料夾路徑", "The path where an extension is installed.": "安裝延伸模組的路徑。", "The task runner's current working directory on startup": "啟用時工作執行器目前工作目錄", "Use the language of the currently active text editor if any": "使用目前使用的文字編輯器語言 (如果有的話)", "a conditional separator (' - ') that only shows when surrounded by variables with values": "條件式分隔符號 (' - ')，只會在前後為具有值的變數時顯示", "an indicator for when the active editor has unsaved changes": "當使用中編輯器具有未儲存變更的指示器", "e.g. SSH": "例如 SSH", "e.g. VS Code": "例如 VS Code", "file path of the workspace (e.g. /Users/<USER>/myWorkspace)": "工作區檔案路徑 (例如 /Users/<USER>/myWorkspace)", "file path of the workspace folder the file is contained in (e.g. /Users/<USER>/myFolder)": "包含在工作區內的檔案路徑 (例如 /Users/<USER>/myFolder)", "gist": "Gist", "name of the workspace folder the file is contained in (e.g. myFolder)": "包含在工作區內的檔案名稱 (例如 myFolder)", "name of the workspace with optional remote name and workspace indicator if applicable (e.g. myFolder, myRemoteFolder [SSH] or myWorkspace (Workspace))": "如果適用 (例如 myFolder、myRemoteFolder [SSH] 或 myWorkspace (Workspace))，則為具有選擇性遠端名稱與工作區指示器的工作區名稱。", "shortened name of the workspace without suffixes (e.g. myFolder or myWorkspace)": "沒有尾碼之工作區名稱 (例如 myFolder 或 myWorkspace) 的縮短名稱", "the file name (e.g. myFile.txt)": "檔案名稱(例如：myFile.txt)", "the full path of the file (e.g. /Users/<USER>/myFolder/myFileFolder/myFile.txt)": "檔案的完整路徑 (例如 /Users/<USER>/myFolder/myFileFolder/myFile.txt)", "the full path of the folder the file is contained in (e.g. /Users/<USER>/myFolder/myFileFolder)": "包含檔案的資料夾完整路徑 (例如 /Users/<USER>/myFolder/myFileFolder)", "the name of the active branch in the active repository (e.g. main)": "使用中存放庫中使用中分支的名稱 (例如 main)", "the name of the active repository (e.g. vscode)": "使用中存放庫的名稱 (例如 vscode)", "the name of the folder the file is contained in (e.g. myFileFolder)": "包含檔案的資料夾名稱 (如 myFileFolder)", "the path of the file relative to the workspace folder (e.g. myFolder/myFileFolder/myFile.txt)": "相對於工作區資料夾的檔案路徑 (如 myFolder/myFileFolder/myFile.txt)", "the path of the folder the file is contained in, relative to the workspace folder (e.g. myFolder/myFileFolder)": "包含檔案的資料夾路徑，相對於工作區資料夾 (如 myFolder/myFileFolder)", "the state of the active editor (e.g. modified).": "使用中的編輯器狀態 (例如，已修改)。"}, "package": {"description": "在設定、啟動及延伸模組建議檔案等組態檔案中，提供進階 IntelliSense 及自動修正等功能。", "displayName": "編輯設定值"}}}