{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "properties": {"formatting": {"type": "object", "description": "Keywords are presented according to the case configuration", "properties": {"trimNewlines": {"type": "boolean", "description": "Trim the original multiple newlines to keep one blank line."}, "trimFinalNewlines": {"type": "boolean", "description": "Trim the final newlines down to one newline"}, "trimTrailingWhitespace": {"type": "boolean", "description": "Trim the trailing white spaces of a line."}, "insertFinalNewline": {"type": "boolean", "description": "Insert a newline at the end of the file if one does not exist."}, "multiStatementsOneLine": {"type": "boolean", "description": "Specify whether to allow multiple statements on the same line."}, "alignInlineComments": {"type": "integer", "description": "Specify the column to which the inline comments are aligned when possible.", "minimum": 0}, "spacing.operator": {"type": "integer", "description": "Specify the number of whitespaces around binary operators such as “+”, ”-“, ”:”, etc.", "minimum": 0}, "spacing.assignment": {"type": "integer", "description": "Specify the number of whitespaces around assignment symbols such as “=”, “+=”, “:=”, etc", "minimum": 0}, "spacing.semicolon": {"type": "integer", "description": "Specify the number of whitespaces after a semicolon delimiter between statements on the same line.", "minimum": 0}, "spacing.comma": {"type": "integer", "description": "Specify the number of whitespaces after a comma delimiter in a parameter list", "minimum": 0}, "spacing.parentheses": {"type": "integer", "description": "Specify the number of whitespaces after the open parentheses and before the close parentheses for a parameter list or matrix reference.", "minimum": 0}, "spacing.squareBrackets": {"type": "integer", "description": "Specify the number of whitespaces after the open squareBrackets and before the close squareBrackets for a parameter list or matrix reference.", "minimum": 0}, "spacing.angleBrackets": {"type": "integer", "description": "Specify the number of whitespaces after the open angle bracket and before the close angle bracket for dynamic arrays.", "minimum": 0}, "style.operator": {"type": "string", "description": "Specify whether to use a symbol or keyword for operators such as *GT*, *LE*.", "oneOf": [{"const": "symbol"}, {"const": "keyword"}, {"const": "keep"}]}, "style.keyword": {"type": "string", "description": "Specify whether to use the full name or abbreviated name for keywords that support abbreviation.", "oneOf": [{"const": "full"}, {"const": "abbr"}, {"const": "keep"}]}, "style.commentMark": {"type": "string", "description": "Specify the style of comment mark.", "oneOf": [{"const": "*"}, {"const": "!"}, {"const": "$*"}, {"const": "rem"}, {"const": "keep"}]}, "style.keywordCase": {"type": "string", "description": "Specify the case for the keywords.", "oneOf": [{"const": "uppercase"}, {"const": "lowercase"}, {"const": "titlecase"}, {"const": "keep"}]}, "indent.base": {"type": "integer", "description": "Specify the number of TAB’s to use as the base indentation for all statements.", "minimum": 0}, "indent.block": {"type": "boolean", "description": "Specify whether to indent statements in block clauses. The spaces for each level of indentation is configured by the *tabSize* in the Visual Studio Code."}, "indent.directive": {"type": "boolean", "description": "Specify whether to indent the directives as statements."}, "indent.clause": {"type": "boolean", "description": "If a clause is starting at a new line, then this option controls whether to indent whole clause (including the clause keyword)."}, "indent.commentLine": {"type": "boolean", "description": "Whether to indent comment lines as statements. If set to *true*, the comment line will be indented the same as the statement following the comment."}, "clause.useBlock": {"type": "boolean", "description": "Specify to always use a block for clauses"}, "clause.addBlankLines": {"type": "boolean", "description": "For block clauses, always add a blank line before and after the block."}, "clause.atNewline": {"type": "boolean", "description": "Specify to always ensure that clause keywords (*THEN/ELSE/LOCKED/ON ERROR*) start at a new line."}, "routine.labelPattern": {"type": "string", "description": "Specify a pattern for labels. A Statement’s indentation under these labels is the same as subroutines. For other statements, the indentation is the same as the main routine."}}}}}