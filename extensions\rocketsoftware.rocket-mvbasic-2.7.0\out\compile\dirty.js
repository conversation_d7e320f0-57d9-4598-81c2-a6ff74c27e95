"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirtyFileBox = void 0;
const vscode = require("vscode");
const extension_1 = require("../extension");
class DirtyFileBox {
    constructor() {
        this.dirtyFiles = [];
    }
    pick(uri) {
        // Add current opened file to dirty file list.
        // Because user click compile on current opened file.
        const currentFile = uri.fsPath;
        this.add(currentFile);
        // If only one file and its current open docment,
        // then no need show the list.
        if (this.dirtyFiles.length <= 1) {
            const workspaces = vscode.workspace.workspaceFolders;
            if (!workspaces || workspaces.length == 0) {
                return Promise.resolve([]);
            }
            const fileName = this.getFileName(currentFile);
            if (fileName === undefined) {
                return Promise.resolve(undefined);
            }
            const result = [];
            result.push({ label: fileName, detail: currentFile });
            return Promise.resolve(result);
        }
        const items = [];
        for (const dirtyItem of this.dirtyFiles) {
            const filePath = dirtyItem[0];
            const fileName = dirtyItem[1];
            items.push({ label: fileName, detail: filePath });
        }
        return vscode.window.showQuickPick(items, { canPickMany: true });
    }
    add(file) {
        const workspaces = vscode.workspace.workspaceFolders;
        if (!workspaces || workspaces.length == 0) {
            return;
        }
        // Check whether the file existing in the dirty box
        const lowerFilePath = file.toLowerCase().replace("\\", "/");
        for (const item of this.dirtyFiles) {
            const dirtyFilePath = item[0].toLowerCase().replace("\\", "/");
            if (dirtyFilePath === lowerFilePath) {
                return;
            }
        }
        const fileName = this.getFileName(file);
        if (fileName === undefined) {
            return;
        }
        this.dirtyFiles.push([file, fileName]);
    }
    remove(file) {
        const lowerFilePath = file.toLowerCase();
        this.dirtyFiles = this.dirtyFiles.filter(item => item[0].toLowerCase() !== lowerFilePath);
    }
    refresh() {
        this.dirtyFiles = [];
    }
    getFiles(pickItems) {
        const workspaces = vscode.workspace.workspaceFolders;
        if (!workspaces || workspaces.length == 0) {
            return [];
        }
        const result = [];
        pickItems.forEach(item => {
            if (item.detail) {
                result.push(item.detail);
            }
        });
        return result;
    }
    getFileName(filePath) {
        let pathToReplace = undefined;
        if (extension_1.online !== undefined) {
            const accountUri = extension_1.online.getAccountUri();
            pathToReplace = accountUri ? accountUri.fsPath : undefined;
        }
        else {
            const curWs = this.getWorkspace(filePath);
            if (curWs !== undefined) {
                pathToReplace = curWs.uri.fsPath;
            }
        }
        return filePath.replace(pathToReplace + "\\", "").replace(pathToReplace + "/", "");
    }
    getWorkspace(docPath) {
        const workspaces = vscode.workspace.workspaceFolders;
        if (!workspaces || workspaces.length == 0) {
            return undefined;
        }
        let ret = undefined;
        let maxLen = 0;
        const tempDocPath = docPath.toLowerCase().replace('\\', '/');
        for (const ws of workspaces) {
            const wsPath = ws.uri.fsPath.toLowerCase().replace('\\', '/');
            if (tempDocPath.includes(wsPath) && tempDocPath.length > maxLen) {
                ret = ws;
                maxLen = ret.uri.fsPath.length;
            }
        }
        return ret;
    }
}
exports.DirtyFileBox = DirtyFileBox;
//# sourceMappingURL=dirty.js.map