"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UvCompiler = void 0;
const compiler_1 = require("./compiler");
class UvCompiler extends compiler_1.Compiler {
    constructor(_definition) {
        super();
        this._definition = _definition;
    }
    status(msg) {
        if (this.offline(msg)) {
            return compiler_1.Status.SERVER_NOT_CONNECTED;
        }
        return this.isIgnored(msg) ? compiler_1.Status.INVALID_FILE : this.isFailed(msg) ? compiler_1.Status.BUILD_FAILED : compiler_1.Status.BUILD_SUCCEED;
    }
    getConfig() {
        const compile = this._definition.compile;
        let opt1 = "";
        let opt2 = "";
        let opt3 = "";
        if (compile.catalog != undefined) {
            opt1 = compile.catalog;
            opt2 = compile.initialCharacter ? compile.initialCharacter : "";
            opt3 = compile.arguments ? compile.arguments : "";
        }
        else {
            opt3 = compile.arguments ? compile.arguments : "";
        }
        return {
            option1: opt1,
            option2: opt2,
            option3: opt3,
            doCatalog: compile.catalog != undefined
        };
    }
}
exports.UvCompiler = UvCompiler;
//# sourceMappingURL=uvCompiler.js.map