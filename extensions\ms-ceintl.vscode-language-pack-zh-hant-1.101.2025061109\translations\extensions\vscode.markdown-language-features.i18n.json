{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"...1 additional file not shown": "...另外 1 個檔案未顯示", "...{0} additional files not shown": "...另外 {0} 個檔案未顯示", "Allow all content and script execution. Not recommended": "允許所有內容與指令碼執行。不建議", "Allow insecure content": "允許不安全的內容", "Allow insecure local content": "允許不安全的本機內容", "Always": "一律", "An unexpected error occurred while restoring the Markdown preview.": "還原 Markdown 預覽時發生意外的錯誤。", "Checking for Markdown links to update": "正在檢查要更新的 Markdown 連結", "Content Disabled Security Warning": "內容已停用安全性警告", "Could not load 'markdown.styles': {0}": "無法載入 ‘markdown.style' 樣式:{0}", "Could not open {0}": "無法開啟 {0}", "Disable": "停用", "Disable preview security warning in this workspace": "不允許此工作區預覽安全性警告", "Disable validation of Markdown links": "停用 Markdown 連結的驗證", "Does not affect the content security level": "不影響內容安全性層級", "Enable": "啟用", "Enable loading content over http": "啟用 http 載入內容", "Enable loading content over http served from localhost": "啟用從本機提供 http 載入內容", "Enable preview security warnings in this workspace": "允許此工作區預覽安全性警告", "Enable validation of Markdown links": "啟用 Markdown 連結的驗證", "Exclude '{0}' from link validation.": "從連結驗證排除'{0}'。", "Extract to link definition": "擷取至連結定義", "File does not exist at path: {0}": "檔案不存在於路徑: {0}", "Find file references failed. No resource provided.": "尋找檔案參考失敗。未提供任何資源。", "Finding file references": "正在尋找檔案參考", "Follow link": "追蹤連結", "Go to link definition": "前往連結定義", "Header does not exist in file: {0}": "標頭不存在於檔案中: {0}", "Insert Markdown Audio": "插入 Markdown 音訊", "Insert Markdown Image": "插入 Markdown 影像", "Insert Markdown Images": "插入 Markdown 影像", "Insert Markdown Images and Links": "插入 Markdown 影像和連結", "Insert Markdown Link": "插入 Markdown 連結", "Insert Markdown Links": "插入 Markdown 連結", "Insert Markdown Media": "插入 Markdown 媒體", "Insert Markdown Media and Images": "插入 Markdown 媒體和影像", "Insert Markdown Media and Links": "插入 Markdown 媒體和連結", "Insert Markdown Video": "插入 Markdown 影片", "Insert image": "插入映像", "Insert link": "插入連結", "Link definition for '{0}' already exists": "'{0}' 的連結定義已存在", "Link definition is unused": "連結定義未使用", "Link is already a reference": "連結已經是參考", "Link is also defined here": "連結也定義在這裡", "Link to '# {0}' in '{1}'": "連結至 '{1}' 中的 '# {0}'", "Link to '{0}'": "連結至 '{0}'", "Markdown Language Server": "Markdown 語言伺服器", "Markdown link validation disabled": "Markdown 連結驗證已停用", "Markdown link validation enabled": "Markdown 連結驗證已啟用", "Media": "媒體", "More Information": "詳細資訊", "Never": "永不", "No": "否", "No header found: '{0}'": "找不到標頭: '{0}'", "No link definition found: '{0}'": "找不到連結定義: '{0}'", "Not on link": "不在連結上", "Only load secure content": "僅載入安全內容", "Paste and update pasted links": "貼上並更新貼上的連結", "Potentially unsafe or insecure content has been disabled in the Markdown preview. Change the Markdown preview security setting to allow insecure content or enable scripts": "Markdown 預覽中已停用可能不安全或無防護的內容。請將 Markdown 預覽的安全性設定變更為允許無防護的內容，或啟用指令碼", "Preview {0}": "預覽 {0}", "Reference link '{0}'": "參考連結 '{0}'", "Remove duplicate link definition": "移除重複的連結定義", "Remove unused link definition": "移除未使用的連結定義", "Renaming is not supported here. Try renaming a header or link.": "這裡不支援重新命名。嘗試重新命名標題或連結。", "Select security settings for Markdown previews in this workspace": "選擇此工作區 Markdown 預覽的安全性設定", "Some content has been disabled in this document": "此文件中的部分內容已停用", "Strict": "嚴謹", "Update Markdown links for '{0}'?": "更新 '{0}' 的 Markdown 連結嗎?", "Update Markdown links for the following {0} files?": "要更新下列 {0} 個檔案的 Markdown 連結嗎?", "Yes": "是", "[Preview] {0}": "[預覽] {0}", "{0} cannot be found": "找不到 {0}"}, "package": {"configuration.copyIntoWorkspace.mediaFiles": "嘗試將外部影像和影片檔案複製到工作區。", "configuration.copyIntoWorkspace.never": "請勿將外部檔案複製到工作區。", "configuration.markdown.copyFiles.destination": "設定複製/貼上或拖放所建立檔案的路徑和檔案名稱。這是 Glob 地圖，與 Markdown 應在其中建立新檔案之目的地路徑的文件路徑相符。\r\n\r\n目的地路徑可能會使用下列變數:\r\n\r\n- '${documentDirName}' - Markdown 文件的絕對父目錄路徑，例如 '/Users/<USER>/myProject/docs'。\r\n- '${documentRelativeDirName}' - Markdown 文件的相對父目錄路徑，例如 'docs'。如果檔案不是工作區的一部分，這和 '${documentDirName}' 相同。\r\n- '${documentFileName}' - Markdown 文件的完整檔案名稱，例如 'README.md'。\r\n- '${documentBaseName}' - Markdown 文件的基底名稱，例如 'README'。\r\n- '${documentExtName}' - Markdown 文件的副檔名，例如 'md'。\r\n- '${documentFilePath}' - Markdown 文件的絕對路徑，例如 '/Users/<USER>/myProject/docs/README.md'。\r\n- '${documentRelativeFilePath}' - Markdown 文件的相對路徑，例如 'docs/README.md'。如果檔案不是工作區的一部分，這和 '${documentFilePath}' 相同。\r\n- '${documentWorkspaceFolder}' - Markdown 文件的工作區資料夾，例如 '/Users/<USER>/myProject'。如果檔案不是工作區的一部分，這和 '${documentDirName}' 相同。\r\n- '${fileName}' - 已置放檔案的檔案名稱，例如 'image.png'。\r\n- '${fileExtName}' - 已置放檔案的副檔名，例如 'png'。\r\n- '${unixTime}' - 目前的 Unix 時間戳記 (以毫秒為單位)。", "configuration.markdown.copyFiles.overwriteBehavior": "控制藉由拖放或貼上建立的檔案是否應覆寫現有的檔案。", "configuration.markdown.copyFiles.overwriteBehavior.nameIncrementally": "如果已存在相同名稱的檔案，則附加數字至檔案名稱，例如: `image.png` 會變成 `image-1.png`。", "configuration.markdown.copyFiles.overwriteBehavior.overwrite": "如果已存在相同名稱的檔案，則覆寫該檔案。", "configuration.markdown.editor.drop.copyIntoWorkspace": "控制是否應該將拖放到 Markdown 編輯器的工作區外部的檔案複製到工作區。\r\n\r\n使用 `#markdown.copyFiles.destination#` 來設定應該建立拖放檔案的位置", "configuration.markdown.editor.drop.enabled": "按住 Shift，以啟用將檔案放入 Markdown 編輯器。需要啟用 `#editor.dropIntoEditor.enabled#`。", "configuration.markdown.editor.drop.enabled.always": "一律插入 Markdown 連結。", "configuration.markdown.editor.drop.enabled.never": "永不建立 Markdown 連結。", "configuration.markdown.editor.drop.enabled.smart": "未置放到程式碼區塊或其他特殊元素時，預設會智慧地建立 Markdown 連結。使用置放小工具在以純文字貼上或以 Markdown 連結方式貼上之間切換。", "configuration.markdown.editor.filePaste.audioSnippet": "將音訊新增至 Markdown 時使用的程式碼片段。此程式碼片段可以使用下列變數:\r\n- '${src}' — 音訊檔案的解析路徑。\r\n- '${title}' - 用於音訊的標題。將自動為此變數建立程式碼片段預留位置。", "configuration.markdown.editor.filePaste.copyIntoWorkspace": "控制是否應該將貼上到 Markdown 編輯器的工作區外部的檔案複製到工作區。\r\n\r\n使用 `#markdown.copyFiles.destination#` 來設定應該建立複製檔案的位置。", "configuration.markdown.editor.filePaste.enabled": "啟用將檔案貼上至 Markdown 編輯器以建立 Markdown 連結。需要啟用 `#editor.pasteAs.enabled#`。", "configuration.markdown.editor.filePaste.enabled.always": "一律插入 Markdown 連結。", "configuration.markdown.editor.filePaste.enabled.never": "永不建立 Markdown 連結。", "configuration.markdown.editor.filePaste.enabled.smart": "未貼上程式代碼區塊或其他特殊元素時，預設會智慧地建立 Markdown 連結。使用貼上小工具在以純文本貼上或以 Markdown 連結方式貼上之間切換。", "configuration.markdown.editor.filePaste.videoSnippet": "將影片新增至 Markdown 時使用的程式碼片段。此程式碼片段可以使用下列變數:\r\n- '${src}' — 視訊檔案的解析路徑。\r\n- '${title}' - 用於視訊的標題。將自動為此變數建立程式碼片段預留位置。", "configuration.markdown.editor.pasteUrlAsFormattedLink.enabled": "控制當 URL 被插入 Markdown 編輯器時，是否建立 Markdown 連結。需要啟用 `#editor.pasteAs.enabled#`。", "configuration.markdown.editor.updateLinksOnPaste.enabled": "啟用/停用貼上選項，其更新在 Markdown 編輯器之間複製和貼上的文字中的連結和參考。\r\n\r\n若要使用此功能，在貼上包含可更新連結的文字後，只需按一下貼上介面控件並選取 [貼上並更新已貼上連結]。", "configuration.markdown.links.openLocation.beside": "開啟使用中編輯器旁邊的連結。", "configuration.markdown.links.openLocation.currentGroup": "在使用中的編輯器群組開啟連結。", "configuration.markdown.links.openLocation.description": "控制應在何處開啟 Markdown 檔案中的連結。", "configuration.markdown.occurrencesHighlight.enabled": "啟用醒目提示目前文件中的連結發生次數。", "configuration.markdown.preferredMdPathExtensionStyle": "控制是否針對 Markdown 檔案的連結新增副檔名 (例如 `.md`)。使用工具 (例如路徑完成或檔案重新命名) 新增檔案路徑時，會使用此設定。", "configuration.markdown.preferredMdPathExtensionStyle.auto": "針對現有路徑，請嘗試維持副檔名樣式。針對新路徑，請新增副檔名。", "configuration.markdown.preferredMdPathExtensionStyle.includeExtension": "偏好包含副檔名。例如，名為 `file.md` 的檔案的路徑完成將插入 `file.md`。", "configuration.markdown.preferredMdPathExtensionStyle.removeExtension": "偏好移除副檔名。例如，名為 `file.md` 的檔案的路徑完成將插入 `file`，不含 `.md`。", "configuration.markdown.preview.openMarkdownLinks.description": "控制應如何開啟 Markdown 預覽中其他 Markdown 檔案的連結。", "configuration.markdown.preview.openMarkdownLinks.inEditor": "嘗試在編輯器中開啟連結。", "configuration.markdown.preview.openMarkdownLinks.inPreview": "嘗試開啟 Markdown 預覽中的連結。", "configuration.markdown.suggest.paths.enabled.description": "在 Markdown 檔案中寫入連結時啟用路徑建議。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions": "為目前工作區中其他 Markdown 檔案中的標頭啟用建議。接受其中一個建議會插入該檔案中標頭的完整路徑，例如: `[連結文字](/path/to/file.md#header)`。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.never": "停用工作區標頭建議。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.onDoubleHash": "啟用在路徑中輸入 `##` 之後的工作區標頭建議，例如: `[連結文字](##`。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.onSingleOrDoubleHash": "啟用在路徑中輸入 `##` 或 `#` 之後的工作區標頭建議，例如: `[連結文字](#` 或 `[連結文字](##`。", "configuration.markdown.updateLinksOnFileMove.enableForDirectories": "啟用在工作區中移動或重新命名目錄時更新連結。", "configuration.markdown.updateLinksOnFileMove.enabled": "當工作區中的檔案重新命名/移動時，嘗試更新 Markdown 檔案中的連結。使用 `#markdown.updateLinksOnFileMove.include#` 來設定哪些檔案觸發連結更新。", "configuration.markdown.updateLinksOnFileMove.enabled.always": "永遠自動更新連結。", "configuration.markdown.updateLinksOnFileMove.enabled.never": "永不嘗試更新連結且不提示。", "configuration.markdown.updateLinksOnFileMove.enabled.prompt": "每個檔案移動時提示。", "configuration.markdown.updateLinksOnFileMove.include": "Glob 模式，可指定觸發自動連結更新的檔案。如需此功能的詳細資料，請參閱 '#markdown.updateLinksOnFileMove.enabled#'。", "configuration.markdown.updateLinksOnFileMove.include.property": "符合檔案路徑的 Glob 模式。設為 [true] 以啟用該模式。", "configuration.markdown.validate.duplicateLinkDefinitions.description": "驗證目前檔案中重複的定義。", "configuration.markdown.validate.enabled.description": "啟用 Markdown 檔案中的所有錯誤報告。", "configuration.markdown.validate.fileLinks.enabled.description": "驗證 Markdown 檔案中其他檔案的連結，例如 `[link](/path/to/file.md)`。這會檢查目標檔案是否存在。需要啟用 `#markdown.validate.enabled#`。", "configuration.markdown.validate.fileLinks.markdownFragmentLinks.description": "驗證 Markdown 檔案中其他檔案中標題連結的片段部分，例如: `[link](/path/to/file.md#header)`。預設會繼承來自 `#markdown.validate.fragmentLinks.enabled#` 的設定值。", "configuration.markdown.validate.fragmentLinks.enabled.description": "驗證目前 Markdown 檔案中標頭的片段連結，例如: `[link](#header)`。需要啟用 `#markdown.validate.enabled#`。", "configuration.markdown.validate.ignoredLinks.description": "設定不應驗證的連結。例如新增 `/about` 不會驗證連結 `[about](/about)`，而 glob `/assets/**/*.svg` 可讓您略過驗證 'assets' 目錄下之 '.svg' 檔案的任何連結。", "configuration.markdown.validate.referenceLinks.enabled.description": "驗證 Markdown 檔案中的參考連結，例如: `[link][ref]`。需要啟用 `#markdown.validate.enabled#`。", "configuration.markdown.validate.unusedLinkDefinitions.description": "驗證目前檔案中未使用的連結定義。", "configuration.pasteUrlAsFormattedLink.always": "一律插入 Markdown 連結。", "configuration.pasteUrlAsFormattedLink.never": "永不建立 Markdown 連結。", "configuration.pasteUrlAsFormattedLink.smart": "未貼上程式代碼區塊或其他特殊元素時，預設會智慧地建立 Markdown 連結。使用貼上小工具在以純文本貼上或以 Markdown 連結方式貼上之間切換。", "configuration.pasteUrlAsFormattedLink.smartWithSelection": "當您已選取文字且未貼入程式代碼區塊或其他特殊元素時，預設會智慧地建立 Markdown 連結。使用貼上小工具在以純文本貼上或以 Markdown 連結方式貼上之間切換。", "description": "為 Markdown 提供豐富的語言支援。", "displayName": "Markdown 語言功能", "markdown.copyImage.title": "複製影像", "markdown.editor.insertImageFromWorkspace": "從工作區插入映像", "markdown.editor.insertLinkFromWorkspace": "在工作區中插入檔案連結", "markdown.findAllFileReferences": "尋找檔案參考", "markdown.openImage.title": "開啟影像", "markdown.preview.breaks.desc": "設定在 Markdown 預覽中轉譯分行符號的方式。設定為 `true` 會為段落內的新行建立 `<br>`。", "markdown.preview.doubleClickToSwitchToEditor.desc": "在 Markdown 預覽中按兩下會切換到編輯器。", "markdown.preview.fontFamily.desc": "控制 Markdown 預覽中使用的字型家族。", "markdown.preview.fontSize.desc": "控制 Markdown 預覽中使用的字型大小 (以像素為單位)。", "markdown.preview.lineHeight.desc": "控制 Markdown 預覽中使用的行高。此數字與字型大小成正比。", "markdown.preview.linkify": "在 Markdown 預覽中將類似 URL 的文字轉換為連結。", "markdown.preview.markEditorSelection.desc": "在 Markdown 預覽中標記目前的編輯器選取範圍。", "markdown.preview.refresh.title": "重新整理預覽", "markdown.preview.scrollEditorWithPreview.desc": "在捲動 Markdown 預覽時更新編輯器的檢視。", "markdown.preview.scrollPreviewWithEditor.desc": "在捲動 Markdown 編輯器時更新預覽的檢視。", "markdown.preview.title": "開啟預覽", "markdown.preview.toggleLock.title": "切換預覽鎖定", "markdown.preview.typographer": "在 Markdown 預覽中啟用部分非語言相關的取代及引用修飾。", "markdown.previewSide.title": "在一側開啟預覽", "markdown.server.log.desc": "控制 Markdown 語言伺服器的記錄層級。", "markdown.showLockedPreviewToSide.title": "在側面開啟鎖定的預覽", "markdown.showPreviewSecuritySelector.title": "變更預覽的安全性設定", "markdown.showSource.title": "顯示來源", "markdown.styles.dec": "可從 Markdown 預覽使用之 CSS 樣式表的 URL 或本機路徑清單。相對路徑指的是相對於在總管中開啟的資料夾。若沒有開啟的資料夾，相對路徑則是相對於 Markdown 檔案的位置。所有 '\\' 都必須寫成 '\\\\'。", "markdown.trace.extension.desc": "允許 Markdown 延伸模組進行偵錯記錄。", "markdown.trace.server.desc": "追蹤 VS Code 與 Markdown 語言伺服器之間的通訊。", "workspaceTrust": "載入工作區中設定的樣式時所需。"}}}