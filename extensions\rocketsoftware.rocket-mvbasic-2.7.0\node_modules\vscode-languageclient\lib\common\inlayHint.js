"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InlayHintsFeature = void 0;
const vscode_1 = require("vscode");
const vscode_languageserver_protocol_1 = require("vscode-languageserver-protocol");
const features_1 = require("./features");
class InlayHintsFeature extends features_1.TextDocumentLanguageFeature {
    constructor(client) {
        super(client, vscode_languageserver_protocol_1.InlayHintRequest.type);
    }
    fillClientCapabilities(capabilities) {
        const inlayHint = (0, features_1.ensure)((0, features_1.ensure)(capabilities, 'textDocument'), 'inlayHint');
        inlayHint.dynamicRegistration = true;
        inlayHint.resolveSupport = {
            properties: ['tooltip', 'textEdits', 'label.tooltip', 'label.location', 'label.command']
        };
        (0, features_1.ensure)((0, features_1.ensure)(capabilities, 'workspace'), 'inlayHint').refreshSupport = true;
    }
    initialize(capabilities, documentSelector) {
        this._client.onRequest(vscode_languageserver_protocol_1.InlayHintRefreshRequest.type, async () => {
            for (const provider of this.getAllProviders()) {
                provider.onDidChangeInlayHints.fire();
            }
        });
        const [id, options] = this.getRegistration(documentSelector, capabilities.inlayHintProvider);
        if (!id || !options) {
            return;
        }
        this.register({ id: id, registerOptions: options });
    }
    registerLanguageProvider(options) {
        const selector = options.documentSelector;
        const eventEmitter = new vscode_1.EventEmitter();
        const provider = {
            onDidChangeInlayHints: eventEmitter.event,
            provideInlayHints: (document, viewPort, token) => {
                const client = this._client;
                const provideInlayHints = async (document, viewPort, token) => {
                    const requestParams = {
                        textDocument: client.code2ProtocolConverter.asTextDocumentIdentifier(document),
                        range: client.code2ProtocolConverter.asRange(viewPort)
                    };
                    try {
                        const values = await client.sendRequest(vscode_languageserver_protocol_1.InlayHintRequest.type, requestParams, token);
                        if (token.isCancellationRequested) {
                            return null;
                        }
                        return client.protocol2CodeConverter.asInlayHints(values, token);
                    }
                    catch (error) {
                        return client.handleFailedRequest(vscode_languageserver_protocol_1.InlayHintRequest.type, token, error, null);
                    }
                };
                const middleware = client.middleware;
                return middleware.provideInlayHints
                    ? middleware.provideInlayHints(document, viewPort, token, provideInlayHints)
                    : provideInlayHints(document, viewPort, token);
            }
        };
        provider.resolveInlayHint = options.resolveProvider === true
            ? (hint, token) => {
                const client = this._client;
                const resolveInlayHint = async (item, token) => {
                    try {
                        const value = await client.sendRequest(vscode_languageserver_protocol_1.InlayHintResolveRequest.type, client.code2ProtocolConverter.asInlayHint(item), token);
                        if (token.isCancellationRequested) {
                            return null;
                        }
                        const result = client.protocol2CodeConverter.asInlayHint(value, token);
                        return token.isCancellationRequested ? null : result;
                    }
                    catch (error) {
                        return client.handleFailedRequest(vscode_languageserver_protocol_1.InlayHintResolveRequest.type, token, error, null);
                    }
                };
                const middleware = client.middleware;
                return middleware.resolveInlayHint
                    ? middleware.resolveInlayHint(hint, token, resolveInlayHint)
                    : resolveInlayHint(hint, token);
            }
            : undefined;
        return [this.registerProvider(selector, provider), { provider: provider, onDidChangeInlayHints: eventEmitter }];
    }
    registerProvider(selector, provider) {
        return vscode_1.languages.registerInlayHintsProvider(this._client.protocol2CodeConverter.asDocumentSelector(selector), provider);
    }
}
exports.InlayHintsFeature = InlayHintsFeature;
