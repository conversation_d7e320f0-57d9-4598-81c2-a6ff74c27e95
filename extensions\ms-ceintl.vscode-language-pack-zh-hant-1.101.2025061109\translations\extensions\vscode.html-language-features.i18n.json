{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"\"store\" a boolean test for later evaluation in a guard or if().": "\"store\" 一個布林值測試，以供稍後在 guard 或 if() 中評估。", "'from' expected": "必須有 'from'。", "'in' expected": "必須有 'in'", "'through' or 'to' expected": "必須是 'through' 或 'to'", "'{0}'": "'{0}'", "( expected": "必須有 (", ") expected": "必須有 )", "<undefined>": "<未定義>", "@font-face": "@font-face", "@font-face rule must define 'src' and 'font-family' properties": "@font-face 規則必須定義 'src' 和 'font-family' 屬性", "@keyframes {0}": "@keyframes {0}", "A list of properties that are not validated against the `unknownProperties` rule.": "未對 `unknownProperties` 規則驗證的屬性清單。", "Adds quotes to a string.": "新增引號到字串。", "Also define the standard property '{0}' for compatibility": "也定義標準屬性 '{0}' 以取得相容性", "Always define standard rule '@keyframes' when defining keyframes.": "定義主要畫面格時，一律定義標準規則 '@keyframes'。", "Always include all vendor specific properties: Missing: {0}": "一律包含所有廠商特定屬性: 遺失: {0}", "Always include all vendor specific rules: Missing: {0}": "一律包含所有廠商特定規則: 遺失: {0}", "Appends a single value onto the end of a list.": "將單一值附加到清單結尾。", "Appends selectors to one another without spaces in between.": "將選取器附加至彼此，但兩者之間沒有空格。", "Avoid using !important. It is an indication that the specificity of the entire CSS has gotten out of control and needs to be refactored.": "避免使用 !important。這表示整個 CSS 的明確性皆失控，需要重構。", "Avoid using 'float'. Floats lead to fragile CSS that is easy to break if one aspect of the layout changes.": "避免使用 'float'。浮動會使 CSS 脆弱，在版面配置的任一層面改變時容易中斷。", "Causes one or more rules to be emitted at the root of the document.": "導致在文件根目錄發出一或多個規則。", "Changes one or more properties of a color.": "變更色彩的一或多個屬性。", "Changes the alpha component for a color.": "變更色彩的 Alpha 元件。", "Changes the hue of a color.": "變更色彩的色調。", "Character entity representing '{0}'": "代表 '{0}' 的字元實體", "Character entity representing '{0}', unicode equivalent '{1}'": "代表 '{0}' 的字元實體 (對等 '{1}' 的 Unicode)", "Closing bracket expected.": "必須為右中括號。", "Closing bracket missing.": "遺漏右括號。", "Combines several lists into a single multidimensional list.": "將數個清單合併成單一多維度清單。", "Configure": "設定", "Converts a color into the format understood by IE filters.": "將色彩轉換成 IE 篩選能理解的格式。", "Converts a color to grayscale.": "將色彩轉換為灰階。", "Converts a string to lower case.": "將字串轉換為小寫。", "Converts a string to upper case.": "將字串轉換為大寫。", "Converts a unitless number to a percentage.": "將無單位數字轉換成百分比。", "Creates a Color from hue, saturation, and lightness values.": "從色調、飽和度和亮度值建立色彩。", "Creates a Color from hue, saturation, lightness, and alpha values.": "從色調、飽和度、亮度和 Alpha 值建立色彩。", "Creates a Color from hue, white, and black values.": "從色調、白色和黑色值建立色彩。", "Creates a Color from lightness, a, and b values.": "根據亮度、a 和 b 值建立色彩。", "Creates a Color from lightness, chroma, and hue values.": "根據亮度、色度和色調值建立色彩。", "Creates a Color from red, green, and blue values.": "從紅色、綠色和藍色值建立色彩。", "Creates a Color from red, green, blue, and alpha values.": "從紅色、綠色、藍色和 Alpha 值建立色彩。", "Creates a Color from the hue, saturation, and lightness values of another Color.": "根據其他色彩的色調、彩度和亮度值建立色彩。", "Creates a Color from the hue, white, and black values of another Color.": "根據其他色彩的色調、白色和黑色值建立色彩。", "Creates a Color from the lightness, a, and b values of another Color.": "根據其他色彩的亮度、a 和 b 值建立色彩。", "Creates a Color from the lightness, chroma, and hue values of another Color.": "根據其他色彩的亮度、色度和色調值建立色彩。", "Creates a Color from the red, green, and blue values of another Color.": "根據其他色彩的紅色、綠色和藍色值建立色彩。", "Creates a Color in a specific color space from red, green, and blue values.": "根據紅色、綠色和藍色值在特定色彩空間中建立色彩。", "Creates a Color in a specific color space from the red, green, and blue values of another Color.": "根據其他色彩的紅色、綠色和藍色值，在特定的色彩空間中建立色彩。", "Defines complex operations that can be re-used throughout stylesheets.": "定義可在樣式表中重新使用的複雜作業。", "Defines styles that can be re-used throughout the stylesheet with `@include`.": "定義可以使用 '@include' 在整個樣式表中重複使用的樣式。", "Do not use duplicate style definitions": "請勿使用重複的樣式定義", "Do not use empty rulesets": "請勿使用空白規則集", "Do not use width or height when using padding or border": "使用 padding 或 border 時不要使用 width 或 height。", "Dynamically calls a Sass function.": "動態呼叫 Sass 函數。", "Each loop that sets `$var` to each item in the list or map, then outputs the styles it contains using that value of `$var`.": "將 '$var' 設為清單或對應中每個項目的每個迴圈，然後使用 '$var' 值輸出其中包含的樣式。", "End tag name expected.": "必須有結束標記名稱。", "Exposes the details of Sass’s inner workings.": "公開 Sass 內部工作的詳細資料。", "Extends $extendee with $extender within $selector.": "使用 $selector 內的 $extender 擴充 $extendee。", "Extracts a substring from $string.": "從 $string 擷取 substring。", "Finds the maximum of several numbers.": "尋找數個數字的最大值。", "Finds the minimum of several numbers.": "尋找數個數字的最小值。", "Fluidly scales one or more properties of a color.": "流暢地縮放色彩的一或多個屬性。", "Folding Region End": "摺疊區域結束", "Folding Region Start": "摺疊區域開始", "For loop that repeatedly outputs a set of styles for each `$var` in the `from/through` or `from/to` clause.": "適用於重複輸出 'from/through' 或 'from/to' 子句中每個 '$var' 之一組樣式的迴圈。", "Generates new colors based on existing ones, making it easy to build color themes.": "根據現有的色彩產生新色彩，輕鬆建置色彩主題。", "Gets the blue component of a color.": "取得色彩的藍色元件。", "Gets the green component of a color.": "取得色彩的綠色元件。", "Gets the hue component of a color.": "取得色彩的色調元件。", "Gets the lightness component of a color.": "取得色彩的亮度元件。", "Gets the opacity component of a color.": "取得色彩的不透明度元件。", "Gets the red component of a color.": "取得色彩的紅色元件。", "Gets the saturation component of a color.": "取得色彩的彩度元件。", "HTML Language Server": "HTML 語言伺服器", "Hex colors must consist of three, four, six or eight hex numbers": "十六進位色彩必須由三、四、六或八個十六進位數字組成", "IE hacks are only necessary when supporting IE7 and older": "只有在支援 IE7 及更舊的版本時才需要 IE Hack", "Import statements do not load in parallel": "匯入陳述式不會平行載入", "Includes the body if the expression does not evaluate to `false` or `null`.": "如果運算式的計算結果不是 'false' 或 'null'，則包含主體。", "Includes the styles defined by another mixin into the current rule.": "將另一個 Mixen 定義的樣式包含至目前的規則中。", "Increases or decreases one or more components of a color.": "增加或減少色彩的一或多個元件。", "Inherits the styles of another selector.": "繼承另一個選取器的樣式。", "Inserts $insert into $string at $index.": "將 $insert 於 $index 插入 $string。", "Invalid number of parameters": "無效的參數數目", "Joins together two lists into one.": "將兩個清單結合成一個清單。", "Lets you access and modify values in lists.": "可讓您存取及修改清單中的值。", "Loads a Sass stylesheet and makes its mixins, functions, and variables available when this stylesheet is loaded with the @use rule.": "載入 Sass 樣式表，當此樣式表載入 @use 規則時，使用其 mixin、函數和變數。", "Loads mixins, functions, and variables from other Sass stylesheets as 'modules', and combines CSS from multiple stylesheets together.": "將來自其他 Sass 樣式表的 Mixin、函數和變數載入為「模組」，並將來自多個樣式表的 CSS 結合。", "Makes a color darker.": "讓色彩更深。", "Makes a color less saturated.": "讓色彩較不飽和。", "Makes a color lighter.": "讓色彩更淺。", "Makes a color more opaque.": "讓色彩更不透明。", "Makes a color more saturated.": "讓色彩更飽和。", "Makes a color more transparent.": "讓色彩更透明。", "Makes it easy to combine, search, or split apart strings.": "輕鬆合併、搜尋或分割開字串。", "Makes it possible to look up the value associated with a key in a map, and much more.": "可讓您查詢與對應中索引碼相關聯的值，還有更多功能。", "Merges two maps together into a new map.": "將兩張地圖合併成一張新地圖。", "Mix two colors together in a polar color space.": "在兩極色彩空間中將兩種色彩混合在一起。", "Mix two colors together in a rectangular color space.": "在矩形色彩空間中將兩種色彩混合在一起。", "Mixes two colors together.": "將兩種色彩混合在一起。", "Nests selector beneath one another like they would be nested in the stylesheet.": "選取器巢狀於彼此之下，就像在樣式表中築巢一樣。", "No unit for zero needed": "零不需要任何單位", "Parses a selector into the format returned by &.": "將選取器剖析為 & 所傳回的格式。", "Prints the value of an expression to the standard error output stream. Useful for debugging complicated Sass files.": "將運算式的值列印到標準錯誤輸出資料流。適用於偵錯複雜的 Sass 檔案。", "Prints the value of an expression to the standard error output stream. Useful for libraries that need to warn users of deprecations or recovering from minor mixin usage mistakes. Warnings can be turned off with the `--quiet` command-line option or the `:quiet` Sass option.": "將運算式的值輸出到標準錯誤輸出資料流。適用於需要警告使用者淘汰或從次要 mixin 使用錯誤復原的程式庫。可以使用 '--quiet' 命令列選項或 `:quiet` Sass 選項關閉警告。", "Property is ignored due to the display.": "由於顯示的原因已略過屬性。", "Property is ignored due to the display. With 'display: block', vertical-align should not be used.": "由於顯示，已略過屬性。針對 'display: block'，不應使用垂直對齊。", "Provides access to Sass’s powerful selector engine.": "提供存取 Sass 強大的選取器引擎。", "Provides functions that operate on numbers.": "提供在數字上作業的函數。", "Removes quotes from a string.": "移除字串中的引號。", "Rename to '{0}'": "重新命名為 '{0}'", "Replaces $original with $replacement within $selector.": "以 $selector 内的 $replacement 取代 $original。", "Replaces the nth item in a list.": "取代清單中的第 n 個項目。", "Returns a list of all keys in a map.": "傳回對應中所有索引碼的清單。", "Returns a list of all values in a map.": "傳回對應中所有值的清單。", "Returns a new map with keys removed.": "傳回已移除金鑰的新地圖。", "Returns a random number.": "傳回亂數。", "Returns a specific item in a list.": "傳回清單中的特定項目。", "Returns the absolute value of a number.": "傳回數字的絕對值。", "Returns the complement of a color.": "傳回色彩的補數。", "Returns the index of the first occurance of $substring in $string.": "傳回 $string 中第一次發生之 $substring 的索引。", "Returns the inverse of a color.": "傳回色彩的反函數。", "Returns the keywords passed to a function that takes variable arguments.": "傳回傳遞至採用變數引數之函數的關鍵字。", "Returns the length of a list.": "傳回清單的長度。", "Returns the number of characters in a string.": "傳回字串中的字元數", "Returns the position of a value within a list.": "傳回值在清單中的位置。", "Returns the separator of a list.": "傳回清單的分隔線。", "Returns the simple selectors that comprise a compound selector.": "傳回組成複合選取器的簡單選取器。", "Returns the string representation of a value as it would be represented in Sass.": "傳回值在 Sass 中表示的字串。", "Returns the type of a value.": "傳回值的類型。", "Returns the unit(s) associated with a number.": "傳回與數字相關聯的單位。", "Returns the value in a map associated with a given key.": "傳回與指定索引碼相關之對應中的值。", "Returns whether $super matches all the elements $sub does, and possibly more.": "傳回 $super 是否與 $sub 符合的所有元素相符，以及其符合項目是否更多。", "Returns whether a feature exists in the current Sass runtime.": "傳回功能是否存在於目前的 Sass 執行階段中。", "Returns whether a function with the given name exists.": "傳回具有指定名稱的函數是否存在。", "Returns whether a map has a value associated with a given key.": "返回對應是否有與指定索引碼相關的值。", "Returns whether a mixin with the given name exists.": "傳回具有指定名稱的 Mixin 是否存在。", "Returns whether a number has units.": "傳回數字是否有單位。", "Returns whether a variable with the given name exists in the current scope.": "傳回具有指定名稱的變數是否存在於目前範圍中。", "Returns whether a variable with the given name exists in the global scope.": "傳回具有指定名稱的變數是否存在於全域範圍中。", "Returns whether two numbers can be added, subtracted, or compared.": "傳回兩個數字是否可相加、相減或比較。", "Rounds a number down to the previous whole number.": "將數字無條件捨去到前一個整數。", "Rounds a number to the nearest whole number.": "將數字捨去到最接近的整數。", "Rounds a number up to the next whole number.": "將數字無條件進位入到下一個整數。", "Sass documentation": "Sass 文件", "Selector Specificity": "選取器明確性", "Selectors should not contain IDs because these rules are too tightly coupled with the HTML.": "選取器不應包含 ID，因為這些規則與 HTML 結合過於緊密。", "Simple HTML5 starting point": "簡易的 HTML5 起點", "Start tag name expected.": "必須有開始標記名稱。", "Tag name must directly follow the open bracket.": "標記名稱必須直接跟在左括號後。", "The universal selector (*) is known to be slow": "已知通用選取器 (*) 速度緩慢", "Throws the value of an expression as a fatal error with stack trace. Useful for validating arguments to mixins and functions.": "擲出運算式的值，作為堆疊追蹤的嚴重錯誤。適用於要驗證 Mixin 和函數的引數。", "URI expected": "必須有 URI", "URL encodes a string": "URL 編碼字串", "Unexpected character in tag.": "標籤中有未預期的字元。", "Unifies two selectors to produce a selector that matches elements matched by both.": "統一兩個選取器，以產生與兩者相符的元素符合的選取器。", "Unknown at-rule.": "未知的 at-rule。", "Unknown property.": "未知的屬性。", "Unknown property: '{0}'": "未知的屬性: '{0}'", "Unknown vendor specific property.": "未知的廠商特定屬性。", "VS Code now has built-in support for auto-renaming tags. Do you want to enable it?": "VS Code 現在有內建的自動重新命名標籤支援。要啟用嗎?", "When using a vendor-specific prefix also include the standard property": "在使用廠商專屬的前置詞時，也包括標準屬性", "When using a vendor-specific prefix make sure to also include all other vendor-specific properties": "在使用廠商專屬的前置詞時，請確定也包括其他所有的廠商專屬屬性", "While loop that takes an expression and repeatedly outputs the nested styles until the statement evaluates to `false`.": "While 迴圈會採用運算式並重複輸出巢狀樣式，直到陳述式評估為 `false` 為止。", "[ expected": "必須有 [", "] expected": "必須是 ]", "absolute value of a number": "數字的絕對值", "arccosine - inverse of cosine function": "反餘弦 - 餘弦的反向", "arcsine - inverse of sine function": "反正弦 - 正弦函數的反向", "arctangent - inverse of tangent function": "反正切 - 正切函數的反向", "argument from '{0}'": "來自 '{0}' 的引數", "at-rule or selector expected": "必須有 at-rule 或選取器", "at-rule unknown": "未知的 at-rule", "bind the evaluation of a ruleset to each member of a list.": "將規則集的評估與清單的每個成員繫結。", "calculates square root of a number": "計算數字的平方根", "colon expected": "必須有冒號", "comma expected": "必須有逗號", "condition expected": "必須是條件", "converts numbers from one type into another": "將數字從一種類型轉換成另一種類型", "converts to a %, e.g. 0.5 > 50%": "轉換成 %，例如 0.5 > 50%", "cosine function": "餘弦函數", "creates a #AARRGGBB": "建立 #AARRGGBB", "creates a color": "建立色彩", "css.builtin.lab": "css.builtin.lab", "dot expected": "必須是點", "escape string content": "逸出字串內容", "expression expected": "必須是運算式", "first argument modulus second argument": "第一個引數對第二個引數取用模數", "first argument raised to the power of the second argument": "第一個引數的第二個引數次方", "generate a list spanning a range of values": "產生跨越值範圍的清單", "identifier expected": "必須是識別碼", "identifier or variable expected": "必須是識別碼或變數", "identifier or wildcard expected": "必須有識別碼或萬用字元", "inline-block is ignored due to the float. If 'float' has a value other than 'none', the box is floated and 'display' is treated as 'block'": "由於 float，因此略過 inline-block。如果 'float' 的值不是 'none'，則會浮動該方塊，且將 'display' 視為 'block'", "inlines a resource and falls back to `url()`": "將資源內嵌，並回復為 'url()'", "media query expected": "必須有媒體查詢", "number expected": "必須是數字", "operator expected": "必須是運算子", "page directive or declaraton expected": "需要 page 指示詞或宣告", "parses a string to a color": "將字串剖析為色彩", "percentage expected": "必須有百分比", "property value expected": "預必須是屬性值", "remove or change the unit of a dimension": "移除或變更維度的單位", "return `@color` 10% points darker": "傳回 '@color' 較深 10% 點", "return `@color` 10% points less saturated": "傳回 '@color' 飽和度低 10%", "return `@color` 10% points less transparent": "傳回 '@color' 透明度低 10%", "return `@color` 10% points lighter": "傳回 '@color' 較淺 10% 點", "return `@color` 10% points more saturated": "傳回 '@color' 飽和度高 10%", "return `@color` 10% points more transparent": "傳回 '@color' 透明度高 10%", "return `@color` with 50% transparency": "傳回有 50% 透明度的 `@color`", "return `@color` with a 10 degree larger in hue": "傳回 '@color' 色調超過 10 度", "return `@darkcolor` if `@color1 is> 43% luma` otherwise return `@lightcolor`, see notes": "若 `@color1 is> 43% luma` 會傳回 `@darkcolor`，否則是傳回 `@lightcolor`，請查看記事", "return a mix of `@color1` and `@color2`": "傳回 `@color1` 和 `@color2` 的混合", "returns a grey, 100% desaturated color": "傳回灰色，100% 還原飽和色彩", "returns a value at the specified position in the list": "傳回清單中指定位置的值", "returns one of two values depending on a condition.": "根據條件傳回兩個值的其中一個。", "returns pi": "returns pi", "returns the `alpha` channel of `@color`": "傳回 `@color` 的 `alpha` 通道", "returns the `blue` channel of `@color`": "傳回 `@color` 的 `blue` 通道", "returns the `green` channel of `@color`": "傳回 '@color' 的 'green' 通道", "returns the `hue` channel of `@color` in the HSL space": "傳回 HSL 空間中 '@color' 的 'hue' 通道", "returns the `hue` channel of `@color` in the HSV space": "傳回 HSV 空間中 '@color' 的 'hue' 通道", "returns the `lightness` channel of `@color` in the HSL space": "傳回 HSL 空間中 `@color` 的 `lightness` 通道", "returns the `luma` value (perceptual brightness) of `@color`": "傳回 '@color' 的 'luma' 值 (感知亮度)", "returns the `red` channel of `@color`": "傳回 '@color' 的 'red' 通道", "returns the `saturation` channel of `@color` in the HSL space": "傳回 HSL 空間中 '@color' 的 `saturation` 通道", "returns the `saturation` channel of `@color` in the HSV space": "傳回 HSV 空間中 `@color` 的 `saturation` 通道", "returns the `value` channel of `@color` in the HSV space": "傳回 HSV 空間中 '@color' 的 `value` 通道", "returns the lowest of one or more values": "傳回一或多個值的最低值", "returns the number of elements in a value list": "傳回值清單中的元素數目", "rounds a number to a number of places": "將數字捨入到多個位置", "rounds down to an integer": "無條件捨去到整數", "rounds up to an integer": "無條件進位至整數", "selector expected": "必須有選取器", "semi-colon expected": "必須有冒號", "sine function": "正弦函數", "string literal expected": "必須是字串常值", "string replace": "字串取代", "tangent function": "正切函數", "term expected": "必須是字詞", "unknown keyword": "未知的關鍵字", "uri or string expected": "必須有 URI 或字串", "variable name expected": "必須是變數名稱", "variable value expected": "必須有變數值", "whitespace expected": "必須是空白字元", "wildcard expected": "必須有萬用字元。", "{ expected": "必須是 {", "{0}, '{1}'": "-{0}、'{1}'", "} expected": "必須是 }"}, "package": {"description": "提供豐富的 HTM 和 Handlebar 檔案語言支援", "displayName": "HTML 語言功能", "html.autoClosingTags": "啟用/停用 HTML 標籤的自動關閉功能。", "html.autoCreateQuotes": "啟用/停用 HTML 屬性指派的引號自動建立。引號的類型可以由 '#html.completion.attributeDefaultValue#' 來設定。", "html.completion.attributeDefaultValue": "控制接受完成時的屬性預設值。", "html.completion.attributeDefaultValue.doublequotes": "屬性值設定為 \"\"。", "html.completion.attributeDefaultValue.empty": "屬性值未設定。", "html.completion.attributeDefaultValue.singlequotes": "屬性值設定為 ''。", "html.customData.desc": "指向 JSON 檔案的相對檔案路徑清單，採用[自訂資料格式](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md)。\r\n\r\nVS Code 會在啟動時載入自訂資料，針對您在 JSON 檔案中所指定的自訂 HTML 標籤、屬性以及屬性值，增強其 HTML 支援。\r\n\r\n檔案路徑相對於工作區，而且僅會考慮工作區資料夾設定。", "html.format.contentUnformatted.desc": "逗點分隔的標籤清單，其中內容的格式不應重新設定。`null` 預設為 `pre` 標籤。", "html.format.enable.desc": "啟用/停用預設 HTML 格式器。", "html.format.extraLiners.desc": "標籤清單，逗號分隔，在它們之前應該一r加上額外的分行符號。`null` 預設為 `\"head, body, /html\"`。", "html.format.indentHandlebars.desc": "格式化並縮排 `{{#foo}}` 及 `{{/foo}}`。", "html.format.indentInnerHtml.desc": "將 `<head>` 和 `<body>` 區段縮排。", "html.format.maxPreserveNewLines.desc": "一個區塊要保留的最大分行符號數。使用 `null` 表示無限制。", "html.format.preserveNewLines.desc": "控制是否應保留元素前方原有的分行符號。僅適用於元素前方，而不適用於標籤內或文字。", "html.format.templating.desc": "遵守 django、erb、handlebars 和 php 範本化語言標籤。", "html.format.unformatted.desc": "不應重新格式化的逗號分隔標籤清單。`null` 預設為 https://www.w3.org/TR/html5/dom.html#phrasing-content 中列出的所有標籤。", "html.format.unformattedContentDelimiter.desc": "將文字內容一併保留在此字串之間。", "html.format.wrapAttributes.alignedmultiple": "在超過行長度時換行，並垂直對齊屬性。", "html.format.wrapAttributes.auto": "只在超過行的長度時將屬性換行。", "html.format.wrapAttributes.desc": "將屬性換行。", "html.format.wrapAttributes.force": "將第一個以外的每個屬性換行。", "html.format.wrapAttributes.forcealign": "將第一個以外的每個屬性換行，並保持對齊。", "html.format.wrapAttributes.forcemultiline": "將每個屬性換行。", "html.format.wrapAttributes.preserve": "保留屬性的換行。", "html.format.wrapAttributes.preservealigned": "保留屬性的換行但對齊。", "html.format.wrapAttributesIndentSize.desc": "將屬性縮排為 N 個字元後。使用 `null` 來使用預設縮排大小。如果 '#html.format.wrapAttributes#' 設定為 `aligned`，則會略過。", "html.format.wrapLineLength.desc": "每行的字元數上限 (0 = 停用)。", "html.hover.documentation": "在暫留時顯示標籤和屬性文件。", "html.hover.references": "在暫留時顯示 MDN 參考。", "html.mirrorCursorOnMatchingTag": "啟用/停用相符 HTML 標籤的鏡像游標。", "html.mirrorCursorOnMatchingTagDeprecationMessage": "因 `editor.linkedEditing` 而遭淘汰", "html.suggest.html5.desc": "控制內建 HTML 語言支援是否建議 HTML5 標籤、屬性和值。", "html.trace.server.desc": "追蹤 VS Code 與 HTML 語言伺服器之間的通訊。", "html.validate.scripts": "設定內建 HTML 語言支援是否會驗證內嵌指令碼。", "html.validate.styles": "設定內建 HTML 語言支援是否會驗證內嵌樣式。"}}}