"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineBuilder = void 0;
const vscode = require("vscode");
const builder_1 = require("../../compile/builder");
const onlineBuildTask_1 = require("./onlineBuildTask");
const dataSource = require("../../mvClient/dataSource");
const msg_1 = require("../../msg");
const extension_1 = require("../../extension");
const ol = require("../../config/onlineConfig");
class OnlineBuilder extends builder_1.Builder {
    constructor(_fsProvider) {
        super();
        this._fsProvider = _fsProvider;
        this._name = "Build";
        this._creator = new onlineBuildTask_1.OnlinebuildTaskCreator(this._name, this._source, _fsProvider);
    }
    compile(type, files, flavor) {
        let definition = (type === builder_1.Catalog.Do ? getCatalogDefinition(this._source, builder_1.Catalog.Do) : getDefinition(this._source));
        const task = this._creator.create(files, definition, false);
        vscode.tasks.executeTask(task);
    }
    getTask(definition) {
        let account = extension_1.online.getAccount();
        if (account === undefined || definition.targets === undefined || definition.targets.length <= 0) {
            return undefined;
        }
        if (!account.getIsConnect()) {
            vscode.window.showErrorMessage(msg_1.Msg.ACCOUNT_NOT_CONNECT);
            return undefined;
        }
        const files = this._extract(definition.targets);
        if (files == undefined || files.length === 0) {
            vscode.window.showErrorMessage(msg_1.Msg.FILE_NOT_BEEN_LOAD);
            return undefined;
        }
        else {
            return this._creator.create(files, definition, false);
        }
    }
    getDefaultTask() {
        var definition = getDefinition(this._source);
        return this._creator.create([], definition, false);
    }
    _extract(targetFiles) {
        const accountUri = extension_1.online.getAccountUri();
        if (accountUri !== undefined) {
            return targetFiles.map(target => vscode.Uri.joinPath(accountUri, target).path);
        }
    }
}
exports.OnlineBuilder = OnlineBuilder;
function getDefinition(source) {
    let definition;
    const params = ol.getCatalogParameters();
    if (dataSource.get() === "UNIVERSE") {
        definition = {
            type: source,
            targets: [],
            compile: {
                dataSource: "UNIVERSE",
                arguments: params.compile_arguments
            }
        };
    }
    else {
        definition = {
            type: source,
            targets: [],
            compile: {
                dataSource: "UNIDATA",
                language: params.ud_compile_flavor,
                arguments: params.compile_arguments
            }
        };
    }
    return definition;
}
function getCatalogDefinition(source, type) {
    let definition;
    const params = type === builder_1.Catalog.Do ? ol.getCatalogParameters() : {};
    const flavor = params.ud_compile_flavor || 'Unibasic';
    if (dataSource.get() === "UNIVERSE") {
        definition = {
            type: source,
            targets: [],
            compile: {
                dataSource: "UNIVERSE",
                catalog: params.catalog || '',
                initialCharacter: params.initialCharacter || '',
                arguments: params.catalog_arguments || ''
            }
        };
    }
    else {
        definition = {
            type: source,
            targets: [],
            compile: {
                dataSource: "UNIDATA",
                language: flavor,
                catalog: params.catalog || '',
                arguments: params.catalog_arguments || ''
            }
        };
    }
    return definition;
}
//# sourceMappingURL=onlineBuilder.js.map