"use strict";
/**
 *  builder - build / compile related functions
 *
 *  Rocket Software Confidential
 *  OCO Source Materials
 *  Copyright (C) Rocket Software, Inc.  2021 - 2023
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Builder = exports.Catalog = exports.Compile = void 0;
const vscode = require("vscode");
const provider_1 = require("./provider");
const extension_1 = require("../extension");
var Compile;
(function (Compile) {
    Compile[Compile["Test"] = 0] = "Test";
    Compile[Compile["Do"] = 1] = "Do"; // Compile and also generate objective code on server.
})(Compile || (exports.Compile = Compile = {}));
var Catalog;
(function (Catalog) {
    Catalog[Catalog["Do"] = 0] = "Do"; // Catalog and generate objective code on server
})(Catalog || (exports.Catalog = Catalog = {}));
class Builder {
    constructor() {
        this._name = "build";
        this._source = "BASIC";
    }
    compile(type, files, flavor) {
        const provider = provider_1.BasicBuildTaskProvider.getInstance();
        let task;
        if (type === Compile.Do) {
            if (flavor) {
                task = provider.getTaskForUdQuickCompile(files, flavor);
            }
            else {
                task = provider.getTask(files, true);
            }
        }
        else if (type === Catalog.Do) {
            task = provider.getTask(files, true, undefined, type);
        }
        if (task) {
            vscode.tasks.executeTask(task);
        }
    }
    run(type, flavor) {
        return __awaiter(this, void 0, void 0, function* () {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return Promise.resolve(undefined);
            }
            const uri = editor.document.uri;
            const pickFiles = yield extension_1.mvvs.getDirty().pick(uri);
            if (!pickFiles || pickFiles.length == 0) {
                return;
            }
            const files = extension_1.mvvs.getDirty().getFiles(pickFiles);
            if (type === Compile.Do) {
                this.compile(Compile.Do, files, flavor);
            }
            else if (type === Catalog.Do) {
                this.compile(Catalog.Do, files, flavor);
            }
            extension_1.mvvs.getDirty().refresh();
        });
    }
    compileSelect(items, flavor) {
        const fullFilePaths = [];
        items.forEach(item => {
            fullFilePaths.push(item.fsPath);
        });
        this.compile(Compile.Do, fullFilePaths, flavor);
    }
}
exports.Builder = Builder;
//# sourceMappingURL=builder.js.map