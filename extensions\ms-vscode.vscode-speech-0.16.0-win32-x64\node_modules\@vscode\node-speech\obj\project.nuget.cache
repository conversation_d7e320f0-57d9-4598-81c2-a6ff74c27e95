{"version": 2, "dgSpecHash": "pTWdmwsj/YQ=", "success": true, "projectFilePath": "D:\\a\\_work\\1\\s\\node_modules\\@vscode\\node-speech\\node-speech.csproj", "expectedPackageFiles": ["temp_packages\\azure.core\\1.44.1\\azure.core.1.44.1.nupkg.sha512", "temp_packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "temp_packages\\microsoft.cognitiveservices.speech\\1.44.0\\microsoft.cognitiveservices.speech.1.44.0.nupkg.sha512", "temp_packages\\microsoft.cognitiveservices.speech.extension.embedded.sr\\1.44.0\\microsoft.cognitiveservices.speech.extension.embedded.sr.1.44.0.nupkg.sha512", "temp_packages\\microsoft.cognitiveservices.speech.extension.embedded.tts\\1.44.0\\microsoft.cognitiveservices.speech.extension.embedded.tts.1.44.0.nupkg.sha512", "temp_packages\\microsoft.cognitiveservices.speech.extension.onnx.runtime\\1.44.0\\microsoft.cognitiveservices.speech.extension.onnx.runtime.1.44.0.nupkg.sha512", "temp_packages\\microsoft.cognitiveservices.speech.extension.telemetry\\1.44.0\\microsoft.cognitiveservices.speech.extension.telemetry.1.44.0.nupkg.sha512", "temp_packages\\system.clientmodel\\1.1.0\\system.clientmodel.1.1.0.nupkg.sha512", "temp_packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "temp_packages\\system.memory.data\\6.0.0\\system.memory.data.6.0.0.nupkg.sha512", "temp_packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "temp_packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "temp_packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "temp_packages\\system.text.json\\6.0.10\\system.text.json.6.0.10.nupkg.sha512", "temp_packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": []}