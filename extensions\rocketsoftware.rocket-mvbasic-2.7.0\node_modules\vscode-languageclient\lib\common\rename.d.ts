import { Disposable, TextDocument, ProviderResult, Range as VRange, Position as VPosition, WorkspaceEdit as VWorkspaceEdit, RenameProvider } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, RenameOptions, RenameRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient, DocumentSelectorOptions } from './features';
export interface ProvideRenameEditsSignature {
    (this: void, document: TextDocument, position: VPosition, newName: string, token: CancellationToken): ProviderResult<VWorkspaceEdit>;
}
export interface PrepareRenameSignature {
    (this: void, document: TextDocument, position: VPosition, token: CancellationToken): ProviderResult<VRange | {
        range: VRange;
        placeholder: string;
    }>;
}
export interface RenameMiddleware {
    provideRenameEdits?: (this: void, document: TextDocument, position: VPosition, newName: string, token: CancellationToken, next: ProvideRenameEditsSignature) => ProviderResult<VWorkspaceEdit>;
    prepareRename?: (this: void, document: TextDocument, position: VPosition, token: CancellationToken, next: PrepareRenameSignature) => ProviderResult<VRange | {
        range: VRange;
        placeholder: string;
    }>;
}
export declare class RenameFeature extends TextDocumentLanguageFeature<boolean | RenameOptions, RenameRegistrationOptions, RenameProvider, RenameMiddleware> {
    constructor(client: FeatureClient<RenameMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: RenameRegistrationOptions & DocumentSelectorOptions): [Disposable, RenameProvider];
    private registerProvider;
    private isDefaultBehavior;
}
