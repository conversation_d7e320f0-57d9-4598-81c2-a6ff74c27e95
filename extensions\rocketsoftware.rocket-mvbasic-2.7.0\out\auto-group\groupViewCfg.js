"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GroupViewConfig = void 0;
const extConfig = require("../config/extConfig");
class GroupViewConfig {
    constructor(workspace, config) {
        this.configFile = config;
        this.workspace = workspace;
        this.root = undefined;
    }
    init() {
        try {
            this.root = extConfig.read(this.workspace, this.configFile, extConfig.offlineMode);
            return true;
        }
        catch (e) {
            return false;
        }
    }
    defaultDemiliter() {
        try {
            return this.root.groupView.default.delimiter;
        }
        catch (e) {
            return ".";
        }
    }
    defaultLevel() {
        try {
            let level = this.root.groupView.default.level;
            if (level <= 0) {
                level = 2;
            }
            return level;
        }
        catch (e) {
            return 2;
        }
    }
    groups() {
        try {
            const result = this.root.groupView.groups;
            for (const item of result) {
                if (item.sourceDir !== undefined && item.sourceDir.length != 0) {
                    return result;
                }
            }
            return [];
        }
        catch (e) {
            return [];
        }
    }
    ignore() {
        try {
            return this.root.groupView.ignore;
        }
        catch (e) {
            return [];
        }
    }
    getSrc(group) {
        try {
            return group.sourceDir;
        }
        catch (e) {
            return undefined;
        }
    }
    getInclude(group) {
        try {
            return group.include;
        }
        catch (e) {
            return [];
        }
    }
    getExclude(group) {
        try {
            return group.exclude;
        }
        catch (e) {
            return [];
        }
    }
    getGroupName(group) {
        try {
            return group.groupName;
        }
        catch (e) {
            return undefined;
        }
    }
}
exports.GroupViewConfig = GroupViewConfig;
//# sourceMappingURL=groupViewCfg.js.map